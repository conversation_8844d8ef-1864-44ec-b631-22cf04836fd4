(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/providers/DataSourcesContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DataSourcesProvider": (()=>DataSourcesProvider),
    "useDataSources": (()=>useDataSources)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/ApiContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
;
const DataSourcesContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useDataSources = ()=>{
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(DataSourcesContext);
    if (!context) {
        throw new Error('useDataSources must be used within a DataSourcesProvider');
    }
    return context;
};
_s(useDataSources, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const DataSourcesProvider = ({ children })=>{
    _s1();
    const { listDatabases, disconnectExistingDatabase } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApi"])();
    const [dataSources, setDataSources] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Convert ConnectedDatabase to DataSource format
    const convertConnectedDatabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataSourcesProvider.useCallback[convertConnectedDatabase]": (db)=>({
                id: db.id,
                name: db.name,
                type: 'database',
                description: db.description || `${db.type} database`,
                isConnected: true
            })
    }["DataSourcesProvider.useCallback[convertConnectedDatabase]"], []);
    // Fetch connected databases
    const fetchConnectedDatabases = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataSourcesProvider.useCallback[fetchConnectedDatabases]": async ()=>{
            setIsLoading(true);
            setError(null);
            try {
                console.log('Fetching connected databases...');
                const connectedDbs = await listDatabases();
                const dataSources = connectedDbs.map(convertConnectedDatabase);
                setDataSources(dataSources);
                setIsInitialized(true);
            } catch (err) {
                console.error('Failed to fetch connected databases:', err);
                setError(err instanceof Error ? err.message : 'Failed to fetch connected databases');
                // Don't set mock data - let the UI handle empty state
                setDataSources([]);
                setIsInitialized(true);
            } finally{
                setIsLoading(false);
            }
        }
    }["DataSourcesProvider.useCallback[fetchConnectedDatabases]"], [
        listDatabases,
        convertConnectedDatabase
    ]);
    // Initialize data sources on mount - only once
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataSourcesProvider.useEffect": ()=>{
            if (!isInitialized) {
                fetchConnectedDatabases();
            }
        }
    }["DataSourcesProvider.useEffect"], [
        fetchConnectedDatabases,
        isInitialized
    ]);
    // Get only connected data sources (which should be all of them now)
    const connectedDataSources = dataSources.filter((source)=>source.isConnected);
    // Connect a data source (not implemented - would need connection parameters)
    const connectDataSource = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataSourcesProvider.useCallback[connectDataSource]": async (sourceId)=>{
            // This would require a connection form/modal to collect database credentials
            // For now, we'll just refresh the list
            console.log('Connect data source not implemented - use the Data Sources page');
            setError('Please use the Data Sources page to connect new databases');
        }
    }["DataSourcesProvider.useCallback[connectDataSource]"], []);
    // Disconnect a data source
    const disconnectDataSource = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataSourcesProvider.useCallback[disconnectDataSource]": async (sourceId)=>{
            setIsLoading(true);
            setError(null);
            try {
                // Use the real API to disconnect
                await disconnectExistingDatabase(sourceId);
                // Refresh the list
                await fetchConnectedDatabases();
            } catch (err) {
                setError(err instanceof Error ? err.message : 'Failed to disconnect data source');
                setIsLoading(false);
            }
        }
    }["DataSourcesProvider.useCallback[disconnectDataSource]"], [
        disconnectExistingDatabase,
        fetchConnectedDatabases
    ]);
    // Get a specific data source by ID
    const getDataSource = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataSourcesProvider.useCallback[getDataSource]": (sourceId)=>{
            return dataSources.find({
                "DataSourcesProvider.useCallback[getDataSource]": (source)=>source.id === sourceId
            }["DataSourcesProvider.useCallback[getDataSource]"]);
        }
    }["DataSourcesProvider.useCallback[getDataSource]"], [
        dataSources
    ]);
    // Manual refresh function
    const refreshDataSources = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataSourcesProvider.useCallback[refreshDataSources]": async ()=>{
            await fetchConnectedDatabases();
        }
    }["DataSourcesProvider.useCallback[refreshDataSources]"], [
        fetchConnectedDatabases
    ]);
    const value = {
        dataSources,
        connectedDataSources,
        isLoading,
        error,
        connectDataSource,
        disconnectDataSource,
        getDataSource,
        refreshDataSources
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DataSourcesContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/DataSourcesContext.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, this);
};
_s1(DataSourcesProvider, "CR/G0/fer09k9g7ljzcIQ84KO0M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApi"]
    ];
});
_c = DataSourcesProvider;
var _c;
__turbopack_context__.k.register(_c, "DataSourcesProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/(dashboard)/layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$DataSourcesContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/DataSourcesContext.tsx [app-client] (ecmascript)");
"use client";
;
;
const DashboardLayout = ({ children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$DataSourcesContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DataSourcesProvider"], {
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/(dashboard)/layout.tsx",
        lineNumber: 11,
        columnNumber: 5
    }, this);
};
_c = DashboardLayout;
const __TURBOPACK__default__export__ = DashboardLayout;
var _c;
__turbopack_context__.k.register(_c, "DashboardLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_79d77e8a._.js.map