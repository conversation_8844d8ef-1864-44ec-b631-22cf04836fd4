# Chart Query Performance Optimizations

## Overview

This document outlines the performance optimizations implemented to improve the chart/query endpoint response times. Based on analysis of the logs showing ~20 second response times, we've implemented several key optimizations that should reduce response times to under 5 seconds.

## Performance Issues Identified

### 1. Database Schema Discovery Bottleneck (Major - ~15-17 seconds)
- **Issue**: Every chart query triggered a full database schema discovery via `get_database_schema()`
- **Evidence**: Logs showed multiple database connections and extensive table metadata fetching
- **Impact**: 75-85% of total response time

### 2. Sequential Connection Pool Creation (~2-3 seconds)
- **Issue**: Connection pools created on-demand for each database
- **Evidence**: Multiple "Created connection pool" messages in logs
- **Impact**: 10-15% of total response time

### 3. Expensive Row Count Operations (~1-2 seconds)
- **Issue**: `SELECT COUNT(*)` queries executed for every table during metadata fetching
- **Evidence**: Code analysis showed row count queries for all tables
- **Impact**: Scales poorly with database size

### 4. Lack of Query Result Caching
- **Issue**: Identical chart queries regenerated data every time
- **Impact**: Unnecessary database queries and processing

## Optimizations Implemented

### 1. Aggressive Database Schema Caching

**Changes Made:**
- Implemented database-specific caching in `_get_database_info()`
- Cache key: `chart_db_schema:{user_id}:{database_id}`
- Cache TTL: 10 minutes (extended from default 5 minutes)
- Added `_get_single_database_schema()` method to fetch only required database

**Performance Impact:**
- **First request**: Same time (cache miss)
- **Subsequent requests**: ~90% reduction (cache hit)
- **Expected improvement**: 15-17 seconds → 1-2 seconds for cached requests

**Code Location:**
```python
# AgentReport/app/services/chart_service.py
async def _get_database_info(self, user_id: str, database_id: str)
```

### 2. Eliminated Expensive Row Count Operations

**Changes Made:**
- Removed `SELECT COUNT(*)` queries from table metadata fetching
- Set `table.row_count = None` instead of querying
- Applies to both SQL and MongoDB databases

**Performance Impact:**
- **Reduction**: 1-2 seconds per database
- **Scaling**: No longer degrades with table size
- **Trade-off**: Row counts not available (can be fetched on-demand if needed)

**Code Location:**
```python
# AgentReport/app/services/database_service.py
async def get_table_metadata()
```

### 3. Query Result Caching

**Changes Made:**
- Implemented intelligent caching for chart query results
- Cache key: `chart_query_result:{user_id}:{query_hash}`
- Cache TTL: 5 minutes
- Caches both data points and SQL query

**Performance Impact:**
- **First request**: Same time (cache miss)
- **Subsequent identical requests**: ~95% reduction (cache hit)
- **Expected improvement**: Full query time → <100ms for cached results

**Code Location:**
```python
# AgentReport/app/services/chart_service.py
async def _generate_real_chart_data_with_info()
```

### 4. Parallel Processing Optimization

**Changes Made:**
- Chart type recommendation and database info fetching now run in parallel
- Uses `asyncio.gather()` to execute tasks concurrently
- Added `_generate_real_chart_data_with_info()` method for pre-fetched database info

**Performance Impact:**
- **Reduction**: ~100-200ms (overlap of previously sequential operations)
- **Scaling**: Better utilization of async capabilities

**Code Location:**
```python
# AgentReport/app/services/chart_service.py
async def process_chart_query()
```

### 5. Optimized Connection Pool Management

**Changes Made:**
- Enhanced connection pool reuse logic
- Better validation of existing pools
- Reduced connection overhead

**Performance Impact:**
- **Reduction**: 1-2 seconds for connection establishment
- **Scaling**: Better reuse of existing connections

## Expected Performance Improvements

### Before Optimizations
- **Cold request** (no cache): ~20 seconds
- **Warm request** (same query): ~20 seconds
- **Bottlenecks**: Database schema discovery, row counts, sequential processing

### After Optimizations
- **Cold request** (no cache): ~3-5 seconds
- **Warm request** (cached schema): ~1-2 seconds  
- **Identical query** (cached result): ~100-500ms
- **Bottlenecks**: Mostly eliminated

### Performance Breakdown (Estimated)
| Operation | Before | After (Cold) | After (Warm) | After (Cached) |
|-----------|--------|--------------|--------------|----------------|
| Schema Discovery | 15-17s | 2-3s | 0.1s | 0.1s |
| Connection Setup | 2-3s | 1s | 0.1s | 0.1s |
| Row Counts | 1-2s | 0s | 0s | 0s |
| Query Execution | 1-2s | 1-2s | 1-2s | 0.1s |
| **Total** | **~20s** | **~4-6s** | **~1-2s** | **~0.3s** |

## Testing and Validation

### Performance Tests Created
1. **Unit Tests**: `tests/test_chart_performance.py`
   - Database schema caching validation
   - Query result caching validation
   - Parallel processing performance
   - Cache key generation consistency

2. **Benchmark Script**: `scripts/benchmark_chart_performance.py`
   - Real-world performance measurement
   - Multiple query testing
   - Statistical analysis of response times
   - Success rate monitoring

### Running Performance Tests

```bash
# Run unit tests
pytest tests/test_chart_performance.py -v

# Run benchmark (requires running server and auth token)
python scripts/benchmark_chart_performance.py \
  --token YOUR_AUTH_TOKEN \
  --database-id YOUR_DATABASE_ID \
  --iterations 5
```

## Monitoring and Metrics

### Key Metrics to Monitor
1. **Response Time**: Chart query endpoint response time
2. **Cache Hit Rate**: Percentage of requests served from cache
3. **Database Connection Time**: Time to establish database connections
4. **Query Execution Time**: Time for actual SQL query execution

### Log Messages to Watch
- `"Using cached database schema for {database_id}"`
- `"Using cached chart query result for user {user_id}"`
- `"Reusing connection pool for user {user_id}"`

## Future Optimization Opportunities

### 1. Background Schema Refresh
- Implement background jobs to refresh schema cache before expiration
- Reduce cache misses for frequently used databases

### 2. Query Plan Caching
- Cache SQL query generation for similar natural language queries
- Reduce LLM processing time for query generation

### 3. Connection Pool Warming
- Pre-warm connection pools for frequently accessed databases
- Reduce cold start times

### 4. Streaming Responses
- Implement streaming for large result sets
- Improve perceived performance for complex queries

### 5. Database-Specific Optimizations
- Implement database-specific query optimizations
- Use database-specific features for better performance

## Configuration Options

### Cache TTL Settings
```python
# Database schema cache: 10 minutes
SCHEMA_CACHE_TTL = 600

# Query result cache: 5 minutes  
QUERY_CACHE_TTL = 300
```

### Connection Pool Settings
```python
# Pool size per user
POOL_SIZE = 5
MAX_OVERFLOW = 10
POOL_TIMEOUT = 30
```

## Rollback Plan

If performance issues arise, optimizations can be disabled by:

1. **Disable Schema Caching**: Set cache TTL to 0
2. **Disable Query Caching**: Comment out cache checks
3. **Disable Parallel Processing**: Revert to sequential execution
4. **Re-enable Row Counts**: Uncomment row count queries if needed

## Conclusion

These optimizations address the major performance bottlenecks identified in the chart query endpoint. The expected improvements should reduce response times from ~20 seconds to 1-5 seconds depending on cache state, representing a 75-95% improvement in performance.

The optimizations maintain backward compatibility and can be individually disabled if issues arise. Regular monitoring of the key metrics will help ensure the optimizations are working as expected.
