module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/constants/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API-related constants
__turbopack_context__.s({
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "DEFAULT_HEADERS": (()=>DEFAULT_HEADERS),
    "ENVIRONMENT_CONFIG": (()=>ENVIRONMENT_CONFIG),
    "HTTP_STATUS": (()=>HTTP_STATUS),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "getApiBaseUrl": (()=>getApiBaseUrl),
    "getCurrentEnvironmentConfig": (()=>getCurrentEnvironmentConfig),
    "getRawApiBaseUrl": (()=>getRawApiBaseUrl),
    "validateEnvironment": (()=>validateEnvironment)
});
const API_ENDPOINTS = {
    // Auth endpoints
    AUTH: {
        LOGIN: '/auth/login',
        REGISTER: '/auth/register',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        ME: '/auth/me',
        GOOGLE: '/auth/google',
        COMPLETE_ONBOARDING: '/auth/onboarding/complete'
    },
    // Database endpoints
    DATABASES: {
        LIST: '/databases/listdatabases',
        CONNECT: '/databases/connectdatabase',
        DISCONNECT: '/databases/disconnectdatabase',
        SCHEMA: '/databases/schema'
    },
    // Query endpoints
    QUERY: {
        ASK: '/query/ask'
    },
    // Chat endpoints
    CHAT: {
        LIST: '/chat/list',
        HISTORY: '/chat/history',
        DELETE: '/chat/delete'
    },
    // Report endpoints
    REPORTS: {
        LIST: '/reports/list'
    },
    // Question endpoints
    ASK: {
        QUESTION: '/ask/question'
    },
    // Chart endpoints
    CHART: {
        QUERY: '/chart/query',
        TYPES: '/chart/types',
        VALIDATE: '/chart/validate',
        HEALTH: '/chart/health'
    },
    // Dashboard endpoints
    DASHBOARD: {
        LIST: '/dashboard/list',
        CREATE: '/dashboard/create',
        GET: '/dashboard/get',
        UPDATE: '/dashboard/update',
        DELETE: '/dashboard/delete'
    },
    // Analysis project endpoints
    ANALYSIS: {
        LIST: '/analysis/list',
        CREATE: '/analysis/create',
        GET: '/analysis/get',
        UPDATE: '/analysis/update',
        DELETE: '/analysis/delete',
        DATA: '/analysis/data',
        STEPS: '/analysis/steps',
        EXECUTE_STEP: '/analysis/execute-step',
        UPLOAD_FILE: '/analysis/upload-file',
        DELETE_FILE: '/analysis/delete-file'
    }
};
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500
};
const DEFAULT_HEADERS = {
    'Content-Type': 'application/json'
};
const ENVIRONMENT_CONFIG = {
    development: {
        defaultApiBase: 'http://localhost:8000',
        name: 'Development',
        enableDebugLogs: true
    },
    production: {
        defaultApiBase: 'https://agentreportbackend.vercel.app',
        name: 'Production',
        enableDebugLogs: false
    },
    staging: {
        defaultApiBase: 'https://staging-agentreportbackend.vercel.app',
        name: 'Staging',
        enableDebugLogs: true
    }
};
const validateEnvironment = ()=>{
    const nodeEnv = ("TURBOPACK compile-time value", "development");
    const apiBase = ("TURBOPACK compile-time value", "http://localhost:8000");
    // Check if NODE_ENV is valid
    if (nodeEnv && !Object.keys(ENVIRONMENT_CONFIG).includes(nodeEnv)) {
        console.warn(`⚠️  Unknown NODE_ENV: ${nodeEnv}. Expected: development, production, or staging`);
    }
    // Check if API base URL is set
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Validate URL format
        try {
            new URL(apiBase);
        } catch (error) {
            console.error(`❌ Invalid NEXT_PUBLIC_API_BASE URL: ${apiBase}`);
        }
    }
};
const getCurrentEnvironmentConfig = ()=>{
    const nodeEnv = ("TURBOPACK compile-time value", "development");
    return ENVIRONMENT_CONFIG[nodeEnv] || ENVIRONMENT_CONFIG.development;
};
const getApiBaseUrl = ()=>{
    // Validate environment on first call
    if ("TURBOPACK compile-time truthy", 1) {
        validateEnvironment();
    }
    let rawBaseUrl = ("TURBOPACK compile-time value", "http://localhost:8000") ?? '';
    const envConfig = getCurrentEnvironmentConfig();
    // Fallback to environment-specific defaults if not set
    if (!rawBaseUrl) {
        rawBaseUrl = envConfig.defaultApiBase;
        if (envConfig.enableDebugLogs) {
            console.log(`🔧 Using default ${envConfig.name} API URL: ${rawBaseUrl}`);
        }
    }
    // Remove trailing slashes and add /api
    const apiBaseUrl = `${rawBaseUrl.replace(/\/+$/, '')}/api`;
    // Log the API base URL in development/staging for debugging
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    return apiBaseUrl;
};
const getRawApiBaseUrl = ()=>{
    let rawBaseUrl = ("TURBOPACK compile-time value", "http://localhost:8000") ?? '';
    const envConfig = getCurrentEnvironmentConfig();
    if (!rawBaseUrl) {
        rawBaseUrl = envConfig.defaultApiBase;
    }
    return rawBaseUrl.replace(/\/+$/, '');
};
const STORAGE_KEYS = {
    ACCESS_TOKEN: 'accessToken',
    REFRESH_TOKEN: 'refreshToken',
    TOKEN_TYPE: 'tokenType',
    USER_ID: 'userId',
    EXPIRES_AT: 'expiresAt'
};
}}),
"[project]/src/lib/constants/routes.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Application route constants
__turbopack_context__.s({
    "DEFAULT_LOGIN_REDIRECT": (()=>DEFAULT_LOGIN_REDIRECT),
    "PROTECTED_ROUTES": (()=>PROTECTED_ROUTES),
    "PUBLIC_ROUTES": (()=>PUBLIC_ROUTES),
    "ROUTES": (()=>ROUTES)
});
const ROUTES = {
    // Public routes
    HOME: '/',
    LOGIN: '/login',
    REGISTER: '/register',
    // Auth routes
    AUTH: {
        CALLBACK: '/auth/callback'
    },
    // OAuth routes
    OAUTH: {
        CALLBACK: '/oauth/callback'
    },
    // Onboarding routes
    ONBOARDING: '/onboarding',
    // Protected routes
    DASHBOARD: '/dashboard',
    CHAT: '/chat',
    DATASOURCES: '/datasources',
    REPORTS: '/reports',
    // Dynamic routes
    CHAT_WITH_ID: (chatId)=>`/chat/${chatId}`
};
const PUBLIC_ROUTES = [
    ROUTES.HOME,
    ROUTES.LOGIN,
    ROUTES.REGISTER,
    ROUTES.AUTH.CALLBACK,
    ROUTES.OAUTH.CALLBACK,
    ROUTES.ONBOARDING
];
const PROTECTED_ROUTES = [
    ROUTES.DASHBOARD,
    ROUTES.CHAT,
    ROUTES.DATASOURCES,
    ROUTES.REPORTS
];
const DEFAULT_LOGIN_REDIRECT = ROUTES.CHAT;
}}),
"[project]/src/lib/constants/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Main constants exports
__turbopack_context__.s({
    "APP_CONFIG": (()=>APP_CONFIG),
    "CHAT_CONFIG": (()=>CHAT_CONFIG),
    "DATABASE_TYPES": (()=>DATABASE_TYPES),
    "FILE_CONFIG": (()=>FILE_CONFIG),
    "UI_CONFIG": (()=>UI_CONFIG)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/routes.ts [app-ssr] (ecmascript)");
;
;
const APP_CONFIG = {
    NAME: 'Agent Platform',
    DESCRIPTION: 'Agent Platform for database queries',
    VERSION: '1.0.0'
};
const UI_CONFIG = {
    SIDEBAR_WIDTH: 280,
    HEADER_HEIGHT: 64,
    MOBILE_BREAKPOINT: 768
};
const CHAT_CONFIG = {
    MAX_MESSAGE_LENGTH: 4000,
    DEFAULT_OUTPUT_FORMAT: 'excel',
    SESSION_ID_LENGTH: 36
};
const DATABASE_TYPES = {
    POSTGRESQL: 'POSTGRESQL',
    MONGODB: 'MONGODB',
    MYSQL: 'MYSQL',
    SQLITE: 'SQLITE'
};
const FILE_CONFIG = {
    MAX_FILE_SIZE: 10 * 1024 * 1024,
    ALLOWED_TYPES: [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv'
    ]
};
}}),
"[project]/src/lib/constants/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/routes.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/constants/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/providers/ApiContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiProvider": (()=>ApiProvider),
    "useApi": (()=>useApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/constants/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/api.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const ApiContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const ApiProvider = ({ children })=>{
    const queryDatabases = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Use FormData to match backend expectations
            const formData = new FormData();
            formData.append('query', request.query);
            formData.append('output_format', request.output_format || "excel");
            if (request.session_id) {
                formData.append('session_id', request.session_id);
            }
            if (request.target_databases) {
                formData.append('target_databases', JSON.stringify(request.target_databases));
            }
            if (request.target_tables) {
                formData.append('target_tables', JSON.stringify(request.target_tables));
            }
            if (request.target_columns) {
                formData.append('target_columns', JSON.stringify(request.target_columns));
            }
            // Add streaming support - this was the missing piece!
            if (request.enable_token_streaming) {
                formData.append('enable_token_streaming', 'true');
            }
            const headers = {
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/ask/question`, formData, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("API error (queryDatabases):", error);
            throw error;
        }
    }, []);
    const getDatabaseSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dbId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/databases/schema`, {
                db_id: dbId
            }, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error fetching database schema:", error);
            throw error;
        }
    }, []);
    const listDatabases = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                console.log('No access token found, skipping database list request');
                return [];
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/databases/listdatabases`, {}, {
                headers
            });
            return response.data.databases || response.data || [];
        } catch (error) {
            console.error("Error listing databases:", error);
            throw error;
        }
    }, []);
    const connectNewDatabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (params)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/databases/connectdatabase`, params, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error connecting new database:", error);
            throw error;
        }
    }, []);
    const disconnectExistingDatabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dbId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/databases/disconnectdatabase`, {
                db_id: dbId
            }, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error disconnecting database:", error);
            throw error;
        }
    }, []);
    const askQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (query, outputFormat, conversationHistory, targetDatabases, targetTables, targetColumns)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/query/ask`, {
                query,
                output_format: outputFormat,
                target_databases: targetDatabases,
                target_tables: targetTables,
                target_columns: targetColumns
            }, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error asking query:", error);
            throw error;
        }
    }, []);
    const loginUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (credentials)=>{
        try {
            const params = new URLSearchParams();
            params.append('username', credentials.username);
            params.append('password', credentials.password);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/login`, params, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });
            if (response.data.access_token) {
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN, response.data.access_token);
                console.log('Access token set in localStorage:', response.data.access_token);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].TOKEN_TYPE, response.data.token_type);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN, response.data.refresh_token);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID, response.data.user_id);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT, response.data.expires_at);
            }
            return response.data;
        } catch (error) {
            console.error("Error during login:", error);
            throw error;
        }
    }, []);
    const registerUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (credentials)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.REGISTER}`, {
                email: credentials.email,
                password: credentials.password,
                full_name: credentials.full_name
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error registering user:", error);
            throw error;
        }
    }, []);
    const refreshToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const refreshTokenValue = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
            if (!refreshTokenValue) {
                throw new Error('No refresh token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/refresh`, {
                refresh_token: refreshTokenValue
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (response.data.access_token) {
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN, response.data.access_token);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].TOKEN_TYPE, response.data.token_type);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN, response.data.refresh_token);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID, response.data.user_id);
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT, response.data.expires_at);
                console.log('Token refreshed successfully');
            }
            return response.data;
        } catch (error) {
            console.error("Error refreshing token:", error);
            // Clear stored tokens on refresh failure
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].TOKEN_TYPE);
            throw error;
        }
    }, []);
    const logoutUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (token) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/logout`, {}, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error("Error during logout:", error);
        // Continue with local cleanup even if backend call fails
        } finally{
            // Always clear local storage
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT);
            localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].TOKEN_TYPE);
            console.log('User logged out and tokens cleared');
        }
    }, []);
    const getUserProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/me`, {}, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error fetching user profile:", error);
            throw error;
        }
    }, []);
    const listUserChats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                console.log('No access token found, skipping chat list request');
                return [];
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const apiUrl = `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/chats/listchats`;
            console.log('🔗 Making request to:', apiUrl);
            console.log('🔑 Using Bearer token:', token.substring(0, 20) + '...');
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(apiUrl, {}, {
                headers
            });
            console.log('✅ List chats response received:', response.status);
            return response.data || [];
        } catch (error) {
            console.error("❌ Error listing user chats:", error);
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
                console.error("📍 Request URL:", error.config?.url);
                console.error("📋 Request headers:", error.config?.headers);
                if (error.response) {
                    console.error("🚨 Response status:", error.response.status);
                    console.error("🚨 Response data:", error.response.data);
                    console.error("🚨 Response headers:", error.response.headers);
                } else if (error.request) {
                    console.error("🌐 Network error - no response received:", error.request);
                }
            }
            throw error;
        }
    }, []);
    const getChatHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (sessionId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                console.log('No access token found, skipping chat history request');
                return [];
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const requestBody = {
                session_id: sessionId
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/chats/getchathistory`, requestBody, {
                headers
            });
            return response.data || [];
        } catch (error) {
            console.error("Error getting chat history:", error);
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
                console.error("Chat history error response:", error.response.data);
                console.error("Chat history error status:", error.response.status);
            }
            throw error;
        }
    }, []);
    const listUserReports = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request = {})=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                console.log('No access token found, skipping reports list request');
                return {
                    reports: [],
                    total_count: 0
                };
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/reports/listreports`, request, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error listing user reports:", error);
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
                console.error("List reports error response:", error.response.data);
                console.error("List reports error status:", error.response.status);
            }
            throw error;
        }
    }, []);
    const deleteChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (sessionId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/chats/deletechat`, {
                session_id: sessionId
            }, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error deleting chat:", error);
            throw error;
        }
    }, []);
    // Profile and Settings API methods
    const updateUserProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/profile`, data, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error updating user profile:", error);
            throw error;
        }
    }, []);
    const changePassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/change-password`, data, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error changing password:", error);
            throw error;
        }
    }, []);
    const saveEmailPreferences = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/email-preferences`, data, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error saving email preferences:", error);
            throw error;
        }
    }, []);
    const savePrivacySettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].put(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/privacy-settings`, data, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error saving privacy settings:", error);
            throw error;
        }
    }, []);
    const exportUserData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/export-data`, {}, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error exporting user data:", error);
            throw error;
        }
    }, []);
    const deleteAccount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}/auth/account`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Error deleting account:", error);
            throw error;
        }
    }, []);
    const completeOnboarding = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Check if we're in development mode and the endpoint doesn't exist
            const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.COMPLETE_ONBOARDING}`, {}, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                return response.data;
            } catch (error) {
                // If it's a 404 error and we're in development, mock the response
                if (error.response?.status === 404 && isDevelopment) {
                    console.warn(`⚠️ Backend endpoint ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].AUTH.COMPLETE_ONBOARDING} not implemented yet. Using mock response for development.`);
                    // Simulate a successful response
                    await new Promise((resolve)=>setTimeout(resolve, 1000)); // Simulate network delay
                    return {
                        success: true,
                        message: 'Onboarding completed successfully (mocked)',
                        user: {
                            is_new_user: false,
                            onboarding_completed_at: new Date().toISOString()
                        }
                    };
                }
                // Re-throw the error if it's not a 404 or not in development
                throw error;
            }
        } catch (error) {
            console.error("Error completing onboarding:", error);
            throw error;
        }
    }, []);
    const queryChart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].CHART.QUERY}`, request, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error querying chart:", error);
            throw error;
        }
    }, []);
    const getChartTypes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].CHART.TYPES}`, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error getting chart types:", error);
            throw error;
        }
    }, []);
    const validateChartQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].CHART.VALIDATE}`, request, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error validating chart query:", error);
            throw error;
        }
    }, []);
    const getChartHealth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getApiBaseUrl"])()}${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].CHART.HEALTH}`, {
                headers
            });
            return response.data;
        } catch (error) {
            console.error("Error getting chart health:", error);
            throw error;
        }
    }, []);
    // Dashboard management methods
    const listDashboards = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Return an empty list in development until the user creates dashboards
            const mockDashboards = [];
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: mockDashboards
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.LIST}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error listing dashboards:", error);
            throw error;
        }
    }, []);
    const createDashboard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const newDashboard = {
                id: `dashboard-${Date.now()}`,
                name: request.name,
                description: request.description,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                user_id: 'user-1'
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: newDashboard
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.CREATE}`, request, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error creating dashboard:", error);
            throw error;
        }
    }, []);
    const getDashboard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dashboardId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const mockDashboard = {
                id: dashboardId,
                name: 'Sample Dashboard',
                description: 'A sample dashboard with charts',
                created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                user_id: 'user-1',
                widgets: []
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: mockDashboard
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.GET}/${dashboardId}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error getting dashboard:", error);
            throw error;
        }
    }, []);
    const updateDashboard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dashboardId, request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const updatedDashboard = {
                id: dashboardId,
                name: request.name || 'Updated Dashboard',
                description: request.description || 'Updated description',
                created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                updated_at: new Date().toISOString(),
                user_id: 'user-1'
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: updatedDashboard
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.put(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.UPDATE}/${dashboardId}`, request, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error updating dashboard:", error);
            throw error;
        }
    }, []);
    const deleteDashboard = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (dashboardId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                message: 'Dashboard deleted successfully'
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.DELETE}/${dashboardId}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error deleting dashboard:", error);
            throw error;
        }
    }, []);
    // Analysis Project Management Methods
    const listAnalysisProjects = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const mockProjects = [
                {
                    id: 'analysis-1',
                    name: 'Employee Performance Analysis',
                    description: 'Analyzing employee performance metrics, salary distributions, and department insights',
                    status: 'completed',
                    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    dataSource: 'HR Database',
                    stepCount: 3,
                    userId: 'user-1'
                },
                {
                    id: 'analysis-2',
                    name: 'Customer Behavior Study',
                    description: 'Understanding customer purchasing patterns and churn prediction',
                    status: 'running',
                    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                    dataSource: 'Sales Database',
                    stepCount: 4,
                    progress: 67,
                    userId: 'user-1'
                },
                {
                    id: 'analysis-3',
                    name: 'Market Trend Forecasting',
                    description: 'Forecasting market trends and demand patterns for Q4',
                    status: 'draft',
                    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    dataSource: 'Market Data API',
                    stepCount: 0,
                    userId: 'user-1'
                }
            ];
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: {
                    projects: mockProjects,
                    totalCount: mockProjects.length
                }
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.LIST}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error listing analysis projects:", error);
            throw error;
        }
    }, []);
    const createAnalysisProject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const newProject = {
                id: `analysis-${Date.now()}`,
                name: request.name,
                description: request.description || '',
                status: 'draft',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                dataSource: request.dataSource,
                stepCount: 0,
                userId: 'user-1'
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: newProject
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.CREATE}`, request, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error creating analysis project:", error);
            throw error;
        }
    }, []);
    const getAnalysisProject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const mockProject = {
                id: projectId,
                name: 'Employee Performance Analysis',
                description: 'Analyzing employee performance metrics, salary distributions, and department insights',
                status: 'completed',
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                dataSource: 'HR Database',
                stepCount: 3,
                userId: 'user-1'
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: mockProject
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.GET}/${projectId}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error getting analysis project:", error);
            throw error;
        }
    }, []);
    const updateAnalysisProject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId, request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const updatedProject = {
                id: projectId,
                name: request.name || 'Updated Analysis Project',
                description: request.description || 'Updated description',
                status: request.status || 'draft',
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                updatedAt: new Date().toISOString(),
                dataSource: 'HR Database',
                stepCount: 3,
                userId: 'user-1'
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: updatedProject
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.put(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.UPDATE}/${projectId}`, request, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error updating analysis project:", error);
            throw error;
        }
    }, []);
    const deleteAnalysisProject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                message: 'Analysis project deleted successfully'
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.DELETE}/${projectId}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error deleting analysis project:", error);
            throw error;
        }
    }, []);
    const getProjectData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId, options = {})=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Return empty data initially - data will be populated via file upload
            const emptyData = {
                rows: [],
                columns: [],
                totalRows: 0,
                schema: {}
            };
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 300 + 100));
            return {
                success: true,
                data: emptyData
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const params = new URLSearchParams();
        // if (options.stepId) params.append('stepId', options.stepId);
        // if (options.page) params.append('page', options.page.toString());
        // if (options.pageSize) params.append('pageSize', options.pageSize.toString());
        // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.DATA}/${projectId}?${params}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error getting project data:", error);
            throw error;
        }
    }, []);
    const getProjectSteps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Mock response for development
            const mockSteps = [
                {
                    id: 'step-1',
                    projectId,
                    title: 'Data Loading',
                    status: 'completed',
                    type: 'data_loading',
                    order: 1,
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
                    outputs: {
                        summary: "Successfully loaded 5 employee records from the connected database.",
                        code: `import pandas as pd\nimport numpy as np\n\n# Load employee data from connected database\ndf = pd.read_sql_query("""\n    SELECT id, name, age, department, salary, performance_rating as performance\n    FROM employees \n    WHERE active = true\n""", connection)\n\nprint(f"Loaded {len(df)} employee records")\ndf.head()`
                    }
                },
                {
                    id: 'step-2',
                    projectId,
                    title: 'Data Analysis',
                    status: 'completed',
                    type: 'analysis',
                    order: 2,
                    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                    outputs: {
                        summary: "Analyzed salary distributions and identified 2 high-performing employees.",
                        code: `# Analyze salary distribution by department\ndept_stats = df.groupby('department').agg({\n    'salary': ['mean', 'median', 'std'],\n    'performance': 'mean',\n    'age': 'mean'\n}).round(2)\n\nprint("Department Statistics:")\nprint(dept_stats)\n\n# Identify high performers\nhigh_performers = df[df['performance'] >= 4.5]\nprint(f"\\nHigh performers ({len(high_performers)} employees):")\nprint(high_performers[['name', 'department', 'performance']])`
                    }
                },
                {
                    id: 'step-3',
                    projectId,
                    title: 'Visualization',
                    status: 'completed',
                    type: 'visualization',
                    order: 3,
                    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
                    outputs: {
                        summary: "Created scatter plot showing relationship between salary and performance across departments.",
                        code: `import matplotlib.pyplot as plt\nimport seaborn as sns\n\n# Create scatter plot of salary vs performance\nplt.figure(figsize=(10, 6))\nsns.scatterplot(data=df, x='performance', y='salary', hue='department', s=100)\nplt.title('Salary vs Performance by Department')\nplt.xlabel('Performance Rating')\nplt.ylabel('Salary ($)')\nplt.legend(title='Department')\nplt.tight_layout()\nplt.show()`,
                        visualization: {
                            type: 'chart',
                            config: {
                                title: 'Salary vs Performance by Department',
                                xAxis: 'performance',
                                yAxis: 'salary',
                                groupBy: 'department'
                            },
                            data: [
                                {
                                    x: 4.2,
                                    y: 85000,
                                    group: 'Engineering',
                                    name: 'John Doe'
                                },
                                {
                                    x: 4.8,
                                    y: 65000,
                                    group: 'Marketing',
                                    name: 'Jane Smith'
                                },
                                {
                                    x: 4.1,
                                    y: 92000,
                                    group: 'Engineering',
                                    name: 'Mike Johnson'
                                },
                                {
                                    x: 4.6,
                                    y: 70000,
                                    group: 'Design',
                                    name: 'Sarah Wilson'
                                },
                                {
                                    x: 3.9,
                                    y: 98000,
                                    group: 'Engineering',
                                    name: 'David Brown'
                                }
                            ]
                        }
                    }
                }
            ];
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 500 + 200));
            return {
                success: true,
                data: mockSteps
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.STEPS}/${projectId}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error getting project steps:", error);
            throw error;
        }
    }, []);
    const executeStep = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId, stepId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, Math.random() * 2000 + 1000));
            return {
                success: true,
                message: 'Step execution completed successfully'
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.EXECUTE_STEP}/${projectId}/${stepId}`, {}, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error executing step:", error);
            throw error;
        }
    }, []);
    // File Upload Methods
    const uploadProjectFile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (request)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            const { file, projectId, onProgress } = request;
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('file', file);
            formData.append('projectId', projectId);
            // For now, simulate file processing and return mock data based on file type
            if (onProgress) {
                // Simulate upload progress
                for(let i = 0; i <= 100; i += 10){
                    await new Promise((resolve)=>setTimeout(resolve, 100));
                    onProgress({
                        loaded: file.size * i / 100,
                        total: file.size,
                        percentage: i
                    });
                }
            }
            // Mock response based on file type
            let mockPreviewData = {
                rows: [],
                columns: [],
                totalRows: 0,
                schema: {}
            };
            if (file.name.toLowerCase().includes('csv') || file.name.toLowerCase().includes('excel')) {
                // Simulate parsing CSV/Excel data
                mockPreviewData = {
                    rows: [
                        {
                            id: 1,
                            name: 'Alice Johnson',
                            age: 28,
                            salary: 75000,
                            department: 'Engineering'
                        },
                        {
                            id: 2,
                            name: 'Bob Smith',
                            age: 34,
                            salary: 82000,
                            department: 'Marketing'
                        },
                        {
                            id: 3,
                            name: 'Carol White',
                            age: 29,
                            salary: 78000,
                            department: 'Design'
                        },
                        {
                            id: 4,
                            name: 'David Brown',
                            age: 31,
                            salary: 85000,
                            department: 'Engineering'
                        },
                        {
                            id: 5,
                            name: 'Emma Davis',
                            age: 26,
                            salary: 72000,
                            department: 'Sales'
                        }
                    ],
                    columns: [
                        {
                            name: 'id',
                            type: 'number',
                            nullable: false
                        },
                        {
                            name: 'name',
                            type: 'string',
                            nullable: false
                        },
                        {
                            name: 'age',
                            type: 'number',
                            nullable: false
                        },
                        {
                            name: 'salary',
                            type: 'number',
                            nullable: false
                        },
                        {
                            name: 'department',
                            type: 'string',
                            nullable: false
                        }
                    ],
                    totalRows: 5,
                    schema: {
                        id: 'INTEGER',
                        name: 'VARCHAR(255)',
                        age: 'INTEGER',
                        salary: 'INTEGER',
                        department: 'VARCHAR(100)'
                    }
                };
            }
            // Simulate final processing delay
            await new Promise((resolve)=>setTimeout(resolve, 500));
            return {
                success: true,
                data: {
                    fileId: `file-${Date.now()}`,
                    filename: file.name,
                    rowCount: mockPreviewData.totalRows,
                    columnCount: mockPreviewData.columns.length,
                    previewData: mockPreviewData
                }
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Authorization': `Bearer ${token}`,
        //   // Don't set Content-Type for FormData, let browser set it with boundary
        // };
        // 
        // const response = await axios.post(
        //   `${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.UPLOAD_FILE}`, 
        //   formData, 
        //   { 
        //     headers,
        //     onUploadProgress: (progressEvent) => {
        //       if (onProgress && progressEvent.total) {
        //         onProgress({
        //           loaded: progressEvent.loaded,
        //           total: progressEvent.total,
        //           percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)
        //         });
        //       }
        //     }
        //   }
        // );
        // return response.data;
        } catch (error) {
            console.error("Error uploading file:", error);
            throw error;
        }
    }, []);
    const deleteProjectFile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (projectId, fileId)=>{
        try {
            const token = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
            if (!token) {
                throw new Error('No authentication token found');
            }
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return {
                success: true,
                message: 'File deleted successfully'
            };
        // Uncomment when backend is ready:
        // const headers: Record<string, string> = {
        //   'Content-Type': 'application/json',
        //   'Authorization': `Bearer ${token}`,
        // };
        // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.DELETE_FILE}/${projectId}/${fileId}`, { headers });
        // return response.data;
        } catch (error) {
            console.error("Error deleting file:", error);
            throw error;
        }
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ApiContext.Provider, {
        value: {
            queryDatabases,
            getDatabaseSchema,
            listDatabases,
            connectNewDatabase,
            disconnectExistingDatabase,
            askQuery,
            loginUser,
            registerUser,
            refreshToken,
            logoutUser,
            getUserProfile,
            updateUserProfile,
            changePassword,
            saveEmailPreferences,
            savePrivacySettings,
            exportUserData,
            deleteAccount,
            listUserChats,
            getChatHistory,
            listUserReports,
            deleteChat,
            completeOnboarding,
            queryChart,
            getChartTypes,
            validateChartQuery,
            getChartHealth,
            listDashboards,
            createDashboard,
            getDashboard,
            updateDashboard,
            deleteDashboard,
            listAnalysisProjects,
            createAnalysisProject,
            getAnalysisProject,
            updateAnalysisProject,
            deleteAnalysisProject,
            getProjectData,
            getProjectSteps,
            executeStep,
            uploadProjectFile,
            deleteProjectFile
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/ApiContext.tsx",
        lineNumber: 1435,
        columnNumber: 5
    }, this);
};
const useApi = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ApiContext);
    if (context === undefined) {
        throw new Error('useApi must be used within an ApiProvider');
    }
    return context;
};
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/utils/auth-error-handling.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Enhanced error handling utilities for authentication and onboarding
__turbopack_context__.s({
    "DEFAULT_RETRY_CONFIG": (()=>DEFAULT_RETRY_CONFIG),
    "categorizeAuthError": (()=>categorizeAuthError),
    "isRedirectLoopError": (()=>isRedirectLoopError),
    "logAuthError": (()=>logAuthError),
    "normalizeAuthResponse": (()=>normalizeAuthResponse),
    "retryWithBackoff": (()=>retryWithBackoff),
    "safeApiCall": (()=>safeApiCall),
    "validateAuthResponse": (()=>validateAuthResponse)
});
const DEFAULT_RETRY_CONFIG = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2
};
async function retryWithBackoff(operation, config = DEFAULT_RETRY_CONFIG, operationName = 'operation') {
    let lastError;
    for(let attempt = 0; attempt <= config.maxRetries; attempt++){
        try {
            console.log(`Attempting ${operationName} (attempt ${attempt + 1}/${config.maxRetries + 1})`);
            const result = await operation();
            if (attempt > 0) {
                console.log(`${operationName} succeeded after ${attempt + 1} attempts`);
            }
            return result;
        } catch (error) {
            lastError = error;
            console.warn(`${operationName} failed on attempt ${attempt + 1}:`, error);
            // Don't retry on the last attempt
            if (attempt === config.maxRetries) {
                break;
            }
            // Calculate delay with exponential backoff
            const delay = Math.min(config.baseDelay * Math.pow(config.backoffFactor, attempt), config.maxDelay);
            console.log(`Retrying ${operationName} in ${delay}ms...`);
            await new Promise((resolve)=>setTimeout(resolve, delay));
        }
    }
    console.error(`${operationName} failed after ${config.maxRetries + 1} attempts`);
    throw lastError;
}
function categorizeAuthError(error) {
    const timestamp = new Date().toISOString();
    // Network errors
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('network') || error.message?.includes('fetch')) {
        return {
            code: 'NETWORK_ERROR',
            message: error.message || 'Network error occurred',
            retryable: true,
            userMessage: 'Network connection issue. Please check your internet connection and try again.',
            timestamp
        };
    }
    // Token errors
    if (error.response?.status === 401 || error.message?.includes('token') || error.message?.includes('unauthorized')) {
        return {
            code: 'TOKEN_ERROR',
            message: error.message || 'Authentication token invalid',
            retryable: false,
            userMessage: 'Your session has expired. Please log in again.',
            timestamp
        };
    }
    // Server errors (5xx)
    if (error.response?.status >= 500) {
        return {
            code: 'SERVER_ERROR',
            message: error.message || 'Server error occurred',
            retryable: true,
            userMessage: 'Server is temporarily unavailable. Please try again in a moment.',
            timestamp
        };
    }
    // Email already exists (409 Conflict)
    if (error.response?.status === 409) {
        const errorData = error.response?.data?.error;
        if (errorData?.code === 'EMAIL_ALREADY_EXISTS') {
            return {
                code: 'EMAIL_ALREADY_EXISTS',
                message: errorData.message || 'Email already exists',
                retryable: false,
                userMessage: errorData.message || 'An account with this email address already exists. Please use a different email or try logging in.',
                timestamp
            };
        }
        // Generic 409 conflict
        return {
            code: 'CONFLICT_ERROR',
            message: error.response?.data?.error?.message || error.message || 'Conflict error occurred',
            retryable: false,
            userMessage: error.response?.data?.error?.message || 'There was a conflict with your request. Please try again.',
            timestamp
        };
    }
    // Client errors (4xx)
    if (error.response?.status >= 400 && error.response?.status < 500) {
        // Check if there's a structured error response
        const errorData = error.response?.data?.error;
        if (errorData?.message) {
            return {
                code: errorData.code || 'CLIENT_ERROR',
                message: errorData.message,
                retryable: false,
                userMessage: errorData.message,
                timestamp
            };
        }
        return {
            code: 'CLIENT_ERROR',
            message: error.message || 'Client error occurred',
            retryable: false,
            userMessage: 'There was an issue with your request. Please try again or contact support.',
            timestamp
        };
    }
    // Onboarding specific errors
    if (error.message?.includes('onboarding') || error.message?.includes('is_new_user')) {
        return {
            code: 'ONBOARDING_ERROR',
            message: error.message || 'Onboarding error occurred',
            retryable: true,
            userMessage: 'There was an issue completing your setup. Please try again.',
            timestamp
        };
    }
    // Generic error
    return {
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unknown error occurred',
        retryable: true,
        userMessage: 'An unexpected error occurred. Please try again.',
        timestamp
    };
}
async function safeApiCall(apiCall, operationName, retryConfig) {
    try {
        const data = await retryWithBackoff(apiCall, {
            ...DEFAULT_RETRY_CONFIG,
            ...retryConfig
        }, operationName);
        return {
            data
        };
    } catch (error) {
        const authError = categorizeAuthError(error);
        console.error(`Safe API call failed for ${operationName}:`, authError);
        return {
            error: authError
        };
    }
}
function validateAuthResponse(response) {
    if (!response || typeof response !== 'object') {
        return false;
    }
    // Required fields
    const requiredFields = [
        'access_token',
        'user_id'
    ];
    for (const field of requiredFields){
        if (!response[field] || typeof response[field] !== 'string') {
            return false;
        }
    }
    // Optional but validated fields
    if (response.is_new_user !== undefined && typeof response.is_new_user !== 'boolean') {
        return false;
    }
    return true;
}
function normalizeAuthResponse(response) {
    if (!validateAuthResponse(response)) {
        throw new Error('Invalid authentication response structure');
    }
    return {
        ...response,
        // Default is_new_user to false if not provided (for legacy users)
        is_new_user: response.is_new_user ?? false,
        // Ensure token_type is set
        token_type: response.token_type || 'bearer'
    };
}
function isRedirectLoopError(error, redirectHistory) {
    // Check if we've been redirecting between the same routes repeatedly
    if (redirectHistory.length >= 6) {
        const recent = redirectHistory.slice(-6);
        const uniqueRoutes = new Set(recent);
        // If we only have 2-3 unique routes in recent history, it's likely a loop
        return uniqueRoutes.size <= 3;
    }
    return false;
}
function logAuthError(error, context) {
    const logData = {
        context,
        code: error.code,
        message: error.message,
        timestamp: error.timestamp,
        retryable: error.retryable,
        userAgent: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'server',
        url: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'unknown'
    };
    // In production, you might want to send this to an error tracking service
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        console.error('Auth Error:', logData);
    }
}
}}),
"[project]/src/providers/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/ApiContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/constants/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/api.ts [app-ssr] (ecmascript)");
// import { getRedirectPath } from '@/lib/utils/onboarding';
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/auth-error-handling.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false); // Changed to false - no automatic loading
    const [isNewUser, setIsNewUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastStateCheck, setLastStateCheck] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(Date.now());
    const [isCompletingOnboarding, setIsCompletingOnboarding] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasAttemptedAutoAuth, setHasAttemptedAutoAuth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { loginUser, registerUser, refreshToken: refreshTokenApi, logoutUser, getUserProfile, completeOnboarding: completeOnboardingApi } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApi"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // const pathname = usePathname();
    // Refresh user state from backend with enhanced error handling
    const refreshUserState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!user) return;
        const { data: profile, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["safeApiCall"])(()=>getUserProfile(), 'refresh user state', {
            maxRetries: 2
        } // Fewer retries for background operations
        );
        if (profile) {
            setIsNewUser(profile.is_new_user || false);
            setLastStateCheck(Date.now());
            console.log('User state refreshed, is_new_user:', profile.is_new_user);
        } else if (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logAuthError"])(error, 'refreshUserState');
        // Don't update state on error to avoid disrupting user experience
        // But log the error for debugging
        }
    }, [
        user,
        getUserProfile,
        setIsNewUser,
        setLastStateCheck
    ]);
    // Check if state refresh is needed (every 5 minutes)
    const shouldRefreshState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const fiveMinutes = 5 * 60 * 1000;
        return Date.now() - lastStateCheck > fiveMinutes;
    }, [
        lastStateCheck
    ]);
    // Visibility change handler for tab focus
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleVisibilityChange = async ()=>{
            if (!document.hidden && user && shouldRefreshState()) {
                console.log('Tab became visible, refreshing user state...');
                await refreshUserState();
            }
        };
        document.addEventListener('visibilitychange', handleVisibilityChange);
        return ()=>document.removeEventListener('visibilitychange', handleVisibilityChange);
    }, [
        user,
        shouldRefreshState,
        refreshUserState
    ]);
    // Periodic state revalidation (every 10 minutes)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!user) return;
        const interval = setInterval(async ()=>{
            if (shouldRefreshState()) {
                console.log('Periodic state refresh triggered...');
                await refreshUserState();
            }
        }, 10 * 60 * 1000); // 10 minutes
        return ()=>clearInterval(interval);
    }, [
        user,
        shouldRefreshState,
        refreshUserState
    ]);
    // Manual authentication initialization - only called when user explicitly signs in
    const initializeAuthFromStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        console.log('Manually initializing authentication from storage...');
        setIsLoading(true);
        const accessToken = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
        const userId = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID);
        const refreshToken = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
        const expiresAt = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT);
        if (accessToken && userId && refreshToken && expiresAt) {
            try {
                // Prepare token data structure and set it so that other components can access it while we validate.
                const tokenData = {
                    access_token: accessToken,
                    user_id: userId,
                    refresh_token: refreshToken,
                    expires_at: expiresAt,
                    token_type: 'bearer'
                };
                // Optimistically set the user so downstream hooks/components can read it while we validate.
                setUser(tokenData);
                // Validate access token by attempting to fetch the user profile.
                const profile = await getUserProfile();
                setIsNewUser(profile.is_new_user || false);
                setLastStateCheck(Date.now());
                console.log('User profile fetched, is_new_user:', profile.is_new_user);
                // 🔄 If the access token is going to expire in the next 5 minutes, refresh it proactively.
                try {
                    const expirationTime = new Date(expiresAt).getTime();
                    const bufferTime = 5 * 60 * 1000; // 5 minutes
                    if (Date.now() > expirationTime - bufferTime) {
                        console.log('Access token nearing expiry. Refreshing…');
                        const newTokenData = await refreshTokenApi();
                        setUser(newTokenData);
                    }
                } catch (refreshCheckError) {
                    console.warn('Proactive refresh check failed:', refreshCheckError);
                }
                setIsLoading(false);
                return true; // ✅ Stored token is still valid (and refreshed if necessary)
            } catch (error) {
                if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 401) {
                    // Stored access token is no longer valid – try to refresh it.
                    console.warn('Access token invalid (401). Attempting refresh…');
                    try {
                        const newTokenData = await refreshTokenApi();
                        console.log('Token refresh successful after 401');
                        setUser(newTokenData);
                        const refreshedProfile = await getUserProfile();
                        setIsNewUser(refreshedProfile.is_new_user || false);
                        setLastStateCheck(Date.now());
                        setIsLoading(false);
                        return true;
                    } catch (refreshError) {
                        console.error('Token refresh failed after 401:', refreshError);
                    }
                } else {
                    console.error('Failed to fetch user profile during initialization:', error);
                }
                // At this point we either couldn't refresh or had another error – clear auth data.
                localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
                localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
                localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID);
                localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT);
                setUser(null);
                setIsLoading(false);
                return false;
            }
        } else {
            console.log('No complete auth data found in localStorage');
            setIsLoading(false);
            return false;
        }
    }, [
        refreshTokenApi,
        getUserProfile
    ]);
    // Check if user has valid stored authentication on app load (but don't auto-authenticate)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!hasAttemptedAutoAuth) {
            setHasAttemptedAutoAuth(true);
            const hasStoredAuth = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN) && localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
            if (hasStoredAuth) {
                console.log('Found stored auth data, but not auto-authenticating. User must manually sign in.');
            }
        }
    }, [
        hasAttemptedAutoAuth
    ]);
    // Disabled automatic redirects - users must manually navigate
    // This allows the landing page to be truly public
    // Protected routes will handle their own authentication checks
    const login = async (credentials)=>{
        const { data, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["safeApiCall"])(()=>loginUser(credentials), 'user login');
        if (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logAuthError"])(error, 'login');
            throw new Error(error.userMessage);
        }
        if (data) {
            const normalizedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeAuthResponse"])(data);
            setUser(normalizedData);
            setIsNewUser(normalizedData.is_new_user);
            setLastStateCheck(Date.now());
            // Redirect based on user status
            if (normalizedData.is_new_user) {
                router.push('/onboarding');
            } else {
                router.push('/chat');
            }
        }
    };
    // Manual sign in function that checks stored tokens first
    const signIn = async (credentials, shouldRedirect = true)=>{
        // First try to authenticate from stored tokens
        const hasValidStoredAuth = await initializeAuthFromStorage();
        if (hasValidStoredAuth) {
            console.log('Authenticated from stored tokens');
            // Only redirect if explicitly requested (for manual sign-in from landing page)
            if (shouldRedirect) {
                if (isNewUser) {
                    router.push('/onboarding');
                } else {
                    router.push('/chat');
                }
            }
            return;
        }
        // If no valid stored auth and credentials provided, perform login
        if (credentials) {
            await login(credentials);
        } else if (shouldRedirect) {
            // No stored auth and no credentials - redirect to login page only if redirect is requested
            router.push('/login');
        } else {
            // For ProtectedRoute scenarios, throw error to indicate authentication failed
            throw new Error('No valid authentication found');
        }
    };
    const register = async (credentials)=>{
        const { data, error } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["safeApiCall"])(()=>registerUser(credentials), 'user registration');
        if (error) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logAuthError"])(error, 'register');
            throw new Error(error.userMessage);
        }
        if (data) {
            // Registration successful, now automatically log the user in
            console.log('Registration successful, automatically logging in user...');
            try {
                // Use the same credentials to log in the user
                const loginCredentials = {
                    username: credentials.email,
                    password: credentials.password
                };
                const { data: loginData, error: loginError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["safeApiCall"])(()=>loginUser(loginCredentials), 'auto-login after registration');
                if (loginError) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logAuthError"])(loginError, 'auto-login after registration');
                    // If auto-login fails, redirect to login with success message
                    console.warn('Auto-login failed after registration, redirecting to login page');
                    router.push('/login?registration=success');
                    return;
                }
                if (loginData) {
                    const normalizedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$auth$2d$error$2d$handling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeAuthResponse"])(loginData);
                    setUser(normalizedData);
                    setIsNewUser(true); // New registrations are always new users
                    setLastStateCheck(Date.now());
                    console.log('Auto-login successful, redirecting to onboarding...');
                    // Redirect new user to onboarding
                    router.push('/onboarding');
                }
            } catch (autoLoginError) {
                console.error('Auto-login failed after registration:', autoLoginError);
                // Fallback to login page with success message
                router.push('/login?registration=success');
            }
        }
    };
    const logout = async ()=>{
        try {
            await logoutUser();
        } catch (error) {
            console.error('Logout API call failed:', error);
        }
        // Clear all auth data
        localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN);
        localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN);
        localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID);
        localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT);
        localStorage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].TOKEN_TYPE);
        setUser(null);
        setIsNewUser(false);
        setHasAttemptedAutoAuth(false);
        // Redirect to landing page instead of login
        router.push('/');
    };
    const refreshUserToken = async ()=>{
        try {
            const newTokenData = await refreshTokenApi();
            setUser(newTokenData);
            // Extract is_new_user from refresh response if available
            if (newTokenData.is_new_user !== undefined) {
                setIsNewUser(newTokenData.is_new_user);
                setLastStateCheck(Date.now());
            } else if (shouldRefreshState()) {
                // If refresh response doesn't include is_new_user and it's been a while, fetch it
                await refreshUserState();
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            setUser(null);
            setIsNewUser(false);
            setLastStateCheck(Date.now());
            router.push('/login');
            throw error;
        }
    };
    const completeOnboarding = async ()=>{
        try {
            setIsCompletingOnboarding(true);
            console.log('🚀 Starting onboarding completion...');
            // Call the backend to mark onboarding as complete
            await completeOnboardingApi();
            console.log('✅ Backend onboarding completion successful');
            // Fetch updated user profile to confirm is_new_user is now false
            const updatedProfile = await getUserProfile();
            console.log('✅ Updated profile fetched:', {
                is_new_user: updatedProfile.is_new_user
            });
            setIsNewUser(updatedProfile.is_new_user || false);
            setLastStateCheck(Date.now());
            console.log('✅ User state updated, redirecting to dashboard...');
            // Use setTimeout to ensure state updates are processed before redirect
            setTimeout(()=>{
                setIsCompletingOnboarding(false);
                router.push('/chat');
            }, 100);
        } catch (error) {
            console.error('❌ Failed to complete onboarding:', error);
            setIsCompletingOnboarding(false);
            throw error;
        }
    };
    const handleOAuthCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (tokens)=>{
        try {
            // Calculate expires_at from expires_in (seconds to ISO string)
            const expiresAt = new Date(Date.now() + tokens.expires_in * 1000).toISOString();
            // Store tokens in localStorage using existing STORAGE_KEYS
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].ACCESS_TOKEN, tokens.access_token);
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].REFRESH_TOKEN, tokens.refresh_token);
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].USER_ID, tokens.user_id);
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].EXPIRES_AT, expiresAt);
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].TOKEN_TYPE, tokens.token_type || 'bearer');
            // Create token data for auth context
            const tokenData = {
                access_token: tokens.access_token,
                token_type: tokens.token_type || 'bearer',
                user_id: tokens.user_id,
                expires_at: expiresAt,
                refresh_token: tokens.refresh_token
            };
            // Update auth context
            setUser(tokenData);
            // For OAuth, we need to fetch user profile to get is_new_user status
            try {
                const profile = await getUserProfile();
                setIsNewUser(profile.is_new_user || false);
                setLastStateCheck(Date.now());
                console.log('OAuth user profile fetched, is_new_user:', profile.is_new_user);
            } catch (error) {
                console.error('Failed to fetch user profile during OAuth callback:', error);
                // Don't fail the OAuth flow if profile fetch fails
                setIsNewUser(false);
            }
        // Don't redirect here - let the calling component handle the redirect
        } catch (error) {
            console.error('OAuth callback handling failed:', error);
            throw error;
        }
    }, [
        setUser
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: {
            isAuthenticated: !!user,
            user,
            isLoading,
            isNewUser,
            isCompletingOnboarding,
            login,
            signIn,
            initializeAuthFromStorage,
            register,
            logout,
            setUser,
            refreshUserToken,
            handleOAuthCallback,
            completeOnboarding
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/AuthContext.tsx",
        lineNumber: 408,
        columnNumber: 5
    }, this);
};
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
}}),
"[project]/src/providers/PageHeaderContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageHeaderProvider": (()=>PageHeaderProvider),
    "usePageHeader": (()=>usePageHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
"use client";
;
;
const defaultPageInfo = {
    title: 'Agent Platform',
    subtitle: 'AI-Powered Data Analytics'
};
const defaultPageActions = {
    onCreateChart: undefined,
    chartCount: 0,
    maxCharts: 12
};
const PageHeaderContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const PageHeaderProvider = ({ children })=>{
    const [pageInfo, setPageInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultPageInfo);
    const [actions, setActions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultPageActions);
    const setPageHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((info)=>{
        setPageInfo(info);
    }, []);
    const setPageActions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((newActions)=>{
        setActions(newActions);
    }, []);
    const resetPageHeader = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setPageInfo(defaultPageInfo);
        setActions(defaultPageActions);
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(PageHeaderContext.Provider, {
        value: {
            pageInfo,
            actions,
            setPageHeader,
            setPageActions,
            resetPageHeader
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/PageHeaderContext.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
};
const usePageHeader = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(PageHeaderContext);
    if (context === undefined) {
        throw new Error('usePageHeader must be used within a PageHeaderProvider');
    }
    return context;
};
}}),
"[project]/src/components/common/ClientLayout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/ApiContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$PageHeaderContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/PageHeaderContext.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
// Create a client
const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClient"]();
const ClientLayout = ({ children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthProvider"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$PageHeaderContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PageHeaderProvider"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/components/common/ClientLayout.tsx",
                    lineNumber: 20,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/ClientLayout.tsx",
                lineNumber: 19,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/ClientLayout.tsx",
            lineNumber: 18,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/ClientLayout.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ClientLayout;
}}),
"[project]/src/providers/ChatHistoryContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChatHistoryProvider": (()=>ChatHistoryProvider),
    "useChatHistory": (()=>useChatHistory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/ApiContext.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const ChatHistoryContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const ChatHistoryProvider = ({ children })=>{
    const { user, isAuthenticated, logout, isLoading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const { listUserChats, getChatHistory, deleteChat: apiDeleteChat } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$ApiContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApi"])();
    const [chatHistory, setChatHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [activeChat, setActiveChatState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [chatMessages, setChatMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [isLoadingChats, setIsLoadingChats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoadingHistory, setIsLoadingHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [pendingFirstMessage, setPendingFirstMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isCreatingChat, setIsCreatingChat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const ongoingLoads = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new Set());
    const hasLoadedChats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false); // Track if we've already loaded chats for this session
    const ongoingSetActiveChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false); // Track if setActiveChat is in progress
    // Generate session ID in the format expected by backend: sess_{8_hex_chars}
    const generateSessionId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const hexChars = '0123456789abcdef';
        let result = 'sess_';
        for(let i = 0; i < 8; i++){
            result += hexChars[Math.floor(Math.random() * 16)];
        }
        return result;
    }, []);
    // Convert backend ChatListItem to frontend ChatHistoryItem
    const convertBackendChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((backendChat)=>{
        const lastUpdated = new Date(backendChat.last_seen);
        return {
            id: `chat_${backendChat.session_id}`,
            session_id: backendChat.session_id,
            title: backendChat.title || "Untitled Chat",
            created_at: lastUpdated,
            last_updated: lastUpdated,
            message_count: backendChat.message_count || 0
        };
    }, []);
    // Convert backend ChatMessage to frontend Message
    const convertBackendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((backendMessage)=>{
        return {
            role: backendMessage.role === 'assistant' ? 'agent' : backendMessage.role,
            content: backendMessage.content,
            timestamp: new Date()
        };
    }, []);
    // Load conversation history for a specific session
    const loadChatHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (sessionId)=>{
        if (!isAuthenticated || !user) return;
        // Prevent concurrent loads for the same session
        if (ongoingLoads.current.has(sessionId)) {
            console.log(`Load for session ${sessionId} already in progress, skipping.`);
            return;
        }
        setIsLoadingHistory(true);
        ongoingLoads.current.add(sessionId); // Mark as loading
        try {
            const backendMessages = await getChatHistory(sessionId);
            const convertedMessages = backendMessages.map(convertBackendMessage);
            setChatMessages((prev)=>({
                    ...prev,
                    [sessionId]: convertedMessages
                }));
            // Update message_count in chatHistory to reflect actual loaded messages
            setChatHistory((prevChatHistory)=>prevChatHistory.map((chatItem)=>chatItem.session_id === sessionId ? {
                        ...chatItem,
                        message_count: convertedMessages.length,
                        last_updated: new Date()
                    } : chatItem));
        } catch (error) {
            console.error('Error loading chat history from backend:', error);
            // Handle 401 errors by logging out the user
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 401) {
                console.log('Authentication token invalid while loading chat history, logging out...');
                logout();
                return;
            }
            // Handle 404 errors gracefully (chat doesn't exist yet)
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 404) {
                console.log('Chat history not found (404) - this is normal for new chats');
                setChatMessages((prev)=>({
                        ...prev,
                        [sessionId]: []
                    }));
                // For 404, explicitly set message_count to 0 for this chat in chatHistory
                setChatHistory((prevChatHistory)=>prevChatHistory.map((chatItem)=>chatItem.session_id === sessionId ? {
                            ...chatItem,
                            message_count: 0,
                            last_updated: new Date()
                        } : chatItem));
            } else {
                // For other errors, still set empty array but log the error
                console.error('Unexpected error loading chat history:', error);
                setChatMessages((prev)=>({
                        ...prev,
                        [sessionId]: []
                    }));
                // Also set message_count to 0 here
                setChatHistory((prevChatHistory)=>prevChatHistory.map((chatItem)=>chatItem.session_id === sessionId ? {
                            ...chatItem,
                            message_count: 0,
                            last_updated: new Date()
                        } : chatItem));
            }
        } finally{
            setIsLoadingHistory(false);
            ongoingLoads.current.delete(sessionId); // Unmark as loading
        }
    }, [
        isAuthenticated,
        user,
        logout,
        getChatHistory,
        convertBackendMessage
    ]);
    const setActiveChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (chat)=>{
        console.log('setActiveChat called with:', chat);
        // Prevent concurrent setActiveChat operations
        if (ongoingSetActiveChat.current) {
            console.log('setActiveChat already in progress, ignoring call');
            return;
        }
        // If chat is null, clear the active chat state
        if (chat === null) {
            setActiveChatState(null);
            return;
        }
        // If chat is already the active chat, skip to prevent unnecessary reloads
        if (activeChat && activeChat.id === chat.id) {
            console.log('Chat is already active, skipping to prevent duplicate calls');
            return;
        }
        ongoingSetActiveChat.current = true;
        try {
            // Update the active chat state
            setActiveChatState(chat);
            // Check if we already have messages for this chat
            const existingMessages = chatMessages[chat.session_id] || [];
            const messagesLoaded = existingMessages.length > 0;
            // Only load history if messages aren't already loaded
            // Skip loading for new chats (no session_id yet)
            if (!isLoadingHistory && !messagesLoaded && chat.session_id) {
                console.log('Loading chat history for session:', chat.session_id);
                await loadChatHistory(chat.session_id);
            } else {
                console.log('Skipping history load - either already loaded or new chat (no session_id yet)');
            }
        } finally{
            ongoingSetActiveChat.current = false;
        }
    }, [
        loadChatHistory,
        chatMessages,
        activeChat,
        isLoadingHistory
    ]);
    const addChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title)=>{
        // Prevent multiple simultaneous chat creations
        if (isCreatingChat) {
            console.log('Chat creation already in progress, returning existing or throwing error');
            // Return the most recent chat or throw an error
            const latestChat = chatHistory[0];
            if (latestChat) {
                return latestChat;
            }
            throw new Error('Chat creation already in progress');
        }
        setIsCreatingChat(true);
        try {
            const sessionId = generateSessionId();
            const chatId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const now = new Date();
            const newChat = {
                id: chatId,
                session_id: sessionId,
                title: title || "New Chat",
                created_at: now,
                last_updated: now,
                message_count: 0
            };
            // Add to local state immediately for responsive UI
            setChatHistory((prev)=>[
                    newChat,
                    ...prev
                ]);
            return newChat;
        } finally{
            // Reset the creating state after a short delay to allow for proper state updates
            setTimeout(()=>{
                setIsCreatingChat(false);
            }, 500);
        }
    }, [
        generateSessionId,
        isCreatingChat,
        chatHistory
    ]);
    // Update a chat's session_id (used when backend creates the actual session_id)
    const updateChatSessionId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((chatId, newSessionId)=>{
        let updatedChat = null;
        let oldSessionId = undefined;
        // First, get the old session_id and update the chat
        setChatHistory((prev)=>prev.map((chat)=>{
                if (chat.id === chatId) {
                    oldSessionId = chat.session_id; // Capture the old session_id
                    updatedChat = {
                        ...chat,
                        session_id: newSessionId
                    };
                    return updatedChat;
                }
                return chat;
            }));
        // Update the messages mapping with the new session_id if we have an old one
        if (oldSessionId && oldSessionId !== newSessionId) {
            setChatMessages((prev)=>{
                const messages = prev[oldSessionId] || [];
                const updated = {
                    ...prev
                };
                delete updated[oldSessionId]; // Remove old session_id key
                updated[newSessionId] = messages; // Add with new session_id key
                return updated;
            });
        }
        return updatedChat;
    }, []);
    const removeChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        setChatHistory((prev)=>{
            const chatToRemove = prev.find((chat)=>chat.id === id);
            if (chatToRemove) {
                setChatMessages((prevMessages)=>{
                    const updated = {
                        ...prevMessages
                    };
                    delete updated[chatToRemove.session_id];
                    return updated;
                });
                // If we're removing the active chat, clear it
                setActiveChatState((current)=>current?.id === id ? null : current);
                return prev.filter((chat)=>chat.id !== id);
            }
            return prev;
        });
    }, []);
    const deleteChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (id)=>{
        if (!isAuthenticated || !user) return;
        // Find the chat to get the session_id
        const chatToDelete = chatHistory.find((chat)=>chat.id === id);
        if (!chatToDelete) {
            console.warn('Chat not found for deletion:', id);
            return;
        }
        try {
            // Call the backend API to delete the chat
            await apiDeleteChat(chatToDelete.session_id);
            console.log('Chat deleted successfully from backend:', chatToDelete.session_id);
            // Remove from local state after successful backend deletion
            removeChat(id);
        } catch (error) {
            console.error('Error deleting chat from backend:', error);
            // Handle 401 errors by logging out the user
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 401) {
                console.log('Authentication token invalid while deleting chat, logging out...');
                logout();
                return;
            }
            // Handle 404 errors (chat already deleted) by removing locally
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 404) {
                console.log('Chat not found on backend (404) - removing locally');
                removeChat(id);
                return;
            }
            // For other errors, don't remove locally and re-throw
            throw error;
        }
    }, [
        isAuthenticated,
        user,
        chatHistory,
        apiDeleteChat,
        removeChat,
        logout
    ]);
    const renameChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id, newTitle)=>{
        setChatHistory((prev)=>prev.map((chat)=>chat.id === id ? {
                    ...chat,
                    title: newTitle,
                    last_updated: new Date()
                } : chat));
    }, []);
    // Generate a chat title from the first user message
    const generateChatTitle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((firstMessage)=>{
        // Take first 50 characters and clean up
        let title = firstMessage.trim().substring(0, 50);
        // Remove common question words and clean up
        title = title.replace(/^(what|how|when|where|why|who|can|could|would|should|is|are|do|does|did)\s+/i, '');
        // Capitalize first letter
        title = title.charAt(0).toUpperCase() + title.slice(1);
        // Add ellipsis if truncated
        if (firstMessage.length > 50) {
            title += '...';
        }
        return title || 'New Chat';
    }, []);
    const addMessageToChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((sessionId, message)=>{
        const messageWithTimestamp = {
            ...message,
            timestamp: message.timestamp || new Date()
        };
        setChatMessages((prev)=>({
                ...prev,
                [sessionId]: [
                    ...prev[sessionId] || [],
                    messageWithTimestamp
                ]
            }));
        // Update the last_updated time and message count for the chat
        setChatHistory((prev)=>prev.map((chat)=>chat.session_id === sessionId ? {
                    ...chat,
                    last_updated: new Date(),
                    message_count: chat.message_count + 1
                } : chat));
        // Auto-generate title from first user message (for new chats)
        if (message.role === 'user') {
            setChatHistory((prev)=>prev.map((chat)=>{
                    if (chat.session_id === sessionId && (chat.title === 'New Chat' || chat.title === 'Untitled Chat')) {
                        const title = generateChatTitle(message.content);
                        return {
                            ...chat,
                            title
                        };
                    }
                    return chat;
                }));
        }
    }, [
        generateChatTitle
    ]);
    // Load chats when user authentication state changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log('ChatHistoryContext useEffect triggered:', {
            authLoading,
            isAuthenticated,
            hasAccessToken: !!user?.access_token,
            userId: user?.user_id,
            hasLoadedChats: hasLoadedChats.current
        });
        // Skip if still loading auth or not authenticated
        if (authLoading || !isAuthenticated || !user?.access_token) {
            console.log('Skipping chat load - auth loading or not authenticated');
            if (!authLoading && !isAuthenticated) {
                // Only clear data when definitely not authenticated (not during loading)
                setChatHistory([]);
                setChatMessages({});
                setActiveChatState(null);
                hasLoadedChats.current = false; // Reset the flag when user logs out
            }
            return;
        }
        // Prevent loading chats multiple times for the same authenticated session
        if (hasLoadedChats.current) {
            console.log('Chats already loaded for this session, skipping...');
            return;
        }
        // Load chats only once after authentication
        console.log('Loading chats for authenticated user...');
        hasLoadedChats.current = true; // Set flag before starting the load
        setIsLoadingChats(true);
        listUserChats().then((backendChats)=>{
            console.log('Successfully loaded chats from backend:', backendChats.length);
            const convertedChats = backendChats.map(convertBackendChat);
            convertedChats.sort((a, b)=>b.last_updated.getTime() - a.last_updated.getTime());
            setChatHistory(convertedChats);
        }).catch((error)=>{
            console.error('Error loading chats from backend:', error);
            hasLoadedChats.current = false; // Reset flag on error so we can retry
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 401) {
                console.log('401 error while loading chats, logging out...');
                logout();
                return;
            }
            setChatHistory([]);
        }).finally(()=>{
            setIsLoadingChats(false);
        });
    }, [
        authLoading,
        isAuthenticated,
        user?.access_token,
        user?.user_id
    ]); // Removed function dependencies to prevent infinite loops
    // 🏢 ENTERPRISE FUNCTION: Refresh chat list from backend
    const refreshChatList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (!isAuthenticated || !user?.access_token) {
            return;
        }
        try {
            console.log('Refreshing chat list from backend...');
            const backendChats = await listUserChats();
            const convertedChats = backendChats.map(convertBackendChat);
            convertedChats.sort((a, b)=>b.last_updated.getTime() - a.last_updated.getTime());
            setChatHistory(convertedChats);
            console.log('Chat list refreshed successfully');
        } catch (error) {
            console.error('Error refreshing chat list:', error);
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response?.status === 401) {
                logout();
            }
        }
    }, [
        isAuthenticated,
        user?.access_token
    ]); // Removed function dependencies
    // Load specific chat by ID - useful for direct navigation to /chat/{chatId}
    const loadChatById = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (chatId)=>{
        console.log('Loading chat by ID:', chatId);
        // First check if chat is already in our chat history
        let chat = chatHistory.find((c)=>c.id === chatId);
        if (chat) {
            console.log('Chat found in existing history:', chat);
            await setActiveChat(chat);
            return chat;
        }
        // If not found and we haven't loaded chats yet, wait for them to load
        if (isLoadingChats) {
            console.log('Chats still loading, waiting...');
            // Return null and let the component handle the loading state
            return null;
        }
        // If chats are loaded but chat not found, the chat doesn't exist
        console.log('Chat not found in loaded chat history - chat may not exist:', chatId);
        return null;
    }, [
        chatHistory,
        isLoadingChats,
        setActiveChat
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ChatHistoryContext.Provider, {
        value: {
            chatHistory,
            activeChat,
            chatMessages,
            isLoadingChats,
            isLoadingHistory,
            pendingFirstMessage,
            setPendingFirstMessage,
            addChat,
            updateChatSessionId,
            deleteChat,
            renameChat,
            setActiveChat,
            loadChatById,
            addMessageToChat,
            loadChatHistory,
            refreshChatList,
            generateSessionId
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/ChatHistoryContext.tsx",
        lineNumber: 490,
        columnNumber: 5
    }, this);
};
const useChatHistory = ()=>{
    const ctx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ChatHistoryContext);
    if (!ctx) throw new Error("useChatHistory must be used within a ChatHistoryProvider");
    return ctx;
};
}}),
"[project]/src/providers/theme-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
function ThemeProvider({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/theme-provider.tsx",
        lineNumber: 12,
        columnNumber: 10
    }, this);
}
const useTheme = ()=>{
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const { theme, setTheme, resolvedTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    // After mounting, we have access to the theme
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>setMounted(true), []);
    return {
        theme: mounted ? theme : undefined,
        setTheme,
        resolvedTheme: mounted ? resolvedTheme : undefined,
        mounted
    };
};
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c95cade0._.js.map