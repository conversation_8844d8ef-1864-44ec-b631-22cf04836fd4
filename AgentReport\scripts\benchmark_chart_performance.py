#!/usr/bin/env python3
"""
Benchmark script to measure chart query performance improvements.
Run this script to test the actual performance of the chart endpoint.
"""
import asyncio
import time
import json
import statistics
from typing import List, Dict, Any
import aiohttp
import argparse


class ChartPerformanceBenchmark:
    """Benchmark chart query performance."""
    
    def __init__(self, base_url: str = "http://localhost:8000", auth_token: str = None):
        self.base_url = base_url
        self.auth_token = auth_token
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}" if auth_token else ""
        }
    
    async def make_chart_request(self, session: aiohttp.ClientSession, query: str, database_id: str) -> Dict[str, Any]:
        """Make a single chart query request and measure response time."""
        start_time = time.time()
        
        payload = {
            "prompt": query,
            "database_id": database_id
        }
        
        try:
            async with session.post(
                f"{self.base_url}/api/chart/query",
                json=payload,
                headers=self.headers
            ) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    return {
                        "success": True,
                        "response_time": response_time,
                        "status_code": response.status,
                        "data": data
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "response_time": response_time,
                        "status_code": response.status,
                        "error": error_text
                    }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "success": False,
                "response_time": response_time,
                "status_code": 0,
                "error": str(e)
            }
    
    async def run_benchmark(self, queries: List[str], database_id: str, iterations: int = 5) -> Dict[str, Any]:
        """Run benchmark with multiple queries and iterations."""
        results = []
        
        async with aiohttp.ClientSession() as session:
            for query in queries:
                query_results = []
                
                print(f"\nTesting query: '{query[:50]}...'")
                
                for i in range(iterations):
                    print(f"  Iteration {i+1}/{iterations}...", end=" ")
                    
                    result = await self.make_chart_request(session, query, database_id)
                    query_results.append(result)
                    
                    if result["success"]:
                        print(f"✓ {result['response_time']:.2f}s")
                    else:
                        print(f"✗ Error: {result.get('error', 'Unknown error')}")
                
                # Calculate statistics for this query
                successful_results = [r for r in query_results if r["success"]]
                if successful_results:
                    response_times = [r["response_time"] for r in successful_results]
                    query_stats = {
                        "query": query,
                        "success_rate": len(successful_results) / len(query_results),
                        "avg_response_time": statistics.mean(response_times),
                        "min_response_time": min(response_times),
                        "max_response_time": max(response_times),
                        "median_response_time": statistics.median(response_times),
                        "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0,
                        "iterations": len(query_results),
                        "successful_iterations": len(successful_results)
                    }
                else:
                    query_stats = {
                        "query": query,
                        "success_rate": 0,
                        "avg_response_time": None,
                        "min_response_time": None,
                        "max_response_time": None,
                        "median_response_time": None,
                        "std_dev": None,
                        "iterations": len(query_results),
                        "successful_iterations": 0,
                        "errors": [r.get("error") for r in query_results if not r["success"]]
                    }
                
                results.append(query_stats)
        
        return {
            "benchmark_results": results,
            "overall_stats": self._calculate_overall_stats(results)
        }
    
    def _calculate_overall_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall benchmark statistics."""
        successful_queries = [r for r in results if r["success_rate"] > 0]
        
        if not successful_queries:
            return {
                "overall_success_rate": 0,
                "avg_response_time": None,
                "fastest_query": None,
                "slowest_query": None
            }
        
        all_response_times = []
        for result in successful_queries:
            if result["avg_response_time"] is not None:
                all_response_times.append(result["avg_response_time"])
        
        if not all_response_times:
            return {
                "overall_success_rate": 0,
                "avg_response_time": None,
                "fastest_query": None,
                "slowest_query": None
            }
        
        fastest_query = min(successful_queries, key=lambda x: x["avg_response_time"] or float('inf'))
        slowest_query = max(successful_queries, key=lambda x: x["avg_response_time"] or 0)
        
        return {
            "overall_success_rate": sum(r["success_rate"] for r in results) / len(results),
            "avg_response_time": statistics.mean(all_response_times),
            "fastest_query": {
                "query": fastest_query["query"][:50] + "...",
                "avg_time": fastest_query["avg_response_time"]
            },
            "slowest_query": {
                "query": slowest_query["query"][:50] + "...",
                "avg_time": slowest_query["avg_response_time"]
            },
            "total_queries_tested": len(results)
        }
    
    def print_results(self, results: Dict[str, Any]):
        """Print benchmark results in a readable format."""
        print("\n" + "="*80)
        print("CHART QUERY PERFORMANCE BENCHMARK RESULTS")
        print("="*80)
        
        overall = results["overall_stats"]
        print(f"\nOVERALL STATISTICS:")
        print(f"  Success Rate: {overall['overall_success_rate']:.1%}")
        print(f"  Average Response Time: {overall['avg_response_time']:.2f}s" if overall['avg_response_time'] else "  Average Response Time: N/A")
        print(f"  Total Queries Tested: {overall['total_queries_tested']}")
        
        if overall['fastest_query']:
            print(f"  Fastest Query: {overall['fastest_query']['avg_time']:.2f}s - {overall['fastest_query']['query']}")
        if overall['slowest_query']:
            print(f"  Slowest Query: {overall['slowest_query']['avg_time']:.2f}s - {overall['slowest_query']['query']}")
        
        print(f"\nDETAILED RESULTS:")
        for i, result in enumerate(results["benchmark_results"], 1):
            print(f"\n{i}. Query: {result['query'][:60]}...")
            print(f"   Success Rate: {result['success_rate']:.1%} ({result['successful_iterations']}/{result['iterations']})")
            
            if result['avg_response_time'] is not None:
                print(f"   Avg Time: {result['avg_response_time']:.2f}s")
                print(f"   Min Time: {result['min_response_time']:.2f}s")
                print(f"   Max Time: {result['max_response_time']:.2f}s")
                print(f"   Median: {result['median_response_time']:.2f}s")
                print(f"   Std Dev: {result['std_dev']:.2f}s")
            else:
                print(f"   No successful requests")
                if 'errors' in result:
                    print(f"   Errors: {result['errors'][:3]}")  # Show first 3 errors


async def main():
    """Main benchmark function."""
    parser = argparse.ArgumentParser(description="Benchmark chart query performance")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL of the API")
    parser.add_argument("--token", required=True, help="Authentication token")
    parser.add_argument("--database-id", required=True, help="Database ID to test with")
    parser.add_argument("--iterations", type=int, default=3, help="Number of iterations per query")
    
    args = parser.parse_args()
    
    # Sample queries for testing
    test_queries = [
        "What are our top 10 performing films by revenue?",
        "Show me the average rating by genre",
        "How many films were released each year?",
        "What is the total revenue for all films?",
        "Which actors appear in the most films?",
    ]
    
    benchmark = ChartPerformanceBenchmark(args.url, args.token)
    
    print("Starting Chart Query Performance Benchmark...")
    print(f"API URL: {args.url}")
    print(f"Database ID: {args.database_id}")
    print(f"Iterations per query: {args.iterations}")
    print(f"Test queries: {len(test_queries)}")
    
    results = await benchmark.run_benchmark(test_queries, args.database_id, args.iterations)
    benchmark.print_results(results)
    
    # Save results to file
    with open("chart_performance_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved to: chart_performance_results.json")


if __name__ == "__main__":
    asyncio.run(main())
