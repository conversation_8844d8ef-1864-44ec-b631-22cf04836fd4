"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { X, CheckCircle, Database, FileText, Cloud, PlusCircle,
         BarChart3, TrendingUp, Clock, AlignLeft, Filter, Hash,
         Image, FileText as FileTextIcon, Type, PieChart, Activity } from 'lucide-react';
import { ChartData, ChartWidget } from '@/types';
import { useDataSources } from '@/providers/DataSourcesContext';

interface ChartConfigSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  chartData?: ChartData | null;
  widget?: ChartWidget | null;
  sqlQuery?: string;
  onConfigureChart?: (chartType: string, dataSourceId: string, prompt?: string) => void;
  className?: string;
}

// Chart type definitions with icons
const CHART_TYPES = [
  { id: 'table', name: 'Table', icon: AlignLeft },
  { id: 'line', name: 'Line', icon: TrendingUp },
  { id: 'timebar', name: 'Timebar', icon: Clock },
  { id: 'bar', name: 'Horizontal bar', icon: BarChart3 },
  { id: 'funnel', name: 'Funnel', icon: Filter },
  { id: 'number', name: 'Number', icon: Hash },
  { id: 'image', name: 'Image', icon: Image },
  { id: 'detail', name: 'Detail', icon: FileTextIcon },
  { id: 'text', name: 'Text', icon: Type },
  { id: 'pie', name: 'Pie', icon: PieChart },
  { id: 'activity', name: 'Activity', icon: Activity },
];

// Icon mapping for data source types - only databases now
const getDataSourceIcon = (type: string) => {
  return Database; // All data sources are databases
};

const ChartConfigSidebar: React.FC<ChartConfigSidebarProps> = ({
  isOpen,
  onClose,
  chartData,
  widget,
  sqlQuery,
  onConfigureChart,
  className = '',
}) => {
  const [selectedChartType, setSelectedChartType] = useState<string>('');
  const [selectedDataSource, setSelectedDataSource] = useState<string>('');
  const { connectedDataSources } = useDataSources();

  // Reset selections when sidebar opens
  useEffect(() => {
    if (isOpen && !chartData) {
      setSelectedChartType('');
      setSelectedDataSource('');
    }
  }, [isOpen, chartData]);

  const handleApplyConfiguration = () => {
    if (selectedChartType && selectedDataSource && onConfigureChart) {
      // Pass the pending prompt from the widget if available
      const prompt = widget?.pendingPrompt || '';
      onConfigureChart(selectedChartType, selectedDataSource, prompt);
      onClose();
    }
  };

  const canApplyConfiguration = selectedChartType && selectedDataSource;

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      } else if (event.key === 'Enter' && canApplyConfiguration) {
        event.preventDefault();
        handleApplyConfiguration();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onClose, canApplyConfiguration, handleApplyConfiguration]);

  // Component is now always rendered when parent shows it, no need to check isOpen

  return (
    <div 
      className={`w-full h-full flex flex-col ${className}`}
      style={{
        backgroundColor: 'var(--sidebar-surface-secondary)',
        borderLeft: '1px solid var(--sidebar-border)',
      }}
    >
        {/* Header - Fixed with close button only */}
        <div 
          className="flex-shrink-0 flex items-center justify-end p-3"
          style={{ 
            backgroundColor: 'var(--sidebar-surface-secondary)',

          }}
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-7 w-7 p-0 rounded-md"
            style={{ color: 'var(--sidebar-text-secondary)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Chart Data View (when data exists) */}
          {chartData && (
            <div className="space-y-4">
              <div>
                <h3 
                  className="text-sm font-medium mb-2"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                >
                  Chart Type
                </h3>
                <div 
                  className="p-3 rounded-lg border"
                  style={{
                    backgroundColor: 'var(--sidebar-surface-secondary)',
                    borderColor: 'var(--sidebar-border)',
                  }}
                >
                  <div className="flex items-center gap-2">
                    {(() => {
                      const chartType = CHART_TYPES.find(type => type.id === chartData.chartType);
                      const Icon = chartType?.icon || BarChart3;
                      return (
                        <>
                          <Icon className="h-4 w-4" style={{ color: 'var(--sidebar-icon)' }} />
                          <span 
                            className="text-sm"
                            style={{ color: 'var(--sidebar-text-primary)' }}
                          >
                            {chartType?.name || chartData.chartType}
                          </span>
                        </>
                      );
                    })()}
                  </div>
                </div>
              </div>

              {sqlQuery && (
                <div>
                  <h3 
                    className="text-sm font-medium mb-2"
                    style={{ color: 'var(--sidebar-text-primary)' }}
                  >
                    SQL Query
                  </h3>
                  <div 
                    className="p-3 rounded-lg border max-h-48 overflow-y-auto"
                    style={{
                      backgroundColor: 'var(--sidebar-surface-secondary)',
                      borderColor: 'var(--sidebar-border)',
                    }}
                  >
                    <pre 
                      className="text-xs font-mono whitespace-pre-wrap"
                      style={{ color: 'var(--sidebar-text-secondary)' }}
                    >
                      {sqlQuery}
                    </pre>
                  </div>
                </div>
              )}

              <div>
                <h3 
                  className="text-sm font-medium mb-2"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                >
                  Chart Title
                </h3>
                <div 
                  className="p-3 rounded-lg border"
                  style={{
                    backgroundColor: 'var(--sidebar-surface-secondary)',
                    borderColor: 'var(--sidebar-border)',
                  }}
                >
                  <span 
                    className="text-sm"
                    style={{ color: 'var(--sidebar-text-primary)' }}
                  >
                    {chartData.title}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Chart Configuration (when no data exists) */}
          {!chartData && (
            <div className="space-y-6">
              {/* Data Source Selection - moved above chart types */}
              <div>
                <h3
                  className="text-sm font-medium mb-3"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                >
                  Select a database
                </h3>
                <p
                  className="text-xs mb-3"
                  style={{ color: 'var(--sidebar-text-secondary)' }}
                >
                  Choose which connected database to query for real data. Charts will display actual data from your selected database.
                </p>
                {widget?.pendingPrompt && (
                  <div
                    className="text-xs mb-3 p-2 rounded"
                    style={{
                      backgroundColor: 'var(--sidebar-surface-tertiary)',
                      color: 'var(--sidebar-text-primary)',
                      border: '1px solid var(--sidebar-border)'
                    }}
                  >
                    <strong>Your prompt:</strong> "{widget.pendingPrompt}"
                  </div>
                )}
                {connectedDataSources.length > 0 ? (
                  <div className="space-y-1.5">
                    {connectedDataSources.map((dataSource) => {
                      const Icon = getDataSourceIcon(dataSource.type);
                      const isSelected = selectedDataSource === dataSource.id;
                      
                      return (
                        <div
                          key={dataSource.id}
                          className={`flex items-center gap-2.5 p-2.5 cursor-pointer transition-all duration-200 ${
                            isSelected ? 'ring-1 ring-blue-400 rounded-md' : ''
                          }`}
                          style={{
                            backgroundColor: isSelected ? 'var(--sidebar-surface-secondary)' : 'var(--sidebar-surface-secondary)',
                          }}
                          onClick={() => setSelectedDataSource(dataSource.id)}
                        >
                          <Icon className="h-3.5 w-3.5" style={{ color: 'var(--sidebar-icon)' }} />
                          <div className="flex-1 min-w-0">
                            <span 
                              className="text-xs font-medium block"
                              style={{ color: 'var(--sidebar-text-primary)' }}
                            >
                              {dataSource.name}
                            </span>
                            <span 
                              className="text-xs block truncate"
                              style={{ color: 'var(--sidebar-text-secondary)' }}
                            >
                              {dataSource.description}
                            </span>
                          </div>
                          {isSelected && (
                            <CheckCircle className="h-3.5 w-3.5 ml-1" style={{ color: 'rgba(59, 130, 246, 1)' }} />
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div 
                    className="p-3 text-center"
                    style={{
                      backgroundColor: '#303030',
                    }}
                  >
                    <Database className="h-6 w-6 mx-auto mb-1.5" style={{ color: 'var(--sidebar-text-tertiary)' }} />
                    <p 
                      className="text-xs font-medium"
                      style={{ color: 'var(--sidebar-text-primary)' }}
                    >
                      No data sources connected
                    </p>
                    <p 
                      className="text-xs mt-0.5"
                      style={{ color: 'var(--sidebar-text-secondary)' }}
                    >
                      Connect a data source to create charts
                    </p>
                  </div>
                )}
              </div>

              {/* User Prompt Input */}


              {/* Chart Type Selection - moved below data sources */}
              <div>
                <h3 
                  className="text-sm font-medium mb-3"
                  style={{ color: 'var(--sidebar-text-primary)' }}
                >
                  Select a chart type
                </h3>
                <div className="space-y-1.5">
                  {CHART_TYPES.map((chartType) => {
                    const Icon = chartType.icon;
                    const isSelected = selectedChartType === chartType.id;
                    
                    return (
                      <div
                        key={chartType.id}
                        className={`flex items-center gap-2.5 p-2.5 cursor-pointer transition-all duration-200 ${
                          isSelected ? 'ring-1 ring-blue-400 rounded-md' : ''
                        }`}
                        style={{
                          backgroundColor: isSelected ? 'var(--sidebar-surface-secondary)' : 'var(--sidebar-surface-secondary)',
                        }}
                        onClick={() => setSelectedChartType(chartType.id)}
                      >
                        <Icon className="h-3.5 w-3.5" style={{ color: 'var(--sidebar-icon)' }} />
                        <span 
                          className="text-xs font-medium"
                          style={{ color: 'var(--sidebar-text-primary)' }}
                        >
                          {chartType.name}
                        </span>
                        {isSelected && (
                          <CheckCircle className="h-3.5 w-3.5 ml-auto" style={{ color: 'rgba(59, 130, 246, 1)' }} />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Generate Chart Button */}
              <div className="pt-4">
                <Button
                  onClick={handleApplyConfiguration}
                  disabled={!canApplyConfiguration}
                  className="w-full"
                  style={{
                    backgroundColor: canApplyConfiguration ? 'rgba(59, 130, 246, 1)' : 'var(--sidebar-surface-secondary)',
                    color: canApplyConfiguration ? 'white' : 'var(--sidebar-text-secondary)',
                    borderColor: 'var(--sidebar-border)',
                  }}
                >
                  Generate Chart with Real Data
                </Button>
                {selectedDataSource && (
                  <p
                    className="text-xs mt-2 text-center"
                    style={{ color: 'var(--sidebar-text-secondary)' }}
                  >
                    Will query: {connectedDataSources.find(ds => ds.id === selectedDataSource)?.name}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

export default ChartConfigSidebar; 