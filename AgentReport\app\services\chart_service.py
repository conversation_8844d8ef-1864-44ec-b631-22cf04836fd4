"""Chart Service

This service handles chart generation from user queries using LLM analysis.
It processes natural language queries to create appropriate visualizations with real or mock data.
"""

import json
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union

from app.models.chart import (
    ChartType, ChartData, ChartDataPoint, ChartMetadata,
    ChartQueryRequest, ChartQueryResponse, ChartGenerationContext,
    ChartTypeRecommendation, MockDataGenerationConfig,
    DEFAULT_CHART_COLORS, CHART_TYPE_DESCRIPTIONS, CHART_SQL_REQUIREMENTS,
    PieChartData, BarChartData, LineChartData, TimeBarChartData, NumberChartData,
    TableChartData, FunnelChartData, ActivityChartData, TextChartData,
    ImageChartData, DetailChartData
)
from app.models.database import Database, DatabaseType
from app.services.database_manager_service import DatabaseManagerService
from app.services.database_service import DatabaseService
from app.services.intelligent_cache_service import IntelligentCacheService
from app.utils.bedrock_client import BedrockClient
from app.config import settings

logger = logging.getLogger(__name__)


class ChartService:
    """Service for generating charts from natural language queries using LLM analysis."""

    def __init__(self):
        """Initialize the chart service with required dependencies."""
        self.bedrock_client = BedrockClient()
        self.mock_config = MockDataGenerationConfig()
        self.database_manager = DatabaseManagerService()
        self.database_service = DatabaseService()
        self.cache_service = IntelligentCacheService()

    async def process_chart_query(self, request: ChartQueryRequest) -> ChartQueryResponse:
        """
        Process a chart query request and return a complete chart response.

        Args:
            request: The chart query request from the frontend

        Returns:
            ChartQueryResponse with chart data or error information
        """
        try:
            logger.info(f"Processing chart query: {request.prompt[:100]}...")

            # Create generation context
            context = ChartGenerationContext(
                user_query=request.prompt,
                user_id=request.user_id,
                dashboard_id=request.dashboard_id,
                processing_notes=[]
            )

            # Run chart type recommendation and database info gathering in parallel
            import asyncio
            recommendation_task = asyncio.create_task(self._get_chart_type_recommendation(context))
            database_info_task = asyncio.create_task(self._get_database_info(request.user_id, request.database_id))

            # Wait for both to complete
            recommendation, database_info = await asyncio.gather(recommendation_task, database_info_task)
            context.recommended_chart_type = recommendation.chart_type

            # Validate database info
            if not database_info:
                raise ValueError(f"Database {request.database_id} not found or has no accessible tables. Please check your database connection.")

            # Generate data from database (now with pre-fetched database info)
            data_points, sql_query = await self._generate_real_chart_data_with_info(
                context, recommendation.chart_type, request.database_id, request.user_id, database_info
            )

            # Create metadata and assemble final chart data
            metadata = self._create_chart_metadata(context, recommendation, sql_query)
            final_chart_type = context.recommended_chart_type

            # Convert structured data to legacy format for frontend compatibility
            legacy_data_points = self._convert_to_legacy_format(data_points, final_chart_type)

            chart_data = ChartData(
                title=recommendation.title,
                chartType=final_chart_type,
                data=legacy_data_points,
                metadata=metadata
            )

            logger.info(f"Successfully generated {recommendation.chart_type} chart: {recommendation.title}")
            return ChartQueryResponse(success=True, data=chart_data)

        except Exception as e:
            logger.error(f"Error processing chart query: {str(e)}")
            return ChartQueryResponse(
                success=False,
                error=f"Failed to generate chart: {str(e)}"
            )
    
    # ============================================================================
    # CHART TYPE RECOMMENDATION
    # ============================================================================

    async def _get_chart_type_recommendation(self, context: ChartGenerationContext) -> ChartTypeRecommendation:
        """
        Analyze the user query and recommend the most appropriate chart type.

        Args:
            context: Chart generation context

        Returns:
            ChartTypeRecommendation with the recommended chart type and title
        """
        try:
            response = await self._get_llm_chart_recommendation(context)
            
            # Clean and extract JSON from response
            cleaned_response = self._extract_json_from_response(response)
            response_data = json.loads(cleaned_response)

            return ChartTypeRecommendation(
                chart_type=ChartType(response_data["chart_type"]),
                title=response_data.get("title", self._generate_fallback_title(context.user_query))
            )

        except Exception as e:
            logger.warning(f"LLM chart type recommendation failed: {e}. Using fallback logic.")
            return self._fallback_chart_type_recommendation(context)

    async def _get_llm_chart_recommendation(self, context: ChartGenerationContext) -> str:
        """Get chart type recommendation and title from LLM."""
        system_prompt = """You are a data visualization expert. Analyze user queries and recommend the most appropriate chart type and generate a descriptive title.

Available chart types and their use cases:
- table: Detailed data display with multiple columns and precise values
- line: Showing trends and changes over continuous time periods
- timebar: Data changes over specific time intervals and periods
- bar: Comparing discrete categories, showing changes over distinct time periods
- funnel: Conversion rates, process stages, and sequential data
- number: Single key metrics, KPIs, and summary statistics
- image: Visual content, photos, graphics, or visual documentation
- detail: Comprehensive information views, drill-down data, and detailed analysis
- text: Descriptive information, summaries, explanations, and narrative content
- pie: Parts of a whole, percentage breakdowns, and composition analysis
- activity: Events, actions, timelines, and activity patterns over time

For the title, create a concise, descriptive chart title that clearly indicates what data is being visualized. Keep titles under 60 characters and make them professional and clear.

Respond with a JSON object containing:
{
    "chart_type": "recommended_type",
    "title": "Chart Title"
}"""

        user_prompt = f"""Analyze this user query and recommend the best chart type:

Query: "{context.user_query}"

Consider:
1. What type of data comparison or analysis is being requested?
2. Is this about trends over time, categorical comparisons, or composition?
3. Does the user want to see detailed data or high-level insights?
4. Are there keywords that suggest specific visualization needs?

Provide your recommendation as a JSON object."""

        return await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3
        )

    def _extract_json_from_response(self, response: str) -> str:
        """Extract JSON object from LLM response that may contain extra content."""
        response = response.strip()
        
        # Try to find JSON object boundaries
        json_start = response.find('{')
        if json_start == -1:
            raise ValueError("No JSON object found in response")
        
        # Find the matching closing brace
        brace_count = 0
        json_end = -1
        
        for i in range(json_start, len(response)):
            if response[i] == '{':
                brace_count += 1
            elif response[i] == '}':
                brace_count -= 1
                if brace_count == 0:
                    json_end = i
                    break
        
        if json_end == -1:
            raise ValueError("No complete JSON object found in response")
        
        json_str = response[json_start:json_end + 1]
        logger.debug(f"Extracted JSON from LLM response: {json_str}")
        return json_str
    
    def _fallback_chart_type_recommendation(self, context: ChartGenerationContext) -> ChartTypeRecommendation:
        """
        Fallback chart type recommendation using keyword analysis.
        
        Args:
            context: Chart generation context
            
        Returns:
            ChartTypeRecommendation based on keyword analysis
        """
        query_lower = context.user_query.lower()
        
        # First check for explicit chart type requests
        if any(word in query_lower for word in ['pie chart', 'make it a pie', 'as a pie', 'pie graph']):
            chart_type = ChartType.PIE
        elif any(word in query_lower for word in ['bar chart', 'make it a bar', 'as a bar', 'bar graph']):
            chart_type = ChartType.BAR
        elif any(word in query_lower for word in ['line chart', 'make it a line', 'as a line', 'line graph']):
            chart_type = ChartType.LINE
        elif any(word in query_lower for word in ['table', 'make it a table', 'as a table']):
            chart_type = ChartType.TABLE
        elif any(word in query_lower for word in ['funnel chart', 'make it a funnel', 'as a funnel']):
            chart_type = ChartType.FUNNEL
        # Then check for content-based keywords
        elif any(word in query_lower for word in ['breakdown', 'distribution', 'percentage', 'share', 'composition']):
            chart_type = ChartType.PIE
        elif any(word in query_lower for word in ['over time', 'trend', 'timeline', 'monthly', 'daily', 'yearly']):
            chart_type = ChartType.LINE
        elif any(word in query_lower for word in ['time periods', 'time intervals', 'duration', 'schedule']):
            chart_type = ChartType.TIMEBAR
        elif any(word in query_lower for word in ['compare', 'comparison', 'vs', 'versus', 'between']):
            chart_type = ChartType.BAR
        elif any(word in query_lower for word in ['detailed', 'list', 'rows', 'columns']):
            chart_type = ChartType.TABLE
        elif any(word in query_lower for word in ['conversion', 'stages', 'process', 'steps']):
            chart_type = ChartType.FUNNEL
        elif any(word in query_lower for word in ['count', 'number', 'total', 'metric']) and len(query_lower.split()) < 5:
            chart_type = ChartType.NUMBER
        elif any(word in query_lower for word in ['activity', 'events', 'actions', 'history', 'log']):
            chart_type = ChartType.ACTIVITY
        elif any(word in query_lower for word in ['image', 'photo', 'picture', 'visual', 'graphic']):
            chart_type = ChartType.IMAGE
        elif any(word in query_lower for word in ['detail', 'details', 'drill down', 'comprehensive', 'analysis']):
            chart_type = ChartType.DETAIL
        elif any(word in query_lower for word in ['text', 'description', 'summary', 'explanation', 'narrative']):
            chart_type = ChartType.TEXT
        else:
            chart_type = ChartType.BAR
        
        return ChartTypeRecommendation(
            chart_type=chart_type,
            title=self._generate_fallback_title(context.user_query)
        )

    def _generate_fallback_title(self, user_query: str) -> str:
        """Generate a fallback title from the user query."""
        query_words = user_query.strip().split()

        # If query is short enough, use it directly
        if len(query_words) <= 8:
            title = user_query.strip()
            # Capitalize first letter if not already
            if title and not title[0].isupper():
                title = title[0].upper() + title[1:]
            return title

        # For longer queries, create a shortened version
        key_words = query_words[:6]
        title = ' '.join(key_words)

        # Add ellipsis if we truncated
        if len(query_words) > 6:
            title += "..."

        # Capitalize first letter
        if title and not title[0].isupper():
            title = title[0].upper() + title[1:]

        return title
    
    # ============================================================================
    # DATA GENERATION
    # ============================================================================

    async def _generate_real_chart_data(self, context: ChartGenerationContext, chart_type: ChartType, database_id: str, user_id: str) -> Tuple[List[ChartDataPoint], Optional[str]]:
        """
        Generate chart data by querying a real database with intelligent caching.

        Args:
            context: Chart generation context
            chart_type: The type of chart to generate data for
            database_id: ID of the database to query
            user_id: ID of the user making the request

        Returns:
            Tuple of (List of chart data points, SQL query used)
        """
        try:
            # Create cache key for this specific query
            import hashlib
            query_hash = hashlib.md5(f"{context.user_query}:{chart_type.value}:{database_id}".encode()).hexdigest()
            cache_key = f"chart_query_result:{user_id}:{query_hash}"

            # Check if we have cached results
            cached_result = self.cache_service.get_result_data(cache_key)
            if cached_result:
                logger.info(f"Using cached chart query result for user {user_id}")
                return cached_result["data_points"], cached_result["sql_query"]

            # Get database schema and validate
            database_info = await self._get_database_info(user_id, database_id)
            if not database_info:
                raise ValueError(f"Database {database_id} not found or has no accessible tables. Please check your database connection.")

            # Generate SQL query
            sql_query = await self._generate_sql_for_chart_with_schema(context.user_query, database_info, chart_type)
            if not sql_query:
                raise ValueError("Failed to generate SQL query. Please try rephrasing your question or check if your database has the required data.")

            # Execute query and transform results
            query_result = await self._execute_database_query(database_id, user_id, sql_query)
            if query_result is None:
                raise ValueError("Failed to execute database query. Please check your database connection and permissions.")

            # Transform results to chart data points
            data_points = self._transform_query_result_to_structured_data(query_result, chart_type)

            # Post-process for single data points (convert to number chart if appropriate)
            self._post_process_single_data_point(context, data_points, chart_type)

            # Cache the result for 5 minutes
            cache_result = {
                "data_points": data_points,
                "sql_query": sql_query
            }
            self.cache_service.cache_result_data(cache_key, cache_result, ttl=300)
            logger.info(f"Cached chart query result for user {user_id}")

            logger.info(f"Successfully generated chart data from database {database_id}")
            return data_points, sql_query

        except Exception as e:
            logger.error(f"Error generating chart data from database: {e}")
            raise

    async def _generate_real_chart_data_with_info(
        self,
        context: ChartGenerationContext,
        chart_type: ChartType,
        database_id: str,
        user_id: str,
        database_info: Dict[str, Any]
    ) -> Tuple[List[ChartDataPoint], Optional[str]]:
        """
        Generate chart data using pre-fetched database info for better performance.

        Args:
            context: Chart generation context
            chart_type: The type of chart to generate data for
            database_id: ID of the database to query
            user_id: ID of the user making the request
            database_info: Pre-fetched database schema information

        Returns:
            Tuple of (List of chart data points, SQL query used)
        """
        try:
            # Create cache key for this specific query
            import hashlib
            query_hash = hashlib.md5(f"{context.user_query}:{chart_type.value}:{database_id}".encode()).hexdigest()
            cache_key = f"chart_query_result:{user_id}:{query_hash}"

            # Check if we have cached results
            cached_result = self.cache_service.get_result_data(cache_key)
            if cached_result:
                logger.info(f"Using cached chart query result for user {user_id}")
                return cached_result["data_points"], cached_result["sql_query"]

            # Generate SQL query using pre-fetched database info
            sql_query = await self._generate_sql_for_chart_with_schema(context.user_query, database_info, chart_type)
            if not sql_query:
                raise ValueError("Failed to generate SQL query. Please try rephrasing your question or check if your database has the required data.")

            # Execute query and transform results
            query_result = await self._execute_database_query(database_id, user_id, sql_query)
            if query_result is None:
                raise ValueError("Failed to execute database query. Please check your database connection and permissions.")

            # Transform results to chart data points
            data_points = self._transform_query_result_to_structured_data(query_result, chart_type)

            # Post-process for single data points (convert to number chart if appropriate)
            self._post_process_single_data_point(context, data_points, chart_type)

            # Cache the result for 5 minutes
            cache_result = {
                "data_points": data_points,
                "sql_query": sql_query
            }
            self.cache_service.cache_result_data(cache_key, cache_result, ttl=300)
            logger.info(f"Cached chart query result for user {user_id}")

            logger.info(f"Successfully generated chart data from database {database_id}")
            return data_points, sql_query

        except Exception as e:
            logger.error(f"Error generating chart data from database: {e}")
            raise

    async def _get_database_info(self, user_id: str, database_id: str) -> Optional[Dict[str, Any]]:
        """Get and validate database information with aggressive caching."""
        try:
            # Check if we have cached schema info for this specific database
            cached_db_info = self.cache_service.get_schema_info(database_id, f"user_{user_id}")

            if cached_db_info:
                logger.info(f"Using cached database schema for {database_id} (user {user_id})")
                return cached_db_info

            # Only get schema for the specific database we need, not all databases
            db_info = await self._get_single_database_schema(user_id, database_id)

            if db_info:
                # Cache the database info for 10 minutes (longer than default)
                self.cache_service.cache_schema_info(database_id, db_info, f"user_{user_id}", ttl=600)
                logger.info(f"Cached database schema for {database_id} with {len(db_info.get('tables', []))} tables")

            return db_info

        except Exception as e:
            logger.error(f"Failed to get database schema for user {user_id}: {e}")
            return None

    async def _get_single_database_schema(self, user_id: str, database_id: str) -> Optional[Dict[str, Any]]:
        """Get schema for a single database without fetching all user databases."""
        try:
            # Get the specific database credentials
            database = await self.database_manager.credential_service.get_credentials(user_id, database_id)

            if not database:
                logger.warning(f"Database {database_id} not found for user {user_id}")
                return None

            # Connect to the specific database
            success, error_msg = await self.database_manager.database_service.connect_database(database)
            if not success:
                logger.error(f"Failed to connect to database {database_id}")
                return None

            try:
                # Get table list with caching
                table_names = await self.database_manager.database_service.list_tables(
                    database_id, schema=database.credentials.db_schema
                )

                if not table_names:
                    logger.warning(f"Database {database_id} has no tables")
                    return None

                # Build database info with essential metadata only
                db_info = {
                    "id": database.id,
                    "name": database.name,
                    "type": database.db_type.value,
                    "tables": []
                }

                # Get lightweight table metadata (skip row counts for performance)
                for table_name in table_names[:50]:  # Limit to first 50 tables for performance
                    try:
                        table_info = {
                            "name": table_name,
                            "schema": database.credentials.db_schema,
                            "columns": []
                        }

                        # Get column metadata only (skip row counts and foreign keys for speed)
                        table_metadata = await self.database_manager.database_service.get_table_metadata(
                            database_id, table_name, schema=database.credentials.db_schema
                        )

                        if table_metadata and table_metadata.columns:
                            for col in table_metadata.columns:
                                table_info["columns"].append({
                                    "name": col.name,
                                    "type": col.data_type,
                                    "nullable": col.is_nullable,
                                    "primary_key": col.is_primary_key
                                })

                        db_info["tables"].append(table_info)

                    except Exception as table_error:
                        logger.warning(f"Error getting metadata for table {table_name}: {str(table_error)}")
                        # Add basic table info even if metadata fails
                        db_info["tables"].append({
                            "name": table_name,
                            "schema": database.credentials.db_schema,
                            "columns": []
                        })

                logger.info(f"Retrieved schema for database {database_id}: {len(db_info['tables'])} tables")
                return db_info

            finally:
                # Keep connection open for potential chart query
                pass

        except Exception as e:
            logger.error(f"Error getting single database schema for {database_id}: {str(e)}")
            return None

    async def _execute_database_query(self, database_id: str, user_id: str, sql_query: str) -> Any:
        """Execute SQL query on the database."""
        try:
            # Get database credentials
            database_obj = await self.database_manager.credential_service.get_credentials(user_id, database_id)
            if not database_obj:
                logger.error(f"Database {database_id} not found for user {user_id}")
                return None

            # Establish connection
            success, error = await self.database_service.connect_database(database_obj, user_id=user_id)
            if not success:
                logger.error(f"Failed to connect to database {database_id}: {error}")
                return None

            # Execute query
            return await self.database_service.execute_query(database_id, sql_query)

        except Exception as e:
            logger.error(f"Failed to execute SQL query on database {database_id}: {e}")
            return None

    def _post_process_single_data_point(self, context: ChartGenerationContext, data_points: Any, chart_type: ChartType) -> None:
        """Post-process single data points for better UX."""
        # Check if we have structured data that represents a single value
        should_convert = False
        
        if isinstance(data_points, BarChartData) and len(data_points.data) == 1:
            should_convert = True
        elif isinstance(data_points, PieChartData) and len(data_points.data) == 1:
            should_convert = True
        elif isinstance(data_points, LineChartData) and len(data_points.data) == 1:
            should_convert = True
        elif isinstance(data_points, list) and len(data_points) == 1:
            should_convert = self._should_convert_to_number_chart(context.user_query, data_points[0])
        
        if (should_convert and
            chart_type not in [ChartType.NUMBER, ChartType.TABLE, ChartType.TEXT, ChartType.IMAGE, ChartType.DETAIL]):
            
            logger.info(f"Converting single data point from {chart_type} to NUMBER chart for better UX")
            context.recommended_chart_type = ChartType.NUMBER

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def _generate_fallback_structured_data(self, chart_type: ChartType) -> Union[
        PieChartData, BarChartData, LineChartData, TimeBarChartData, NumberChartData,
        TableChartData, FunnelChartData, ActivityChartData, TextChartData,
        ImageChartData, DetailChartData, List[ChartDataPoint]
    ]:
        """Generate fallback structured data when query transformation fails."""
        if chart_type == ChartType.PIE:
            return PieChartData(data=[{"name": "No Data", "value": 100}])
        elif chart_type == ChartType.BAR:
            return BarChartData(data=[{"name": "No Data", "value": 0}])
        elif chart_type == ChartType.LINE:
            return LineChartData(data=[{"name": "No Data", "value": 0}])
        elif chart_type == ChartType.TIMEBAR:
            return TimeBarChartData(data=[{"time": "No Data", "value": 0}])
        elif chart_type == ChartType.NUMBER:
            return NumberChartData(value=0, name="No Data", formatType="number")
        elif chart_type == ChartType.TABLE:
            return TableChartData(
                columns=[{"key": "status", "label": "Status"}],
                data=[{"status": "No Data Available"}]
            )
        elif chart_type == ChartType.FUNNEL:
            return FunnelChartData(data=[{"name": "No Data", "value": 0}])
        elif chart_type == ChartType.ACTIVITY:
            return ActivityChartData(data=[{"time": "No Data", "count": 0}])
        elif chart_type == ChartType.TEXT:
            return TextChartData(content="No data available", formatType="plain")
        elif chart_type == ChartType.IMAGE:
            return ImageChartData(imageUrl="", altText="No image available")
        elif chart_type == ChartType.DETAIL:
            return DetailChartData(title="No Data", data=[{"Status": "No data available"}])
        else:
            return [ChartDataPoint(name="No Data", value=0)]

    def _should_convert_to_number_chart(self, user_query: str, data_point: ChartDataPoint) -> bool:
        """
        Determine if a single data point should be displayed as a number chart instead of other chart types.

        Args:
            user_query: The original user query
            data_point: The single data point returned

        Returns:
            True if should convert to number chart, False otherwise
        """
        query_lower = user_query.lower()
        logger.info(f"Checking conversion for query: '{user_query}' with data point: {data_point}")

        # Keywords that suggest a single metric/number is expected
        number_keywords = [
            'total', 'count', 'sum', 'average', 'mean', 'median', 'max', 'min',
            'how many', 'how much', 'number of', 'amount of', 'size of',
            'revenue', 'profit', 'cost', 'price', 'value', 'score', 'rating',
            'percentage', 'percent', 'rate'
        ]

        # Check if query contains number-indicating keywords
        has_number_keywords = any(keyword in query_lower for keyword in number_keywords)
        logger.info(f"Query has number keywords: {has_number_keywords}")

        # Check if the data point name suggests it's a single metric
        name_lower = data_point.name.lower()
        single_metric_names = ['total', 'count', 'sum', 'average', 'value', 'metric', 'score']
        has_metric_name = any(name in name_lower for name in single_metric_names)
        logger.info(f"Data point name '{data_point.name}' has metric name: {has_metric_name}")

        # For now, let's be more aggressive and convert any single data point to number
        # This will help with the current case where "Show me users by role" returns one result
        result = has_number_keywords or has_metric_name or True  # Always convert single points for now
        logger.info(f"Final conversion decision: {result}")

        return result

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def _create_chart_metadata(self, context: ChartGenerationContext, recommendation: ChartTypeRecommendation, sql_query: Optional[str] = None) -> ChartMetadata:
        """
        Create chart metadata based on context and recommendation.

        Args:
            context: Chart generation context
            recommendation: Chart type recommendation
            sql_query: Optional SQL query used to generate the data

        Returns:
            ChartMetadata object
        """
        # Generate axis labels based on chart type and query
        x_axis_label = self._generate_axis_label(context.user_query, "x", recommendation.chart_type)
        y_axis_label = self._generate_axis_label(context.user_query, "y", recommendation.chart_type)

        # Data source is always real database query
        data_source = "Real Database Query"

        return ChartMetadata(
            xAxisLabel=x_axis_label,
            yAxisLabel=y_axis_label,
            colors=DEFAULT_CHART_COLORS[:6],  # Use first 6 colors
            sqlQuery=sql_query,
            description=None,
            dataSource=data_source,
            generatedAt=datetime.now().isoformat()
        )

    def _generate_axis_label(self, query: str, axis: str, chart_type: ChartType) -> Optional[str]:
        """
        Generate appropriate axis labels based on query and chart type.

        Args:
            query: User query
            axis: "x" or "y"
            chart_type: Type of chart

        Returns:
            Axis label or None
        """
        query_lower = query.lower()

        if chart_type in [ChartType.PIE, ChartType.NUMBER, ChartType.TEXT, ChartType.IMAGE, ChartType.DETAIL, ChartType.TABLE]:
            return None  # These chart types don't use axis labels

        if axis == "x":
            if any(word in query_lower for word in ['time', 'date', 'month', 'year', 'day']):
                return "Time Period"
            elif any(word in query_lower for word in ['category', 'type', 'group']):
                return "Category"
            else:
                return "Categories"
        else:  # y-axis
            if any(word in query_lower for word in ['count', 'number', 'total']):
                return "Count"
            elif any(word in query_lower for word in ['revenue', 'sales', 'amount', 'value']):
                return "Value"
            elif any(word in query_lower for word in ['percentage', 'percent', '%']):
                return "Percentage"
            else:
                return "Value"

    # ============================================================================
    # SQL GENERATION AND DATABASE OPERATIONS
    # ============================================================================

    async def _generate_sql_for_chart_with_schema(self, user_query: str, database_info: Dict[str, Any], chart_type: ChartType) -> Optional[str]:
        """
        Generate SQL query for chart data using LLM with actual database schema.

        Args:
            user_query: User's natural language query
            database_info: Database information including tables and columns
            chart_type: Type of chart being generated

        Returns:
            SQL query string or None if generation fails
        """
        try:
            # Build schema context from the database info
            schema_context = self._build_schema_context(database_info)

            # Get chart-specific SQL requirements
            chart_requirements = CHART_SQL_REQUIREMENTS.get(chart_type, CHART_SQL_REQUIREMENTS[ChartType.BAR])
            
            system_prompt = f"""You are a SQL expert. Generate a {database_info['type']} SQL query to answer the user's question.

Database Schema:
{schema_context}

Chart Type: {chart_type.value}
Required Output: {chart_requirements['description']}
Column Requirements: {chart_requirements['columns']} columns
Additional Requirements:
{chr(10).join(f"- {req}" for req in chart_requirements['requirements'])}

CRITICAL SQL Requirements:
1. Return ONLY the SQL query, no explanations
2. Use proper {database_info['type']} syntax
3. MUST return exactly {chart_requirements['columns']} columns (unless specified as "2+" or "1-2")
4. Use aggregate functions (COUNT, SUM, AVG) when appropriate
5. Order results appropriately for the chart type
6. Handle NULL values appropriately
7. IMPORTANT: Do NOT use column aliases in WHERE, GROUP BY, or HAVING clauses
8. If using HAVING, reference the full expression, not the alias

CRITICAL: If you need HAVING clause, use the full expression:
- CORRECT: "HAVING SUM(amount) > 0"
- INCORRECT: "HAVING value > 0" (when value is an alias)

For {chart_type.value} charts specifically:
- Column 1: {chart_requirements['description'].split(',')[0].split('SELECT')[1].strip()}
- Expected result: meaningful data that can be visualized as a {chart_type.value} chart
"""

            user_prompt = f"User question: {user_query}\n\nGenerate the SQL query:"

            # Generate and clean SQL query
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1
            )

            sql_query = self._clean_sql_response(response)

            if not self._validate_sql_query(sql_query):
                return None

            logger.info(f"Generated clean SQL query: {sql_query}")
            return sql_query

        except Exception as e:
            logger.error(f"Error generating SQL query with schema: {e}")
            return None

    def _build_schema_context(self, database_info: Dict[str, Any]) -> str:
        """Build a readable schema context from database info."""
        schema_lines = []
        schema_lines.append(f"Database: {database_info['name']} ({database_info['type']})")
        schema_lines.append("")

        for table in database_info.get('tables', []):
            table_name = table['name']
            columns = table.get('columns', [])

            schema_lines.append(f"Table: {table_name}")
            if columns:
                schema_lines.append("Columns:")
                for col in columns:
                    schema_lines.append(f"  - {col}")
            else:
                schema_lines.append("  (No column information available)")
            schema_lines.append("")

        return "\n".join(schema_lines)

    def _clean_sql_response(self, response: str) -> str:
        """Clean SQL response from LLM by removing markdown formatting."""
        sql_query = response.strip()

        # Remove markdown code blocks
        if sql_query.startswith('```sql'):
            sql_query = sql_query[6:]
        elif sql_query.startswith('```'):
            sql_query = sql_query[3:]

        if sql_query.endswith('```'):
            sql_query = sql_query[:-3]

        return sql_query.strip()

    def _validate_sql_query(self, sql_query: str) -> bool:
        """Validate that the SQL query looks correct."""
        if not sql_query or not sql_query.upper().startswith('SELECT'):
            logger.warning(f"Generated SQL doesn't look valid: {sql_query}")
            return False
        return True

    # ============================================================================
    # DATA TRANSFORMATION
    # ============================================================================

    def _transform_query_result_to_structured_data(self, query_result: Any, chart_type: ChartType) -> Union[
        PieChartData, BarChartData, LineChartData, TimeBarChartData, NumberChartData,
        TableChartData, FunnelChartData, ActivityChartData, TextChartData,
        ImageChartData, DetailChartData, List[ChartDataPoint]
    ]:
        """
        Transform database query results into chart-specific structured data.

        Args:
            query_result: Result from database query (DataFrame or list of dicts)
            chart_type: Type of chart being generated

        Returns:
            Structured data in the appropriate format for the chart type
        """
        try:
            logger.debug(f"Transforming query result for {chart_type}: type={type(query_result)}")

            # Convert result to DataFrame if it's not already
            if hasattr(query_result, 'to_dict'):
                df = query_result
            elif isinstance(query_result, list) and query_result:
                df = pd.DataFrame(query_result)
            else:
                logger.warning(f"Query result is empty or invalid format: {type(query_result)}")
                return self._generate_fallback_structured_data(chart_type)

            if df.empty:
                logger.warning("Query returned no data")
                return self._generate_fallback_structured_data(chart_type)

            logger.debug(f"DataFrame columns: {df.columns.tolist()}")
            logger.debug(f"DataFrame shape: {df.shape}")

            # Transform based on chart type
            if chart_type == ChartType.PIE:
                return self._transform_to_pie_data(df)
            elif chart_type == ChartType.BAR:
                return self._transform_to_bar_data(df)
            elif chart_type == ChartType.LINE:
                return self._transform_to_line_data(df)
            elif chart_type == ChartType.TIMEBAR:
                return self._transform_to_timebar_data(df)
            elif chart_type == ChartType.NUMBER:
                return self._transform_to_number_data(df)
            elif chart_type == ChartType.TABLE:
                return self._transform_to_table_data(df)
            elif chart_type == ChartType.FUNNEL:
                return self._transform_to_funnel_data(df)
            elif chart_type == ChartType.ACTIVITY:
                return self._transform_to_activity_data(df)
            elif chart_type == ChartType.TEXT:
                return self._transform_to_text_data(df)
            elif chart_type == ChartType.IMAGE:
                return self._transform_to_image_data(df)
            elif chart_type == ChartType.DETAIL:
                return self._transform_to_detail_data(df)
            else:
                # Fallback to legacy format
                return self._transform_to_legacy_data_points(df)

        except Exception as e:
            logger.error(f"Error transforming query result to structured data: {e}")
            return self._generate_fallback_structured_data(chart_type)

    def _transform_to_pie_data(self, df) -> PieChartData:
        """Transform DataFrame to pie chart data - Recharts compatible format."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Pie charts require at least 2 columns")
        
        data = []
        for _, row in df.iterrows():
            name = str(row[columns[0]])
            value = float(row[columns[1]])
            data.append({"name": name, "value": value})
            
        return PieChartData(data=data)

    def _transform_to_bar_data(self, df) -> BarChartData:
        """Transform DataFrame to bar chart data - Recharts compatible format."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Bar charts require at least 2 columns")
        
        data = []
        for _, row in df.iterrows():
            name = str(row[columns[0]])
            value = float(row[columns[1]])
            data.append({"name": name, "value": value})
        
        return BarChartData(
            data=data,
            categoryKey="name",
            valueKey="value"
        )

    def _transform_to_line_data(self, df) -> LineChartData:
        """Transform DataFrame to line chart data - Recharts compatible format."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Line charts require at least 2 columns")
        
        data = []
        for _, row in df.iterrows():
            name = str(row[columns[0]])
            value = float(row[columns[1]])
            data.append({"name": name, "value": value})
        
        return LineChartData(
            data=data,
            xKey="name",
            yKey="value"
        )

    def _transform_to_timebar_data(self, df) -> TimeBarChartData:
        """Transform DataFrame to time bar chart data - Recharts compatible format."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Time bar charts require at least 2 columns")
        
        data = []
        for _, row in df.iterrows():
            time = str(row[columns[0]])
            value = float(row[columns[1]])
            data.append({"time": time, "value": value})
        
        return TimeBarChartData(
            data=data,
            timeKey="time",
            valueKey="value"
        )

    def _transform_to_number_data(self, df) -> NumberChartData:
        """Transform DataFrame to number chart data."""
        # Get the first numeric value
        for col in df.columns:
            if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                value = float(df[col].iloc[0])
                name = f"Total {col.replace('_', ' ').title()}"
                
                # Determine format type based on value and column name
                col_name_lower = col.lower()
                if any(word in col_name_lower for word in ['amount', 'price', 'cost', 'revenue', 'sales']):
                    format_type = "currency"
                elif any(word in col_name_lower for word in ['percent', 'rate', 'ratio']) or '%' in col_name_lower:
                    format_type = "percentage"
                else:
                    format_type = "number"
                    
                return NumberChartData(
                    value=value, 
                    name=name, 
                    formatType=format_type,
                    color=DEFAULT_CHART_COLORS[0]
                )
        
        # Fallback
        return NumberChartData(value=0, name="No Data", formatType="number")

    def _transform_to_table_data(self, df) -> TableChartData:
        """Transform DataFrame to table chart data - Recharts compatible format."""
        columns = [
            {"key": str(col), "label": str(col).replace('_', ' ').title()} 
            for col in df.columns.tolist()
        ]
        
        data = []
        for _, row in df.iterrows():
            row_data = {}
            for col in df.columns:
                row_data[str(col)] = str(row[col]) if pd.notna(row[col]) else ""
            data.append(row_data)
        
        return TableChartData(columns=columns, data=data)

    def _transform_to_funnel_data(self, df) -> FunnelChartData:
        """Transform DataFrame to funnel chart data - Recharts compatible format."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Funnel charts require at least 2 columns")
        
        data = []
        for _, row in df.iterrows():
            name = str(row[columns[0]])
            value = float(row[columns[1]])
            data.append({"name": name, "value": value})
        
        return FunnelChartData(
            data=data,
            nameKey="name",
            valueKey="value"
        )

    def _transform_to_activity_data(self, df) -> ActivityChartData:
        """Transform DataFrame to activity chart data - Recharts compatible format."""
        columns = df.columns.tolist()
        if len(columns) < 2:
            raise ValueError("Activity charts require at least 2 columns")
        
        data = []
        for _, row in df.iterrows():
            time = str(row[columns[0]])
            count = int(float(row[columns[1]]))
            data.append({"time": time, "count": count})
        
        return ActivityChartData(
            data=data,
            timeKey="time",
            countKey="count"
        )

    def _transform_to_text_data(self, df) -> TextChartData:
        """Transform DataFrame to text chart data."""
        # Combine all text content
        content_parts = []
        for _, row in df.iterrows():
            row_text = " ".join([str(val) for val in row.tolist() if pd.notna(val)])
            content_parts.append(row_text)
        
        content = "\n".join(content_parts)
        return TextChartData(content=content, formatType="plain")

    def _transform_to_image_data(self, df) -> ImageChartData:
        """Transform DataFrame to image chart data."""
        columns = df.columns.tolist()
        image_url = str(df[columns[0]].iloc[0]) if not df.empty else ""
        caption = str(df[columns[1]].iloc[0]) if len(columns) > 1 and not df.empty else None
        
        return ImageChartData(
            imageUrl=image_url,
            altText=caption,
            caption=caption
        )

    def _transform_to_detail_data(self, df) -> DetailChartData:
        """Transform DataFrame to detail chart data - Recharts compatible format."""
        data = []
        
        for _, row in df.iterrows():
            row_data = {}
            for col, val in row.items():
                if pd.notna(val):
                    row_data[str(col).replace('_', ' ').title()] = str(val)
            data.append(row_data)
        
        return DetailChartData(
            title="Detailed Information",
            data=data
        )

    def _transform_to_legacy_data_points(self, df) -> List[ChartDataPoint]:
        """Transform DataFrame to legacy ChartDataPoint format for backward compatibility."""
        columns = df.columns.tolist()
        data_points = []
        
        if len(columns) >= 2:
            name_col = columns[0]
            value_col = columns[1]
            
            for _, row in df.head(20).iterrows():
                try:
                    name = str(row[name_col])
                    value = float(row[value_col])
                    data_points.append(ChartDataPoint(name=name, value=value))
                except (ValueError, TypeError):
                    continue
        
        return data_points if data_points else [ChartDataPoint(name="No Data", value=0)]

    def _convert_to_legacy_format(self, data_points: Any, chart_type: ChartType) -> List[ChartDataPoint]:
        """
        Convert structured data objects to legacy ChartDataPoint format for frontend compatibility.

        Args:
            data_points: Structured data object (BarChartData, PieChartData, etc.) or legacy format
            chart_type: Type of chart

        Returns:
            List of ChartDataPoint objects that the frontend expects
        """
        try:
            # If it's already in legacy format, return as-is
            if isinstance(data_points, list):
                return data_points

            # Convert structured data to legacy format based on type
            if isinstance(data_points, BarChartData):
                return [ChartDataPoint(name=item.get("name", ""), value=item.get("value", 0))
                       for item in data_points.data]

            elif isinstance(data_points, PieChartData):
                return [ChartDataPoint(name=item.get("name", ""), value=item.get("value", 0))
                       for item in data_points.data]

            elif isinstance(data_points, LineChartData):
                return [ChartDataPoint(name=item.get("name", ""), value=item.get("value", 0))
                       for item in data_points.data]

            elif isinstance(data_points, TimeBarChartData):
                return [ChartDataPoint(name=item.get("time", ""), value=item.get("value", 0))
                       for item in data_points.data]

            elif isinstance(data_points, NumberChartData):
                return [ChartDataPoint(name=data_points.name, value=data_points.value)]

            elif isinstance(data_points, FunnelChartData):
                return [ChartDataPoint(name=item.get("name", ""), value=item.get("value", 0))
                       for item in data_points.data]

            elif isinstance(data_points, ActivityChartData):
                return [ChartDataPoint(name=item.get("time", ""), value=item.get("count", 0))
                       for item in data_points.data]

            elif isinstance(data_points, TableChartData):
                # For tables, create a simplified representation
                if data_points.data:
                    return [ChartDataPoint(name=f"Row {i+1}", value=len(row))
                           for i, row in enumerate(data_points.data[:10])]  # Limit to 10 rows
                else:
                    return [ChartDataPoint(name="No Data", value=0)]

            elif isinstance(data_points, TextChartData):
                # For text, create a single data point
                return [ChartDataPoint(name="Text Content", value=len(data_points.content))]

            elif isinstance(data_points, ImageChartData):
                # For images, create a single data point
                return [ChartDataPoint(name="Image", value=1)]

            elif isinstance(data_points, DetailChartData):
                # For details, create data points from data
                if data_points.data:
                    return [ChartDataPoint(name=f"Section {i+1}", value=len(section))
                           for i, section in enumerate(data_points.data[:10])]
                else:
                    return [ChartDataPoint(name="No Data", value=0)]

            else:
                logger.warning(f"Unknown data type for conversion: {type(data_points)}")
                return [ChartDataPoint(name="Unknown Data", value=0)]

        except Exception as e:
            logger.error(f"Error converting to legacy format: {e}")
            return [ChartDataPoint(name="Error", value=0)]
