{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/providers/DataSourcesContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';\r\nimport { useApi, ConnectedDatabase } from '@/providers/ApiContext';\r\n\r\ninterface DataSource {\r\n  id: string;\r\n  name: string;\r\n  type: 'database' | 'file' | 'cloud' | 'other';\r\n  description: string;\r\n  isConnected: boolean;\r\n}\r\n\r\ninterface DataSourcesContextType {\r\n  dataSources: DataSource[];\r\n  connectedDataSources: DataSource[];\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  connectDataSource: (sourceId: string) => Promise<void>;\r\n  disconnectDataSource: (sourceId: string) => Promise<void>;\r\n  getDataSource: (sourceId: string) => DataSource | undefined;\r\n  refreshDataSources: () => Promise<void>;\r\n}\r\n\r\nconst DataSourcesContext = createContext<DataSourcesContextType | undefined>(undefined);\r\n\r\nexport const useDataSources = () => {\r\n  const context = useContext(DataSourcesContext);\r\n  if (!context) {\r\n    throw new Error('useDataSources must be used within a DataSourcesProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface DataSourcesProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const DataSourcesProvider: React.FC<DataSourcesProviderProps> = ({ children }) => {\r\n  const { listDatabases, disconnectExistingDatabase } = useApi();\r\n  const [dataSources, setDataSources] = useState<DataSource[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Convert ConnectedDatabase to DataSource format\r\n  const convertConnectedDatabase = useCallback((db: ConnectedDatabase): DataSource => ({\r\n    id: db.id,\r\n    name: db.name,\r\n    type: 'database', // All connected databases are database type\r\n    description: db.description || `${db.type} database`,\r\n    isConnected: true, // All returned databases are connected\r\n  }), []);\r\n\r\n  // Fetch connected databases\r\n  const fetchConnectedDatabases = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    \r\n    try {\r\n      console.log('Fetching connected databases...');\r\n      const connectedDbs = await listDatabases();\r\n      const dataSources = connectedDbs.map(convertConnectedDatabase);\r\n      setDataSources(dataSources);\r\n      setIsInitialized(true);\r\n    } catch (err) {\r\n      console.error('Failed to fetch connected databases:', err);\r\n      setError(err instanceof Error ? err.message : 'Failed to fetch connected databases');\r\n      // Don't set mock data - let the UI handle empty state\r\n      setDataSources([]);\r\n      setIsInitialized(true);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [listDatabases, convertConnectedDatabase]);\r\n\r\n  // Initialize data sources on mount - only once\r\n  useEffect(() => {\r\n    if (!isInitialized) {\r\n      fetchConnectedDatabases();\r\n    }\r\n  }, [fetchConnectedDatabases, isInitialized]);\r\n\r\n  // Get only connected data sources (which should be all of them now)\r\n  const connectedDataSources = dataSources.filter(source => source.isConnected);\r\n\r\n  // Connect a data source (not implemented - would need connection parameters)\r\n  const connectDataSource = useCallback(async (sourceId: string) => {\r\n    // This would require a connection form/modal to collect database credentials\r\n    // For now, we'll just refresh the list\r\n    console.log('Connect data source not implemented - use the Data Sources page');\r\n    setError('Please use the Data Sources page to connect new databases');\r\n  }, []);\r\n\r\n  // Disconnect a data source\r\n  const disconnectDataSource = useCallback(async (sourceId: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    \r\n    try {\r\n      // Use the real API to disconnect\r\n      await disconnectExistingDatabase(sourceId);\r\n      \r\n      // Refresh the list\r\n      await fetchConnectedDatabases();\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to disconnect data source');\r\n      setIsLoading(false);\r\n    }\r\n  }, [disconnectExistingDatabase, fetchConnectedDatabases]);\r\n\r\n  // Get a specific data source by ID\r\n  const getDataSource = useCallback((sourceId: string) => {\r\n    return dataSources.find(source => source.id === sourceId);\r\n  }, [dataSources]);\r\n\r\n  // Manual refresh function\r\n  const refreshDataSources = useCallback(async () => {\r\n    await fetchConnectedDatabases();\r\n  }, [fetchConnectedDatabases]);\r\n\r\n  const value: DataSourcesContextType = {\r\n    dataSources,\r\n    connectedDataSources,\r\n    isLoading,\r\n    error,\r\n    connectDataSource,\r\n    disconnectDataSource,\r\n    getDataSource,\r\n    refreshDataSources,\r\n  };\r\n\r\n  return (\r\n    <DataSourcesContext.Provider value={value}>\r\n      {children}\r\n    </DataSourcesContext.Provider>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAqBA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsC;AAEtE,MAAM,iBAAiB;IAC5B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,sBAA0D,CAAC,EAAE,QAAQ,EAAE;IAClF,MAAM,EAAE,aAAa,EAAE,0BAA0B,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,iDAAiD;IACjD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,KAAsC,CAAC;YACnF,IAAI,GAAG,EAAE;YACT,MAAM,GAAG,IAAI;YACb,MAAM;YACN,aAAa,GAAG,WAAW,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;YACpD,aAAa;QACf,CAAC,GAAG,EAAE;IAEN,4BAA4B;IAC5B,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,aAAa;QACb,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,MAAM;YAC3B,MAAM,cAAc,aAAa,GAAG,CAAC;YACrC,eAAe;YACf,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wCAAwC;YACtD,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,sDAAsD;YACtD,eAAe,EAAE;YACjB,iBAAiB;QACnB,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAe;KAAyB;IAE5C,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;YAClB;QACF;IACF,GAAG;QAAC;QAAyB;KAAc;IAE3C,oEAAoE;IACpE,MAAM,uBAAuB,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW;IAE5E,6EAA6E;IAC7E,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,6EAA6E;QAC7E,uCAAuC;QACvC,QAAQ,GAAG,CAAC;QACZ,SAAS;IACX,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC9C,aAAa;QACb,SAAS;QAET,IAAI;YACF,iCAAiC;YACjC,MAAM,2BAA2B;YAEjC,mBAAmB;YACnB,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,aAAa;QACf;IACF,GAAG;QAAC;QAA4B;KAAwB;IAExD,mCAAmC;IACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IAClD,GAAG;QAAC;KAAY;IAEhB,0BAA0B;IAC1B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM;IACR,GAAG;QAAC;KAAwB;IAE5B,MAAM,QAAgC;QACpC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;kBACjC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode } from 'react';\r\nimport { DataSourcesProvider } from \"@/providers/DataSourcesContext\";\r\n\r\ninterface DashboardLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {\r\n  return (\r\n    <DataSourcesProvider>\r\n      {children}\r\n    </DataSourcesProvider>\r\n  );\r\n};\r\n\r\nexport default DashboardLayout; "], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,qBACE,8OAAC,uIAAA,CAAA,sBAAmB;kBACjB;;;;;;AAGP;uCAEe", "debugId": null}}]}