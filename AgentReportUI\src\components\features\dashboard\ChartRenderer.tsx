"use client";

import React, { useRef, useEffect, useState } from 'react';
import {
  Bar,
  BarChart,
  Line,
  LineChart,
  Pie,
  PieChart,
  Area,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
  Tooltip,
} from 'recharts';
import { ChartData, DEFAULT_CHART_COLORS } from '@/types';
import { useTheme } from 'next-themes';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

interface ChartRendererProps {
  chartData: ChartData;
  width?: number;
  height?: number;
  className?: string;
}

// Responsive font size based on container dimensions
const getResponsiveFontSize = (width?: number, height?: number) => {
  if (!width || !height) return 10;
  const minDimension = Math.min(width, height);
  if (minDimension < 200) return 8;
  if (minDimension < 300) return 9;
  if (minDimension < 400) return 10;
  return 11;
};

// Responsive axis dimensions with better handling for small heights
const getAxisDimensions = (width?: number, height?: number) => {
  if (!width || !height) return { xHeight: 20, yWidth: 25 };
  
  // For very small heights, reduce axis dimensions significantly
  if (height < 150) return { xHeight: 12, yWidth: 15 };
  if (height < 200) return { xHeight: 15, yWidth: 18 };
  if (height < 300) return { xHeight: 18, yWidth: 22 };
  return { xHeight: 20, yWidth: 25 };
};

// Calculate optimal margins based on container size
const getOptimalMargins = (width?: number, height?: number, chartType?: string) => {
  if (!width || !height) return { top: 5, right: 5, left: 5, bottom: 5 };
  
  // For bar charts in very small containers, use minimal margins
  if (chartType === 'bar' && height < 150) {
    return { top: 2, right: 2, left: 2, bottom: 2 };
  }
  
  if (height < 200) {
    return { top: 3, right: 3, left: 3, bottom: 3 };
  }
  
  return { top: 5, right: 5, left: 5, bottom: 5 };
};

const ChartRenderer: React.FC<ChartRendererProps> = ({
  chartData,
  width,
  height,
  className = '',
}) => {
  const { chartType, data, metadata } = chartData;
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  // Responsive sizing
  const fontSize = getResponsiveFontSize(width, height);
  const { xHeight, yWidth } = getAxisDimensions(width, height);
  const margins = getOptimalMargins(width, height, chartType);

  // Theme-aware colors
  const getThemeColors = () => ({
    grid: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',
    text: isDark ? 'hsl(215 20.2% 65.1%)' : 'hsl(215.4 16.3% 46.9%)',
    tooltipBg: isDark ? 'hsl(222.2 84% 4.9%)' : 'hsl(0 0% 100%)',
    tooltipBorder: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',
    tooltipText: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)',
    primary: isDark ? 'hsl(217.2 91.2% 59.8%)' : 'hsl(221.2 83.2% 53.3%)',
  });

  const themeColors = getThemeColors();

  // For very small heights, consider hiding some elements
  const showGrid = height ? height > 120 : true;
  const showXAxis = height ? height > 100 : true;
  const showYAxis = height ? height > 100 : true;

  // Helper function to get chart data in the right format
  const getChartData = () => {
    if (Array.isArray(data)) {
      return data;
    }
    if (data && typeof data === 'object' && 'data' in data) {
      return (data as any).data;
    }
    return [];
  };

  const renderChart = () => {
    const chartData = getChartData();

    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={margins}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />}
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  height={xHeight}
                  interval={width && width < 200 ? 'preserveStartEnd' : 0}
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  width={yWidth}
                  tickCount={height && height < 150 ? 3 : 5}
                />
              )}
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar
                dataKey="value"
                fill={metadata.colors?.[0] || themeColors.primary}
                radius={height && height < 150 ? [1, 1, 0, 0] : [2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={margins}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />}
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  height={xHeight}
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  width={yWidth}
                />
              )}
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={metadata.colors?.[0] || themeColors.primary}
                strokeWidth={2}
                dot={{ fill: metadata.colors?.[0] || themeColors.primary, strokeWidth: 0, r: 3 }}
                activeDot={{ r: 5, fill: metadata.colors?.[0] || themeColors.primary }}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData} margin={margins}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />}
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  height={xHeight}
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  width={yWidth}
                />
              )}
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke={metadata.colors?.[0] || themeColors.primary}
                fill={metadata.colors?.[0] || themeColors.primary}
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={width && width < 250 ? false : ({ name, percent }) => `${name} ${percent ? (percent * 100).toFixed(0) : 0}%`}
                outerRadius="90%"
                innerRadius="35%"
                fill={themeColors.primary}
                dataKey="value"
                nameKey="name"
                stroke={themeColors.grid}
                strokeWidth={1}
              >
                {chartData.map((_: any, index: number) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={metadata.colors?.[index] || DEFAULT_CHART_COLORS[index % DEFAULT_CHART_COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      case 'timebar':
        // Use time as X-axis, similar to bar chart but with time formatting
        const timeData = chartData.map((item: any) => ({
          name: item.time || item.name,
          value: item.value
        }));
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={timeData} margin={margins}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />}
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  height={xHeight}
                  interval={width && width < 200 ? 'preserveStartEnd' : 0}
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  width={yWidth}
                />
              )}
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar
                dataKey="value"
                fill={metadata.colors?.[0] || themeColors.primary}
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'funnel':
        // Custom funnel chart implementation using SVG
        const funnelTotal = chartData.reduce((sum: number, item: any) => sum + item.value, 0);
        const funnelHeight = height ? height - 40 : 300;
        const funnelWidth = width ? width - 40 : 400;
        
        return (
          <div className="w-full h-full flex items-center justify-center p-4">
            <svg width={funnelWidth} height={funnelHeight} viewBox={`0 0 ${funnelWidth} ${funnelHeight}`}>
              {chartData.map((item: any, index: number) => {
                const percentage = item.value / funnelTotal;
                const segmentHeight = funnelHeight / chartData.length;
                const topWidth = funnelWidth * (1 - index * 0.15);
                const bottomWidth = funnelWidth * (1 - (index + 1) * 0.15);
                const yPosition = index * segmentHeight;
                
                return (
                  <g key={index}>
                    <path
                      d={`M ${(funnelWidth - topWidth) / 2} ${yPosition} 
                          L ${(funnelWidth + topWidth) / 2} ${yPosition}
                          L ${(funnelWidth + bottomWidth) / 2} ${yPosition + segmentHeight}
                          L ${(funnelWidth - bottomWidth) / 2} ${yPosition + segmentHeight} Z`}
                      fill={metadata.colors?.[index] || DEFAULT_CHART_COLORS[index % DEFAULT_CHART_COLORS.length]}
                      stroke={themeColors.grid}
                      strokeWidth={1}
                    />
                    <text
                      x={funnelWidth / 2}
                      y={yPosition + segmentHeight / 2}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fill={themeColors.text}
                      fontSize={12}
                    >
                      {item.name}: {item.value} ({(percentage * 100).toFixed(1)}%)
                    </text>
                  </g>
                );
              })}
            </svg>
          </div>
        );

      case 'activity':
        // Similar to timebar but for activity data
        const activityData = chartData.map((item: any) => ({
          name: item.time || item.name,
          value: item.count || item.value
        }));
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={activityData} margin={margins}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />}
              {showXAxis && (
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  height={xHeight}
                />
              )}
              {showYAxis && (
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: themeColors.text, fontSize }}
                  width={yWidth}
                />
              )}
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar
                dataKey="value"
                fill={metadata.colors?.[0] || themeColors.primary}
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'number':
        // Handle both legacy format and new NumberChartData format
        let numberValue, numberLabel, formatType, color;
        
        if (data && typeof data === 'object' && 'value' in data) {
          // New NumberChartData format
          const numberData = data as any;
          numberValue = numberData.value;
          numberLabel = numberData.name;
          formatType = numberData.formatType;
          color = numberData.color;
        } else if (Array.isArray(chartData) && chartData.length > 0) {
          // Legacy format
          numberValue = chartData[0].value || 0;
          numberLabel = chartData[0].name || 'Value';
        }

        const formatValue = (value: number, type?: string) => {
          switch (type) {
            case 'currency':
              return new Intl.NumberFormat('en-US', { 
                style: 'currency', 
                currency: 'USD' 
              }).format(value);
            case 'percentage':
              return `${(value * 100).toFixed(1)}%`;
            default:
              return typeof value === 'number' ? value.toLocaleString() : value;
          }
        };

        return (
          <div className="flex flex-col items-center justify-center h-full p-4">
            <div
              className="text-4xl font-bold mb-2"
              style={{ color: color || metadata.colors?.[0] || themeColors.primary }}
            >
              {formatValue(numberValue, formatType)}
            </div>
            <div
              className="text-sm opacity-70"
              style={{ color: themeColors.text }}
            >
              {numberLabel}
            </div>
          </div>
        );

      case 'table':
        const tableData = data as any;
        const columns = tableData.columns || [];
        const rows = tableData.data || [];

        return (
          <div className="w-full h-full overflow-auto p-2">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b" style={{ borderColor: themeColors.grid }}>
                  {columns.map((col: any) => (
                    <th 
                      key={col.key} 
                      className="text-left p-2 font-medium"
                      style={{ color: themeColors.text }}
                    >
                      {col.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {rows.map((row: any, index: number) => (
                  <tr 
                    key={index} 
                    className="border-b" 
                    style={{ borderColor: themeColors.grid }}
                  >
                    {columns.map((col: any) => (
                      <td 
                        key={col.key} 
                        className="p-2"
                        style={{ color: themeColors.text }}
                      >
                        {row[col.key]}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'text':
        const textData = data as any;
        const content = textData.content || '';
        const textFormatType = textData.formatType || 'plain';

        return (
          <div className="w-full h-full overflow-auto p-4">
            {textFormatType === 'markdown' ? (
              <MarkdownRenderer content={content} />
            ) : (
              <div 
                className="text-sm"
                style={{ color: themeColors.text }}
                dangerouslySetInnerHTML={textFormatType === 'html' ? { __html: content } : undefined}
              >
                {textFormatType === 'plain' ? content : undefined}
              </div>
            )}
          </div>
        );

      case 'image':
        const imageData = data as any;
        return (
          <div className="w-full h-full flex flex-col items-center justify-center p-4">
            <img 
              src={imageData.imageUrl} 
              alt={imageData.altText || 'Chart image'} 
              className="max-w-full max-h-full object-contain"
            />
            {imageData.caption && (
              <p 
                className="text-xs mt-2 text-center"
                style={{ color: themeColors.text }}
              >
                {imageData.caption}
              </p>
            )}
          </div>
        );

      case 'detail':
        const detailData = data as any;
        const detailTitle = detailData.title || '';
        const detailItems = detailData.data || [];
        const layout = detailData.layout || 'vertical';

        return (
          <div className="w-full h-full overflow-auto p-4">
            {detailTitle && (
              <h3 
                className="text-lg font-semibold mb-4"
                style={{ color: themeColors.text }}
              >
                {detailTitle}
              </h3>
            )}
            <div className={`space-y-3 ${layout === 'grid' ? 'grid grid-cols-2 gap-3' : ''}`}>
              {detailItems.map((item: any, index: number) => (
                <div 
                  key={index} 
                  className="border rounded-lg p-3"
                  style={{ borderColor: themeColors.grid }}
                >
                  {Object.entries(item).map(([key, value]) => (
                    <div key={key} className="flex justify-between mb-1">
                      <strong style={{ color: themeColors.text }}>{key}:</strong> 
                      <span style={{ color: themeColors.text }}>{String(value)}</span>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <p>Unsupported chart type: {chartType}</p>
          </div>
        );
    }
  };

  return (
    <div className="w-full h-full">
      {renderChart()}
    </div>
  );
};

// Wrapper component that measures container size for responsive behavior
const ResponsiveChartRenderer: React.FC<Omit<ChartRendererProps, 'width' | 'height'>> = (props) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };

    // Initial measurement
    updateDimensions();

    // Set up ResizeObserver for dynamic updates
    const resizeObserver = new ResizeObserver(() => {
      // Use requestAnimationFrame to debounce rapid resize events
      requestAnimationFrame(updateDimensions);
    });
    
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Also listen for window resize as a fallback
    window.addEventListener('resize', updateDimensions);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  // Force re-render when props change to ensure chart updates
  useEffect(() => {
    if (containerRef.current) {
      const updateDimensions = () => {
        if (containerRef.current) {
          const { width, height } = containerRef.current.getBoundingClientRect();
          setDimensions({ width, height });
        }
      };
      // Small delay to ensure DOM has updated
      setTimeout(updateDimensions, 50);
    }
  }, [props.chartData]);

  return (
    <div ref={containerRef} className={`w-full h-full ${props.className || ''}`}>
      {dimensions && (
        <ChartRenderer
          {...props}
          width={dimensions.width}
          height={dimensions.height}
          className=""
        />
      )}
    </div>
  );
};

export default ResponsiveChartRenderer;
