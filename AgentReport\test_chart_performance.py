#!/usr/bin/env python3
"""
Quick test script to verify chart performance improvements.
"""
import asyncio
import aiohttp
import time
import json


async def test_chart_performance():
    """Test chart query performance."""
    
    # Test configuration
    base_url = "http://localhost:8000"
    
    # Sample chart request
    payload = {
        "prompt": "What are our top performing films by revenue?",
        "database_id": "db_1026fc8f"  # Use the database ID from the logs
    }
    
    headers = {
        "Content-Type": "application/json",
        # Note: In a real test, you'd need proper authentication
    }
    
    print("Testing chart query performance...")
    print(f"Query: {payload['prompt']}")
    print(f"Database ID: {payload['database_id']}")
    print("-" * 60)
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Cold request (no cache)
        print("Test 1: Cold request (no cache)")
        start_time = time.time()
        
        try:
            async with session.post(
                f"{base_url}/api/chart/query",
                json=payload,
                headers=headers
            ) as response:
                response_time = time.time() - start_time
                
                print(f"Status: {response.status}")
                print(f"Response time: {response_time:.2f} seconds")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"Success: {data.get('success', False)}")
                    if data.get('data'):
                        chart_data = data['data']
                        print(f"Chart type: {chart_data.get('chartType', 'Unknown')}")
                        print(f"Data points: {len(chart_data.get('data', []))}")
                        if chart_data.get('metadata', {}).get('sqlQuery'):
                            print(f"SQL query generated: Yes")
                        else:
                            print(f"SQL query generated: No")
                    else:
                        print("No chart data returned")
                else:
                    error_text = await response.text()
                    print(f"Error: {error_text}")
                    
        except Exception as e:
            response_time = time.time() - start_time
            print(f"Exception: {e}")
            print(f"Response time: {response_time:.2f} seconds")
        
        print()
        
        # Test 2: Warm request (should use cache if available)
        print("Test 2: Warm request (should use cache)")
        start_time = time.time()
        
        try:
            async with session.post(
                f"{base_url}/api/chart/query",
                json=payload,
                headers=headers
            ) as response:
                response_time = time.time() - start_time
                
                print(f"Status: {response.status}")
                print(f"Response time: {response_time:.2f} seconds")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"Success: {data.get('success', False)}")
                    print(f"Likely cached: {'Yes' if response_time < 1.0 else 'No'}")
                else:
                    error_text = await response.text()
                    print(f"Error: {error_text}")
                    
        except Exception as e:
            response_time = time.time() - start_time
            print(f"Exception: {e}")
            print(f"Response time: {response_time:.2f} seconds")
        
        print()
        print("-" * 60)
        print("Performance test completed!")
        print("\nExpected improvements:")
        print("- Cold request: Should be under 5 seconds (vs ~20 seconds before)")
        print("- Warm request: Should be under 2 seconds (cached schema)")
        print("- Identical request: Should be under 1 second (cached result)")


if __name__ == "__main__":
    asyncio.run(test_chart_performance())
