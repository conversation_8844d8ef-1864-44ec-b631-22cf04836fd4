// Chart-related TypeScript interfaces

export type ChartType = 'bar' | 'line' | 'pie' | 'area' | 'table' | 'timebar' | 'funnel' | 'number' | 'image' | 'detail' | 'text' | 'activity';

// Legacy format (Used by all charts) - Updated to use 'name' for Recharts compatibility
export interface ChartDataPoint {
  name: string;    // Recharts compatible (was "label")
  value: number;
  category?: string;
}

// Chart-specific data structures
export interface PieChartData {
  data: Array<{
    name: string;      // Category name
    value: number;     // Raw value (Recharts calculates percentages)
  }>;
}

export interface BarChartData {
  data: Array<{
    name: string;      // Category name
    value: number;     // Bar height
  }>;
  categoryKey?: string; // Default: "name"
  valueKey?: string;    // Default: "value"
}

export interface LineChartData {
  data: Array<{
    name: string;      // X-axis value (usually time/date)
    value: number;     // Y-axis value
  }>;
  xKey?: string;        // Default: "name"
  yKey?: string;        // Default: "value"
}

export interface TimeBarChartData {
  data: Array<{
    time: string;      // Time interval/period
    value: number;     // Duration or count
  }>;
  timeKey?: string;     // Default: "time"
  valueKey?: string;    // Default: "value"
}

export interface NumberChartData {
  value: number;
  name: string;           // Description/label
  formatType: string;     // "number" | "currency" | "percentage"
  color?: string;
}

export interface TableChartData {
  columns: Array<{
    key: string;       // Data property key
    label: string;     // Display header
  }>;
  data: Array<Record<string, any>>; // Table rows
}

export interface FunnelChartData {
  data: Array<{
    name: string;      // Stage name
    value: number;     // Stage value
  }>;
  nameKey?: string;     // Default: "name"
  valueKey?: string;    // Default: "value"
}

export interface ActivityChartData {
  data: Array<{
    time: string;      // Time period
    count: number;     // Activity count
  }>;
  timeKey?: string;     // Default: "time"
  countKey?: string;    // Default: "count"
}

export interface TextChartData {
  content: string;
  formatType: string;  // "plain" | "markdown" | "html"
}

export interface ImageChartData {
  imageUrl: string;
  altText?: string;
  caption?: string;
}

export interface DetailChartData {
  title: string;
  data: Array<Record<string, any>>;
  layout?: string;     // "vertical" | "horizontal" | "grid"
}

export interface ChartMetadata {
  xAxisLabel?: string;
  yAxisLabel?: string;
  colors?: string[];           // Default color palette
  sqlQuery?: string;
  description?: string;
  dataSource?: string;
  generatedAt?: string;
  chartSpecificConfig?: Record<string, any>;
}

export interface ChartData {
  title: string;
  chartType: ChartType;
  data: ChartDataPoint[] | PieChartData | BarChartData | LineChartData | TimeBarChartData | 
        NumberChartData | TableChartData | FunnelChartData | ActivityChartData | 
        TextChartData | ImageChartData | DetailChartData; // Support all chart data types
  metadata: ChartMetadata;
}

export interface ChartQueryRequest {
  prompt: string;
  user_id?: string;
  dashboard_id?: string;
  database_id: string;  // Required field - backend validates this
}

export interface ChartQueryResponse {
  success: boolean;
  data?: ChartData;  // Optional since backend returns undefined when success is false
  error?: string;
}

export interface ChartTypeRecommendation {
  chart_type: ChartType;
  confidence: number;
  reasoning: string;
  alternative_types?: ChartType[];
}

export interface ChartTypesResponse {
  supported_types: ChartType[];
  descriptions: Record<ChartType, string>;
  default_colors: string[];
}

export interface ChartValidationResponse {
  valid: boolean;
  recommended_chart_type?: ChartType;
  confidence?: number;
  reasoning?: string;
  alternative_types?: ChartType[];
  error?: string;
}

export interface ChartHealthResponse {
  status: 'healthy' | 'unhealthy';
  service: string;
  timestamp: string;
  dependencies?: Record<string, string>;
  error?: string;
}

export interface ChartWidget {
  id: string;
  title: string;
  chartData: ChartData | null;
  isLoading: boolean;
  error: string | null;
  dashboard_id?: string; // Optional dashboard association
  pendingPrompt?: string; // Stores user prompt when waiting for database selection
  layout: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
}

export interface DashboardLayout {
  widgets: ChartWidget[];
  gridCols: number;
  gridRows: number;
}

// Grid layout types for react-grid-layout
export interface GridLayoutItem {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  static?: boolean;
  isDraggable?: boolean;
  isResizable?: boolean;
}

export interface GridLayoutBreakpoint {
  lg: GridLayoutItem[];
  md: GridLayoutItem[];
  sm: GridLayoutItem[];
  xs: GridLayoutItem[];
  xxs: GridLayoutItem[];
}

// Chart configuration constants
export const CHART_CONFIG = {
  DEFAULT_WIDGET_SIZE: { w: 4, h: 5 },
  MIN_WIDGET_SIZE: { w: 3, h: 4 },
  MAX_WIDGET_SIZE: { w: 12, h: 15 }, // Max height is 3x default height (5 * 3 = 15)
  MAX_WIDGETS: 12,
  GRID_COLS: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
  BREAKPOINTS: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
  ROW_HEIGHT: 85,
} as const;

export const DEFAULT_CHART_COLORS = [
  '#8b5cf6', // Purple
  '#06b6d4', // Cyan
  '#10b981', // Emerald
  '#f59e0b', // Amber
  '#ef4444', // Red
  '#8b5cf6', // Purple variant
  '#06b6d4', // Cyan variant
  '#10b981', // Emerald variant
  '#f59e0b', // Amber variant
  '#ef4444', // Red variant
] as const;

// Drag-to-delete state types
export interface DragState {
  isDragging: boolean;
  draggedWidgetId: string | null;
  isOverDeleteZone: boolean;
}

export interface DragToDeleteZoneProps {
  isVisible: boolean;
  isHovered: boolean;
}
