"""Legacy Chart API Controller

This module provides backward compatibility for chart endpoints that were
originally at /chart/* before being moved to /api/chart/*.
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends, status

from app.models.chart import ChartQueryRequest, ChartQueryResponse
from app.models.user import User
from app.services.chart_service import ChartService
from app.utils.security import get_current_user

logger = logging.getLogger(__name__)

# Create legacy router with /chart prefix for backward compatibility
router = APIRouter(prefix="/chart", tags=["chart-legacy"])

# Initialize chart service
chart_service = ChartService()


@router.post("/query", response_model=ChartQueryResponse)
async def query_chart(
    request: ChartQueryRequest,
    current_user: User = Depends(get_current_user)
) -> ChartQueryResponse:
    """
    Legacy endpoint for chart query processing.
    
    This endpoint provides backward compatibility for existing tests and frontend code.
    It delegates to the same chart service as the new /api/chart/query endpoint.
    """
    try:
        logger.info(f"Legacy chart query request from user {current_user.id}: {request.prompt[:100]}...")
        
        # Add user context to request if not provided
        if not request.user_id:
            request.user_id = current_user.id
        
        # Log database usage
        if request.database_id:
            logger.info(f"Legacy chart query will use database {request.database_id} for user {current_user.id}")
        else:
            logger.info(f"Legacy chart query will use mock data for user {current_user.id}")
        
        # Process the chart query using the service
        response = await chart_service.process_chart_query(request)
        
        # Log the result
        if response.success:
            chart_type = response.data.chartType if response.data else "unknown"
            logger.info(f"Successfully generated {chart_type} chart for user {current_user.id} (legacy endpoint)")
        else:
            logger.warning(f"Chart generation failed for user {current_user.id}: {response.error} (legacy endpoint)")
        
        return response
        
    except ValueError as e:
        logger.error(f"Validation error in legacy chart query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid request: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error in legacy chart query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while processing chart query"
        )


@router.get("/types")
async def get_supported_chart_types(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Legacy endpoint for getting supported chart types.
    """
    try:
        from app.models.chart import ChartType, CHART_TYPE_DESCRIPTIONS
        
        chart_types = {
            "supported_types": [chart_type.value for chart_type in ChartType],
            "descriptions": {
                chart_type.value: CHART_TYPE_DESCRIPTIONS[chart_type] 
                for chart_type in ChartType
            },
            "default_colors": [
                '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'
            ]
        }
        
        logger.info(f"Chart types requested by user {current_user.id} (legacy endpoint)")
        return chart_types
        
    except Exception as e:
        logger.error(f"Error retrieving chart types (legacy): {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve chart types"
        )


@router.post("/validate")
async def validate_chart_query(
    request: ChartQueryRequest,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Legacy endpoint for validating chart queries.
    """
    try:
        logger.info(f"Legacy chart validation request from user {current_user.id}: {request.prompt[:100]}...")
        
        # Add user context to request if not provided
        if not request.user_id:
            request.user_id = current_user.id
        
        # Get chart type recommendation without generating full data
        from app.models.chart import ChartGenerationContext
        context = ChartGenerationContext(
            user_query=request.prompt,
            user_id=request.user_id,
            dashboard_id=request.dashboard_id
        )
        
        recommendation = await chart_service._get_chart_type_recommendation(context)
        
        validation_result = {
            "valid": True,
            "recommended_chart_type": recommendation.chart_type.value,
            "confidence": recommendation.confidence,
            "reasoning": recommendation.reasoning,
            "alternative_types": [alt.value for alt in recommendation.alternative_types]
        }
        
        logger.info(f"Chart validation successful for user {current_user.id} (legacy endpoint)")
        return validation_result
        
    except ValueError as e:
        logger.error(f"Validation error in legacy chart validation: {str(e)}")
        return {
            "valid": False,
            "error": f"Invalid request: {str(e)}",
            "recommended_chart_type": None
        }
    except Exception as e:
        logger.error(f"Unexpected error in legacy chart validation: {str(e)}")
        return {
            "valid": False,
            "error": f"Validation failed: {str(e)}",
            "recommended_chart_type": None
        }


@router.get("/health")
async def chart_service_health() -> Dict[str, Any]:
    """
    Legacy endpoint for chart service health check.
    """
    try:
        health_status = {
            "status": "healthy",
            "service": "chart_selection",
            "timestamp": "2024-01-01T00:00:00Z",
            "dependencies": {
                "bedrock_client": "available",
                "chart_service": "available"
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Legacy chart service health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "service": "chart_selection",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00Z"
        }
