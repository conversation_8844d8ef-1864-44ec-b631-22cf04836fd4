'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { TokenResponse, LoginRequest, RegisterRequest, AuthContextType, OAuthTokens } from '@/types';
import { useApi } from './ApiContext';
import { STORAGE_KEYS } from '@/lib/constants';
// import { getRedirectPath } from '@/lib/utils/onboarding';
import { safeApiCall, normalizeAuthResponse, logAuthError } from '@/lib/utils/auth-error-handling';
import axios from 'axios';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<TokenResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false); // Changed to false - no automatic loading
  const [isNewUser, setIsNewUser] = useState<boolean>(false);
  const [lastStateCheck, setLastStateCheck] = useState<number>(Date.now());
  const [isCompletingOnboarding, setIsCompletingOnboarding] = useState<boolean>(false);
  const [hasAttemptedAutoAuth, setHasAttemptedAutoAuth] = useState<boolean>(false);
  const { loginUser, registerUser, refreshToken: refreshTokenApi, logoutUser, getUserProfile, completeOnboarding: completeOnboardingApi } = useApi();
  const router = useRouter();
  // const pathname = usePathname();

  // Refresh user state from backend with enhanced error handling
  const refreshUserState = useCallback(async () => {
    if (!user) return;

    const { data: profile, error } = await safeApiCall(
      () => getUserProfile(),
      'refresh user state',
      { maxRetries: 2 } // Fewer retries for background operations
    );

    if (profile) {
      setIsNewUser(profile.is_new_user || false);
      setLastStateCheck(Date.now());
      console.log('User state refreshed, is_new_user:', profile.is_new_user);
    } else if (error) {
      logAuthError(error, 'refreshUserState');
      // Don't update state on error to avoid disrupting user experience
      // But log the error for debugging
    }
  }, [user, getUserProfile, setIsNewUser, setLastStateCheck]);

  // Check if state refresh is needed (every 5 minutes)
  const shouldRefreshState = useCallback(() => {
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - lastStateCheck > fiveMinutes;
  }, [lastStateCheck]);

  // Visibility change handler for tab focus
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (!document.hidden && user && shouldRefreshState()) {
        console.log('Tab became visible, refreshing user state...');
        await refreshUserState();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [user, shouldRefreshState, refreshUserState]);

  // Periodic state revalidation (every 10 minutes)
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(async () => {
      if (shouldRefreshState()) {
        console.log('Periodic state refresh triggered...');
        await refreshUserState();
      }
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(interval);
  }, [user, shouldRefreshState, refreshUserState]);

  // Manual authentication initialization - only called when user explicitly signs in
  const initializeAuthFromStorage = useCallback(async (): Promise<boolean> => {
    console.log('Manually initializing authentication from storage...');
    setIsLoading(true);

    const accessToken = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    const userId = localStorage.getItem(STORAGE_KEYS.USER_ID);
    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    const expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);

    if (accessToken && userId && refreshToken && expiresAt) {
      try {
        // Prepare token data structure and set it so that other components can access it while we validate.
        const tokenData: TokenResponse = {
          access_token: accessToken,
          user_id: userId,
          refresh_token: refreshToken,
          expires_at: expiresAt,
          token_type: 'bearer',
        };

        // Optimistically set the user so downstream hooks/components can read it while we validate.
        setUser(tokenData);

        // Validate access token by attempting to fetch the user profile.
        const profile = await getUserProfile();
        setIsNewUser(profile.is_new_user || false);
        setLastStateCheck(Date.now());
        console.log('User profile fetched, is_new_user:', profile.is_new_user);

        // 🔄 If the access token is going to expire in the next 5 minutes, refresh it proactively.
        try {
          const expirationTime = new Date(expiresAt).getTime();
          const bufferTime = 5 * 60 * 1000; // 5 minutes
          if (Date.now() > (expirationTime - bufferTime)) {
            console.log('Access token nearing expiry. Refreshing…');
            const newTokenData = await refreshTokenApi();
            setUser(newTokenData);
          }
        } catch (refreshCheckError) {
          console.warn('Proactive refresh check failed:', refreshCheckError);
        }

        setIsLoading(false);
        return true; // ✅ Stored token is still valid (and refreshed if necessary)
              } catch (error: unknown) {
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          // Stored access token is no longer valid – try to refresh it.
          console.warn('Access token invalid (401). Attempting refresh…');
          try {
            const newTokenData = await refreshTokenApi();
            console.log('Token refresh successful after 401');
            setUser(newTokenData);
            const refreshedProfile = await getUserProfile();
            setIsNewUser(refreshedProfile.is_new_user || false);
            setLastStateCheck(Date.now());
            setIsLoading(false);
            return true;
          } catch (refreshError) {
            console.error('Token refresh failed after 401:', refreshError);
          }
        } else {
          console.error('Failed to fetch user profile during initialization:', error);
        }

        // At this point we either couldn't refresh or had another error – clear auth data.
        localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER_ID);
        localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
        setUser(null);
        setIsLoading(false);
        return false;
      }
    } else {
      console.log('No complete auth data found in localStorage');
      setIsLoading(false);
      return false;
    }
  }, [refreshTokenApi, getUserProfile]);

  // Check if user has valid stored authentication on app load (but don't auto-authenticate)
  useEffect(() => {
    if (!hasAttemptedAutoAuth) {
      setHasAttemptedAutoAuth(true);
      const hasStoredAuth = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN) &&
                           localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);

      if (hasStoredAuth) {
        console.log('Found stored auth data, but not auto-authenticating. User must manually sign in.');
      }
    }
  }, [hasAttemptedAutoAuth]);

  // Disabled automatic redirects - users must manually navigate
  // This allows the landing page to be truly public
  // Protected routes will handle their own authentication checks

  const login = async (credentials: LoginRequest) => {
    const { data, error } = await safeApiCall(
      () => loginUser(credentials),
      'user login'
    );

    if (error) {
      logAuthError(error, 'login');
      throw new Error(error.userMessage);
    }

    if (data) {
      const normalizedData = normalizeAuthResponse(data);
      setUser(normalizedData);
      setIsNewUser(normalizedData.is_new_user);
      setLastStateCheck(Date.now());

      // Redirect based on user status
      if (normalizedData.is_new_user) {
        router.push('/onboarding');
      } else {
        router.push('/chat');
      }
    }
  };

  // Manual sign in function that checks stored tokens first
  const signIn = async (credentials?: LoginRequest, shouldRedirect: boolean = true) => {
    // First try to authenticate from stored tokens
    const hasValidStoredAuth = await initializeAuthFromStorage();

    if (hasValidStoredAuth) {
      console.log('Authenticated from stored tokens');
      // Only redirect if explicitly requested (for manual sign-in from landing page)
      if (shouldRedirect) {
        if (isNewUser) {
          router.push('/onboarding');
        } else {
          router.push('/chat');
        }
      }
      return;
    }

    // If no valid stored auth and credentials provided, perform login
    if (credentials) {
      await login(credentials);
    } else if (shouldRedirect) {
      // No stored auth and no credentials - redirect to login page only if redirect is requested
      router.push('/login');
    } else {
      // For ProtectedRoute scenarios, throw error to indicate authentication failed
      throw new Error('No valid authentication found');
    }
  };

  const register = async (credentials: RegisterRequest) => {
    const { data, error } = await safeApiCall(
      () => registerUser(credentials),
      'user registration'
    );

    if (error) {
      logAuthError(error, 'register');
      throw new Error(error.userMessage);
    }

    if (data) {
      // Registration successful, now automatically log the user in
      console.log('Registration successful, automatically logging in user...');

      try {
        // Use the same credentials to log in the user
        const loginCredentials: LoginRequest = {
          username: credentials.email, // Backend expects username field for email
          password: credentials.password
        };

        const { data: loginData, error: loginError } = await safeApiCall(
          () => loginUser(loginCredentials),
          'auto-login after registration'
        );

        if (loginError) {
          logAuthError(loginError, 'auto-login after registration');
          // If auto-login fails, redirect to login with success message
          console.warn('Auto-login failed after registration, redirecting to login page');
          router.push('/login?registration=success');
          return;
        }

        if (loginData) {
          const normalizedData = normalizeAuthResponse(loginData);
          setUser(normalizedData);
          setIsNewUser(true); // New registrations are always new users
          setLastStateCheck(Date.now());

          console.log('Auto-login successful, redirecting to onboarding...');
          // Redirect new user to onboarding
          router.push('/onboarding');
        }
      } catch (autoLoginError) {
        console.error('Auto-login failed after registration:', autoLoginError);
        // Fallback to login page with success message
        router.push('/login?registration=success');
      }
    }
  };

  const logout = async () => {
    try {
      await logoutUser();
    } catch (error) {
      console.error('Logout API call failed:', error);
    }

    // Clear all auth data
    localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER_ID);
    localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
    localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);

    setUser(null);
    setIsNewUser(false);
    setHasAttemptedAutoAuth(false);

    // Redirect to landing page instead of login
    router.push('/');
  };

  const refreshUserToken = async () => {
    try {
      const newTokenData = await refreshTokenApi();
      setUser(newTokenData);

      // Extract is_new_user from refresh response if available
      if (newTokenData.is_new_user !== undefined) {
        setIsNewUser(newTokenData.is_new_user);
        setLastStateCheck(Date.now());
      } else if (shouldRefreshState()) {
        // If refresh response doesn't include is_new_user and it's been a while, fetch it
        await refreshUserState();
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      setUser(null);
      setIsNewUser(false);
      setLastStateCheck(Date.now());
      router.push('/login');
      throw error;
    }
  };

  const completeOnboarding = async () => {
    try {
      setIsCompletingOnboarding(true);
      console.log('🚀 Starting onboarding completion...');

      // Call the backend to mark onboarding as complete
      await completeOnboardingApi();
      console.log('✅ Backend onboarding completion successful');

      // Fetch updated user profile to confirm is_new_user is now false
      const updatedProfile = await getUserProfile();
      console.log('✅ Updated profile fetched:', { is_new_user: updatedProfile.is_new_user });

      setIsNewUser(updatedProfile.is_new_user || false);
      setLastStateCheck(Date.now());

      console.log('✅ User state updated, redirecting to dashboard...');

      // Use setTimeout to ensure state updates are processed before redirect
      setTimeout(() => {
        setIsCompletingOnboarding(false);
        router.push('/chat');
      }, 100);

    } catch (error) {
      console.error('❌ Failed to complete onboarding:', error);
      setIsCompletingOnboarding(false);
      throw error;
    }
  };

  const handleOAuthCallback = useCallback(async (tokens: OAuthTokens) => {
    try {
      // Calculate expires_at from expires_in (seconds to ISO string)
      const expiresAt = new Date(Date.now() + tokens.expires_in * 1000).toISOString();

      // Store tokens in localStorage using existing STORAGE_KEYS
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.access_token);
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refresh_token);
      localStorage.setItem(STORAGE_KEYS.USER_ID, tokens.user_id);
      localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt);
      localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, tokens.token_type || 'bearer');

      // Create token data for auth context
      const tokenData: TokenResponse = {
        access_token: tokens.access_token,
        token_type: tokens.token_type || 'bearer',
        user_id: tokens.user_id,
        expires_at: expiresAt,
        refresh_token: tokens.refresh_token
      };

      // Update auth context
      setUser(tokenData);

      // For OAuth, we need to fetch user profile to get is_new_user status
      try {
        const profile = await getUserProfile();
        setIsNewUser(profile.is_new_user || false);
        setLastStateCheck(Date.now());
        console.log('OAuth user profile fetched, is_new_user:', profile.is_new_user);
      } catch (error) {
        console.error('Failed to fetch user profile during OAuth callback:', error);
        // Don't fail the OAuth flow if profile fetch fails
        setIsNewUser(false);
      }

      // Don't redirect here - let the calling component handle the redirect
    } catch (error) {
      console.error('OAuth callback handling failed:', error);
      throw error;
    }
  }, [setUser]);



  return (
    <AuthContext.Provider value={{
      isAuthenticated: !!user,
      user,
      isLoading,
      isNewUser,
      isCompletingOnboarding,
      login,
      signIn,
      initializeAuthFromStorage,
      register,
      logout,
      setUser,
      refreshUserToken,
      handleOAuthCallback,
      completeOnboarding
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 