{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/constants/api.ts"], "sourcesContent": ["// API-related constants\r\n\r\nexport const API_ENDPOINTS = {\r\n  // Auth endpoints\r\n  AUTH: {\r\n    LOGIN: '/auth/login',\r\n    REGISTER: '/auth/register',\r\n    LOGOUT: '/auth/logout',\r\n    REFRESH: '/auth/refresh',\r\n    ME: '/auth/me',\r\n    GOOGLE: '/auth/google',\r\n    COMPLETE_ONBOARDING: '/auth/onboarding/complete',\r\n  },\r\n  \r\n  // Database endpoints\r\n  DATABASES: {\r\n    LIST: '/databases/listdatabases',\r\n    CONNECT: '/databases/connectdatabase',\r\n    DISCONNECT: '/databases/disconnectdatabase',\r\n    SCHEMA: '/databases/schema',\r\n  },\r\n  \r\n  // Query endpoints\r\n  QUERY: {\r\n    ASK: '/query/ask',\r\n  },\r\n  \r\n  // Chat endpoints\r\n  CHAT: {\r\n    LIST: '/chat/list',\r\n    HISTORY: '/chat/history',\r\n    DELETE: '/chat/delete',\r\n  },\r\n  \r\n  // Report endpoints\r\n  REPORTS: {\r\n    LIST: '/reports/list',\r\n  },\r\n  \r\n  // Question endpoints\r\n  ASK: {\r\n    QUESTION: '/ask/question',\r\n  },\r\n\r\n  // Chart endpoints\r\n  CHART: {\r\n    QUERY: '/chart/query',\r\n    TYPES: '/chart/types',\r\n    VALIDATE: '/chart/validate',\r\n    HEALTH: '/chart/health',\r\n  },\r\n\r\n  // Dashboard endpoints\r\n  DASHBOARD: {\r\n    LIST: '/dashboard/list',\r\n    CREATE: '/dashboard/create',\r\n    GET: '/dashboard/get',\r\n    UPDATE: '/dashboard/update',\r\n    DELETE: '/dashboard/delete',\r\n  },\r\n\r\n  // Analysis project endpoints\r\n  ANALYSIS: {\r\n    LIST: '/analysis/list',\r\n    CREATE: '/analysis/create',\r\n    GET: '/analysis/get',\r\n    UPDATE: '/analysis/update',\r\n    DELETE: '/analysis/delete',\r\n    DATA: '/analysis/data',\r\n    STEPS: '/analysis/steps',\r\n    EXECUTE_STEP: '/analysis/execute-step',\r\n    UPLOAD_FILE: '/analysis/upload-file',\r\n    DELETE_FILE: '/analysis/delete-file',\r\n  },\r\n} as const;\r\n\r\nexport const HTTP_STATUS = {\r\n  OK: 200,\r\n  CREATED: 201,\r\n  BAD_REQUEST: 400,\r\n  UNAUTHORIZED: 401,\r\n  FORBIDDEN: 403,\r\n  NOT_FOUND: 404,\r\n  INTERNAL_SERVER_ERROR: 500,\r\n} as const;\r\n\r\nexport const DEFAULT_HEADERS = {\r\n  'Content-Type': 'application/json',\r\n} as const;\r\n\r\n// Environment configuration\r\nexport const ENVIRONMENT_CONFIG = {\r\n  development: {\r\n    defaultApiBase: 'http://localhost:8000',\r\n    name: 'Development',\r\n    enableDebugLogs: true,\r\n  },\r\n  production: {\r\n    defaultApiBase: 'https://agentreportbackend.vercel.app',\r\n    name: 'Production',\r\n    enableDebugLogs: false,\r\n  },\r\n  staging: {\r\n    defaultApiBase: 'https://staging-agentreportbackend.vercel.app',\r\n    name: 'Staging',\r\n    enableDebugLogs: true,\r\n  },\r\n} as const;\r\n\r\n// Validate environment configuration\r\nexport const validateEnvironment = (): void => {\r\n  const nodeEnv = process.env.NODE_ENV;\r\n  const apiBase = process.env.NEXT_PUBLIC_API_BASE;\r\n\r\n  // Check if NODE_ENV is valid\r\n  if (nodeEnv && !Object.keys(ENVIRONMENT_CONFIG).includes(nodeEnv)) {\r\n    console.warn(`⚠️  Unknown NODE_ENV: ${nodeEnv}. Expected: development, production, or staging`);\r\n  }\r\n\r\n  // Check if API base URL is set\r\n  if (!apiBase) {\r\n    console.warn('⚠️  NEXT_PUBLIC_API_BASE is not set. Using environment defaults.');\r\n  } else {\r\n    // Validate URL format\r\n    try {\r\n      new URL(apiBase);\r\n    } catch (error) {\r\n      console.error(`❌ Invalid NEXT_PUBLIC_API_BASE URL: ${apiBase}`);\r\n    }\r\n  }\r\n};\r\n\r\n// Get current environment configuration\r\nexport const getCurrentEnvironmentConfig = () => {\r\n  const nodeEnv = process.env.NODE_ENV as keyof typeof ENVIRONMENT_CONFIG;\r\n  return ENVIRONMENT_CONFIG[nodeEnv] || ENVIRONMENT_CONFIG.development;\r\n};\r\n\r\n// Get API base URL with proper formatting and validation\r\nexport const getApiBaseUrl = (): string => {\r\n  // Validate environment on first call\r\n  if (typeof window === 'undefined') {\r\n    validateEnvironment();\r\n  }\r\n\r\n  let rawBaseUrl = process.env.NEXT_PUBLIC_API_BASE ?? '';\r\n  const envConfig = getCurrentEnvironmentConfig();\r\n\r\n  // Fallback to environment-specific defaults if not set\r\n  if (!rawBaseUrl) {\r\n    rawBaseUrl = envConfig.defaultApiBase;\r\n    if (envConfig.enableDebugLogs) {\r\n      console.log(`🔧 Using default ${envConfig.name} API URL: ${rawBaseUrl}`);\r\n    }\r\n  }\r\n\r\n  // Remove trailing slashes and add /api\r\n  const apiBaseUrl = `${rawBaseUrl.replace(/\\/+$/, '')}/api`;\r\n\r\n  // Log the API base URL in development/staging for debugging\r\n  if (envConfig.enableDebugLogs && typeof window !== 'undefined') {\r\n    console.log(`🔗 ${envConfig.name} API Base URL:`, apiBaseUrl);\r\n  }\r\n\r\n  return apiBaseUrl;\r\n};\r\n\r\n// Get the raw base URL without /api suffix (useful for OAuth redirects)\r\nexport const getRawApiBaseUrl = (): string => {\r\n  let rawBaseUrl = process.env.NEXT_PUBLIC_API_BASE ?? '';\r\n  const envConfig = getCurrentEnvironmentConfig();\r\n\r\n  if (!rawBaseUrl) {\r\n    rawBaseUrl = envConfig.defaultApiBase;\r\n  }\r\n\r\n  return rawBaseUrl.replace(/\\/+$/, '');\r\n};\r\n\r\n// Storage keys for localStorage\r\nexport const STORAGE_KEYS = {\r\n  ACCESS_TOKEN: 'accessToken',\r\n  REFRESH_TOKEN: 'refreshToken',\r\n  TOKEN_TYPE: 'tokenType',\r\n  USER_ID: 'userId',\r\n  EXPIRES_AT: 'expiresAt',\r\n} as const;\r\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;AAEjB,MAAM,gBAAgB;IAC3B,iBAAiB;IACjB,MAAM;QACJ,OAAO;QACP,UAAU;QACV,QAAQ;QACR,SAAS;QACT,IAAI;QACJ,QAAQ;QACR,qBAAqB;IACvB;IAEA,qBAAqB;IACrB,WAAW;QACT,MAAM;QACN,SAAS;QACT,YAAY;QACZ,QAAQ;IACV;IAEA,kBAAkB;IAClB,OAAO;QACL,KAAK;IACP;IAEA,iBAAiB;IACjB,MAAM;QACJ,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IAEA,mBAAmB;IACnB,SAAS;QACP,MAAM;IACR;IAEA,qBAAqB;IACrB,KAAK;QACH,UAAU;IACZ;IAEA,kBAAkB;IAClB,OAAO;QACL,OAAO;QACP,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IAEA,sBAAsB;IACtB,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,QAAQ;IACV;IAEA,6BAA6B;IAC7B,UAAU;QACR,MAAM;QACN,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,OAAO;QACP,cAAc;QACd,aAAa;QACb,aAAa;IACf;AACF;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,SAAS;IACT,aAAa;IACb,cAAc;IACd,WAAW;IACX,WAAW;IACX,uBAAuB;AACzB;AAEO,MAAM,kBAAkB;IAC7B,gBAAgB;AAClB;AAGO,MAAM,qBAAqB;IAChC,aAAa;QACX,gBAAgB;QAChB,MAAM;QACN,iBAAiB;IACnB;IACA,YAAY;QACV,gBAAgB;QAChB,MAAM;QACN,iBAAiB;IACnB;IACA,SAAS;QACP,gBAAgB;QAChB,MAAM;QACN,iBAAiB;IACnB;AACF;AAGO,MAAM,sBAAsB;IACjC,MAAM;IACN,MAAM;IAEN,6BAA6B;IAC7B,IAAI,WAAW,CAAC,OAAO,IAAI,CAAC,oBAAoB,QAAQ,CAAC,UAAU;QACjE,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,+CAA+C,CAAC;IAChG;IAEA,+BAA+B;IAC/B,uCAAc;;IAEd,OAAO;QACL,sBAAsB;QACtB,IAAI;YACF,IAAI,IAAI;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS;QAChE;IACF;AACF;AAGO,MAAM,8BAA8B;IACzC,MAAM;IACN,OAAO,kBAAkB,CAAC,QAAQ,IAAI,mBAAmB,WAAW;AACtE;AAGO,MAAM,gBAAgB;IAC3B,qCAAqC;IACrC,wCAAmC;QACjC;IACF;IAEA,IAAI,aAAa,6DAAoC;IACrD,MAAM,YAAY;IAElB,uDAAuD;IACvD,IAAI,CAAC,YAAY;QACf,aAAa,UAAU,cAAc;QACrC,IAAI,UAAU,eAAe,EAAE;YAC7B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU,IAAI,CAAC,UAAU,EAAE,YAAY;QACzE;IACF;IAEA,uCAAuC;IACvC,MAAM,aAAa,GAAG,WAAW,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;IAE1D,4DAA4D;IAC5D,uCAAgE;;IAEhE;IAEA,OAAO;AACT;AAGO,MAAM,mBAAmB;IAC9B,IAAI,aAAa,6DAAoC;IACrD,MAAM,YAAY;IAElB,IAAI,CAAC,YAAY;QACf,aAAa,UAAU,cAAc;IACvC;IAEA,OAAO,WAAW,OAAO,CAAC,QAAQ;AACpC;AAGO,MAAM,eAAe;IAC1B,cAAc;IACd,eAAe;IACf,YAAY;IACZ,SAAS;IACT,YAAY;AACd", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/constants/routes.ts"], "sourcesContent": ["// Application route constants\r\n\r\nexport const ROUTES = {\r\n  // Public routes\r\n  HOME: '/',\r\n  LOGIN: '/login',\r\n  REGISTER: '/register',\r\n\r\n  // Auth routes\r\n  AUTH: {\r\n    CALLBACK: '/auth/callback',\r\n  },\r\n\r\n  // OAuth routes\r\n  OAUTH: {\r\n    CALLBACK: '/oauth/callback',\r\n  },\r\n\r\n  // Onboarding routes\r\n  ONBOARDING: '/onboarding',\r\n\r\n  // Protected routes\r\n  DASHBOARD: '/dashboard',\r\n  CHAT: '/chat',\r\n  DATASOURCES: '/datasources',\r\n  REPORTS: '/reports',\r\n\r\n  // Dynamic routes\r\n  CHAT_WITH_ID: (chatId: string) => `/chat/${chatId}`,\r\n} as const;\r\n\r\n// Routes that don't require authentication\r\nexport const PUBLIC_ROUTES = [\r\n  ROUTES.HOME,\r\n  ROUTES.LOGIN,\r\n  ROUTES.REGISTER,\r\n  ROUTES.AUTH.CALLBACK,\r\n  ROUTES.OAUTH.CALLBACK,\r\n  ROUTES.ONBOARDING,\r\n] as const;\r\n\r\n// Routes that require authentication\r\nexport const PROTECTED_ROUTES = [\r\n  ROUTES.DASHBOARD,\r\n  ROUTES.CHAT,\r\n  ROUTES.DATASOURCES,\r\n  ROUTES.REPORTS,\r\n] as const;\r\n\r\n// Default redirect after login\r\nexport const DEFAULT_LOGIN_REDIRECT = ROUTES.CHAT;\r\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;AAEvB,MAAM,SAAS;IACpB,gBAAgB;IAChB,MAAM;IACN,OAAO;IACP,UAAU;IAEV,cAAc;IACd,MAAM;QACJ,UAAU;IACZ;IAEA,eAAe;IACf,OAAO;QACL,UAAU;IACZ;IAEA,oBAAoB;IACpB,YAAY;IAEZ,mBAAmB;IACnB,WAAW;IACX,MAAM;IACN,aAAa;IACb,SAAS;IAET,iBAAiB;IACjB,cAAc,CAAC,SAAmB,CAAC,MAAM,EAAE,QAAQ;AACrD;AAGO,MAAM,gBAAgB;IAC3B,OAAO,IAAI;IACX,OAAO,KAAK;IACZ,OAAO,QAAQ;IACf,OAAO,IAAI,CAAC,QAAQ;IACpB,OAAO,KAAK,CAAC,QAAQ;IACrB,OAAO,UAAU;CAClB;AAGM,MAAM,mBAAmB;IAC9B,OAAO,SAAS;IAChB,OAAO,IAAI;IACX,OAAO,WAAW;IAClB,OAAO,OAAO;CACf;AAGM,MAAM,yBAAyB,OAAO,IAAI", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/constants/index.ts"], "sourcesContent": ["// Main constants exports\n\nexport * from './api';\nexport * from './routes';\n\n// Application constants\nexport const APP_CONFIG = {\n  NAME: 'Agent Platform',\n  DESCRIPTION: 'Agent Platform for database queries',\n  VERSION: '1.0.0',\n} as const;\n\n// UI constants\nexport const UI_CONFIG = {\n  SIDEBAR_WIDTH: 280,\n  HEADER_HEIGHT: 64,\n  MOBILE_BREAKPOINT: 768,\n} as const;\n\n// Chat constants\nexport const CHAT_CONFIG = {\n  MAX_MESSAGE_LENGTH: 4000,\n  DEFAULT_OUTPUT_FORMAT: 'excel',\n  SESSION_ID_LENGTH: 36,\n} as const;\n\n// Database constants\nexport const DATABASE_TYPES = {\n  POSTGRESQL: 'POSTGRESQL',\n  MONGODB: 'MONGODB',\n  MYSQL: 'MYSQL',\n  SQLITE: 'SQLITE',\n} as const;\n\n// File upload constants\nexport const FILE_CONFIG = {\n  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB\n  ALLOWED_TYPES: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'],\n} as const;\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;;AAEzB;AACA;;;AAGO,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;AACX;AAGO,MAAM,YAAY;IACvB,eAAe;IACf,eAAe;IACf,mBAAmB;AACrB;AAGO,MAAM,cAAc;IACzB,oBAAoB;IACpB,uBAAuB;IACvB,mBAAmB;AACrB;AAGO,MAAM,iBAAiB;IAC5B,YAAY;IACZ,SAAS;IACT,OAAO;IACP,QAAQ;AACV;AAGO,MAAM,cAAc;IACzB,eAAe,KAAK,OAAO;IAC3B,eAAe;QAAC;QAAqE;KAAW;AAClG", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/providers/ApiContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, ReactNode, useCallback } from 'react';\r\nimport axios from 'axios';\r\nimport {\r\n  ApiContextType,\r\n  QueryRequest,\r\n  DatabaseConnectionRequestParams,\r\n  DatabaseConnectionResponse,\r\n  ConnectedDatabase,\r\n  LoginRequest,\r\n  RegisterRequest,\r\n  RegisterResponse,\r\n  TokenResponse,\r\n  ChatListItem,\r\n  ApiChatMessage,\r\n  ChatHistoryRequest,\r\n  ReportInfo,\r\n  ListReportsRequest,\r\n  ListReportsResponse,\r\n  DeleteChatRequest,\r\n  ChartType,\r\n  ChartQueryRequest,\r\n  ChartQueryResponse,\r\n  ChartTypesResponse,\r\n  ChartValidationResponse,\r\n  ChartHealthResponse,\r\n} from '@/types';\r\nimport { getApiBaseUrl, STORAGE_KEYS, API_ENDPOINTS } from '@/lib/constants';\r\n\r\n// Re-export types for backward compatibility\r\nexport type {\r\n  QueryRequest,\r\n  DatabaseConnectionRequestParams,\r\n  DatabaseConnectionResponse,\r\n  ConnectedDatabase,\r\n  LoginRequest,\r\n  TokenResponse,\r\n  ChatListItem,\r\n  ApiChatMessage,\r\n  ReportInfo,\r\n  ListReportsRequest,\r\n  ListReportsResponse,\r\n};\r\nconst ApiContext = createContext<ApiContextType | undefined>(undefined);\r\n\r\n\r\n\r\nexport const ApiProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const queryDatabases = useCallback(async (request: QueryRequest) => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Use FormData to match backend expectations\r\n      const formData = new FormData();\r\n      formData.append('query', request.query);\r\n      formData.append('output_format', request.output_format || \"excel\");\r\n      if (request.session_id) {\r\n        formData.append('session_id', request.session_id);\r\n      }\r\n      if (request.target_databases) {\r\n        formData.append('target_databases', JSON.stringify(request.target_databases));\r\n      }\r\n      if (request.target_tables) {\r\n        formData.append('target_tables', JSON.stringify(request.target_tables));\r\n      }\r\n      if (request.target_columns) {\r\n        formData.append('target_columns', JSON.stringify(request.target_columns));\r\n      }\r\n      // Add streaming support - this was the missing piece!\r\n      if (request.enable_token_streaming) {\r\n        formData.append('enable_token_streaming', 'true');\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Authorization': `Bearer ${token}`,\r\n        // Don't set Content-Type - let axios set it with boundary for FormData\r\n      };\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/ask/question`, formData, { headers });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"API error (queryDatabases):\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getDatabaseSchema = useCallback(async (dbId: string) => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n      };\r\n\r\n      if (token) {\r\n        headers['Authorization'] = `Bearer ${token}`;\r\n      }\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/databases/schema`, { db_id: dbId }, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error fetching database schema:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const listDatabases = useCallback(async (): Promise<ConnectedDatabase[]> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        console.log('No access token found, skipping database list request');\r\n        return [];\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/databases/listdatabases`, {}, { headers });\r\n      return response.data.databases || response.data || [];\r\n    } catch (error) {\r\n      console.error(\"Error listing databases:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const connectNewDatabase = useCallback(async (params: DatabaseConnectionRequestParams): Promise<DatabaseConnectionResponse> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.post<DatabaseConnectionResponse>(\r\n        `${getApiBaseUrl()}/databases/connectdatabase`,\r\n        params,\r\n        { headers }\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error connecting new database:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const disconnectExistingDatabase = useCallback(async (dbId: string): Promise<{ message: string }> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n      };\r\n      \r\n      if (token) {\r\n        headers['Authorization'] = `Bearer ${token}`;\r\n      }\r\n\r\n      const response = await axios.post<{ message: string }>(`${getApiBaseUrl()}/databases/disconnectdatabase`, { db_id: dbId }, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error disconnecting database:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const askQuery = useCallback(async (\r\n    query: string,\r\n    outputFormat: string,\r\n    conversationHistory: any[],\r\n    targetDatabases?: string[],\r\n    targetTables?: string[],\r\n    targetColumns?: string[]\r\n  ): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n      };\r\n      \r\n      if (token) {\r\n        headers['Authorization'] = `Bearer ${token}`;\r\n      }\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/query/ask`, {\r\n        query,\r\n        output_format: outputFormat,\r\n        target_databases: targetDatabases,\r\n        target_tables: targetTables,\r\n        target_columns: targetColumns,\r\n      }, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error asking query:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const loginUser = useCallback(async (credentials: LoginRequest): Promise<TokenResponse> => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      params.append('username', credentials.username);\r\n      params.append('password', credentials.password);\r\n\r\n      const response = await axios.post<TokenResponse>(\r\n        `${getApiBaseUrl()}/auth/login`,\r\n        params,\r\n        {\r\n          headers: {\r\n            'Content-Type': 'application/x-www-form-urlencoded',\r\n          },\r\n        }\r\n      );\r\n      if (response.data.access_token) {\r\n        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, response.data.access_token);\r\n        console.log('Access token set in localStorage:', response.data.access_token);\r\n        localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, response.data.token_type);\r\n        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.refresh_token);\r\n        localStorage.setItem(STORAGE_KEYS.USER_ID, response.data.user_id);\r\n        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, response.data.expires_at);\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error during login:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const registerUser = useCallback(async (credentials: RegisterRequest): Promise<RegisterResponse> => {\r\n    try {\r\n      const response = await axios.post<RegisterResponse>(\r\n        `${getApiBaseUrl()}${API_ENDPOINTS.AUTH.REGISTER}`,\r\n        {\r\n          email: credentials.email,\r\n          password: credentials.password,\r\n          full_name: credentials.full_name,\r\n        },\r\n        {\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n        }\r\n      );\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error registering user:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const refreshToken = useCallback(async (): Promise<TokenResponse> => {\r\n    try {\r\n      const refreshTokenValue = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n\r\n      if (!refreshTokenValue) {\r\n        throw new Error('No refresh token found');\r\n      }\r\n\r\n      const response = await axios.post<TokenResponse>(\r\n        `${getApiBaseUrl()}/auth/refresh`,\r\n        { refresh_token: refreshTokenValue },\r\n        {\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n        }\r\n      );\r\n\r\n      if (response.data.access_token) {\r\n        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, response.data.access_token);\r\n        localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, response.data.token_type);\r\n        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.refresh_token);\r\n        localStorage.setItem(STORAGE_KEYS.USER_ID, response.data.user_id);\r\n        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, response.data.expires_at);\r\n        console.log('Token refreshed successfully');\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error refreshing token:\", error);\r\n      // Clear stored tokens on refresh failure\r\n      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n      localStorage.removeItem(STORAGE_KEYS.USER_ID);\r\n      localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);\r\n      localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const logoutUser = useCallback(async (): Promise<void> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (token) {\r\n        await axios.post(\r\n          `${getApiBaseUrl()}/auth/logout`,\r\n          {},\r\n          {\r\n            headers: {\r\n              'Authorization': `Bearer ${token}`,\r\n              'Content-Type': 'application/json',\r\n            },\r\n          }\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error during logout:\", error);\r\n      // Continue with local cleanup even if backend call fails\r\n    } finally {\r\n      // Always clear local storage\r\n      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n      localStorage.removeItem(STORAGE_KEYS.USER_ID);\r\n      localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);\r\n      localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);\r\n      console.log('User logged out and tokens cleared');\r\n    }\r\n  }, []);\r\n\r\n  const getUserProfile = useCallback(async (): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/auth/me`, {}, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error fetching user profile:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const listUserChats = useCallback(async (): Promise<ChatListItem[]> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        console.log('No access token found, skipping chat list request');\r\n        return [];\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const apiUrl = `${getApiBaseUrl()}/chats/listchats`;\r\n      console.log('🔗 Making request to:', apiUrl);\r\n      console.log('🔑 Using Bearer token:', token.substring(0, 20) + '...');\r\n\r\n      const response = await axios.post(apiUrl, {}, { headers });\r\n      console.log('✅ List chats response received:', response.status);\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error(\"❌ Error listing user chats:\", error);\r\n      if (axios.isAxiosError(error)) {\r\n        console.error(\"📍 Request URL:\", error.config?.url);\r\n        console.error(\"📋 Request headers:\", error.config?.headers);\r\n        if (error.response) {\r\n          console.error(\"🚨 Response status:\", error.response.status);\r\n          console.error(\"🚨 Response data:\", error.response.data);\r\n          console.error(\"🚨 Response headers:\", error.response.headers);\r\n        } else if (error.request) {\r\n          console.error(\"🌐 Network error - no response received:\", error.request);\r\n        }\r\n      }\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getChatHistory = useCallback(async (sessionId: string): Promise<ApiChatMessage[]> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      \r\n      if (!token) {\r\n        console.log('No access token found, skipping chat history request');\r\n        return [];\r\n      }\r\n      \r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const requestBody = { session_id: sessionId };\r\n      const response = await axios.post(`${getApiBaseUrl()}/chats/getchathistory`, requestBody, { headers });\r\n      return response.data || [];\r\n    } catch (error) {\r\n      console.error(\"Error getting chat history:\", error);\r\n      if (axios.isAxiosError(error) && error.response) {\r\n        console.error(\"Chat history error response:\", error.response.data);\r\n        console.error(\"Chat history error status:\", error.response.status);\r\n      }\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const listUserReports = useCallback(async (request: ListReportsRequest = {}): Promise<ListReportsResponse> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      \r\n      if (!token) {\r\n        console.log('No access token found, skipping reports list request');\r\n        return { reports: [], total_count: 0 };\r\n      }\r\n      \r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/reports/listreports`, request, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error listing user reports:\", error);\r\n      if (axios.isAxiosError(error) && error.response) {\r\n        console.error(\"List reports error response:\", error.response.data);\r\n        console.error(\"List reports error status:\", error.response.status);\r\n      }\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const deleteChat = useCallback(async (sessionId: string): Promise<{ message: string }> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n      \r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n      \r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.post<{ message: string }>(`${getApiBaseUrl()}/chats/deletechat`, { session_id: sessionId }, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error deleting chat:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Profile and Settings API methods\r\n  const updateUserProfile = useCallback(async (data: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.put(`${getApiBaseUrl()}/auth/profile`, data, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error updating user profile:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const changePassword = useCallback(async (data: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/auth/change-password`, data, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error changing password:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const saveEmailPreferences = useCallback(async (data: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.put(`${getApiBaseUrl()}/auth/email-preferences`, data, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error saving email preferences:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const savePrivacySettings = useCallback(async (data: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.put(`${getApiBaseUrl()}/auth/privacy-settings`, data, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error saving privacy settings:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const exportUserData = useCallback(async (): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}/auth/export-data`, {}, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error exporting user data:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const deleteAccount = useCallback(async (): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const response = await axios.delete(`${getApiBaseUrl()}/auth/account`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error deleting account:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const completeOnboarding = useCallback(async (): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Check if we're in development mode and the endpoint doesn't exist\r\n      const isDevelopment = process.env.NODE_ENV === 'development';\r\n\r\n      try {\r\n        const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.AUTH.COMPLETE_ONBOARDING}`, {}, {\r\n          headers: {\r\n            'Authorization': `Bearer ${token}`,\r\n            'Content-Type': 'application/json',\r\n          },\r\n        });\r\n\r\n        return response.data;\r\n      } catch (error: any) {\r\n        // If it's a 404 error and we're in development, mock the response\r\n        if (error.response?.status === 404 && isDevelopment) {\r\n          console.warn(`⚠️ Backend endpoint ${API_ENDPOINTS.AUTH.COMPLETE_ONBOARDING} not implemented yet. Using mock response for development.`);\r\n\r\n          // Simulate a successful response\r\n          await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay\r\n\r\n          return {\r\n            success: true,\r\n            message: 'Onboarding completed successfully (mocked)',\r\n            user: {\r\n              is_new_user: false,\r\n              onboarding_completed_at: new Date().toISOString(),\r\n            }\r\n          };\r\n        }\r\n\r\n        // Re-throw the error if it's not a 404 or not in development\r\n        throw error;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error completing onboarding:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const queryChart = useCallback(async (request: ChartQueryRequest): Promise<ChartQueryResponse> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.CHART.QUERY}`, request, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error querying chart:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getChartTypes = useCallback(async (): Promise<ChartTypesResponse> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.CHART.TYPES}`, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting chart types:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const validateChartQuery = useCallback(async (request: ChartQueryRequest): Promise<ChartValidationResponse> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.CHART.VALIDATE}`, request, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error validating chart query:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getChartHealth = useCallback(async (): Promise<ChartHealthResponse> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${token}`,\r\n      };\r\n\r\n      const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.CHART.HEALTH}`, { headers });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting chart health:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Dashboard management methods\r\n  const listDashboards = useCallback(async (): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Return an empty list in development until the user creates dashboards\r\n      const mockDashboards: any[] = [];\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: mockDashboards,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.LIST}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error listing dashboards:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const createDashboard = useCallback(async (request: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const newDashboard = {\r\n        id: `dashboard-${Date.now()}`,\r\n        name: request.name,\r\n        description: request.description,\r\n        created_at: new Date().toISOString(),\r\n        updated_at: new Date().toISOString(),\r\n        user_id: 'user-1',\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: newDashboard,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.CREATE}`, request, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating dashboard:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getDashboard = useCallback(async (dashboardId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const mockDashboard = {\r\n        id: dashboardId,\r\n        name: 'Sample Dashboard',\r\n        description: 'A sample dashboard with charts',\r\n        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n        user_id: 'user-1',\r\n        widgets: [], // Empty widgets array for now\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: mockDashboard,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.GET}/${dashboardId}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting dashboard:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const updateDashboard = useCallback(async (dashboardId: string, request: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const updatedDashboard = {\r\n        id: dashboardId,\r\n        name: request.name || 'Updated Dashboard',\r\n        description: request.description || 'Updated description',\r\n        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        updated_at: new Date().toISOString(),\r\n        user_id: 'user-1',\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: updatedDashboard,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.put(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.UPDATE}/${dashboardId}`, request, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error updating dashboard:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const deleteDashboard = useCallback(async (dashboardId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Dashboard deleted successfully',\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.DELETE}/${dashboardId}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error deleting dashboard:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Analysis Project Management Methods\r\n  const listAnalysisProjects = useCallback(async (): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const mockProjects = [\r\n        {\r\n          id: 'analysis-1',\r\n          name: 'Employee Performance Analysis',\r\n          description: 'Analyzing employee performance metrics, salary distributions, and department insights',\r\n          status: 'completed',\r\n          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\r\n          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n          dataSource: 'HR Database',\r\n          stepCount: 3,\r\n          userId: 'user-1',\r\n        },\r\n        {\r\n          id: 'analysis-2',\r\n          name: 'Customer Behavior Study',\r\n          description: 'Understanding customer purchasing patterns and churn prediction',\r\n          status: 'running',\r\n          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),\r\n          updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\r\n          dataSource: 'Sales Database',\r\n          stepCount: 4,\r\n          progress: 67,\r\n          userId: 'user-1',\r\n        },\r\n        {\r\n          id: 'analysis-3',\r\n          name: 'Market Trend Forecasting',\r\n          description: 'Forecasting market trends and demand patterns for Q4',\r\n          status: 'draft',\r\n          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n          updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n          dataSource: 'Market Data API',\r\n          stepCount: 0,\r\n          userId: 'user-1',\r\n        },\r\n      ];\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: {\r\n          projects: mockProjects,\r\n          totalCount: mockProjects.length,\r\n        },\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.LIST}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error listing analysis projects:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const createAnalysisProject = useCallback(async (request: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const newProject = {\r\n        id: `analysis-${Date.now()}`,\r\n        name: request.name,\r\n        description: request.description || '',\r\n        status: 'draft',\r\n        createdAt: new Date().toISOString(),\r\n        updatedAt: new Date().toISOString(),\r\n        dataSource: request.dataSource,\r\n        stepCount: 0,\r\n        userId: 'user-1',\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: newProject,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.CREATE}`, request, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating analysis project:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getAnalysisProject = useCallback(async (projectId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const mockProject = {\r\n        id: projectId,\r\n        name: 'Employee Performance Analysis',\r\n        description: 'Analyzing employee performance metrics, salary distributions, and department insights',\r\n        status: 'completed',\r\n        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\r\n        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),\r\n        dataSource: 'HR Database',\r\n        stepCount: 3,\r\n        userId: 'user-1',\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: mockProject,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.GET}/${projectId}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting analysis project:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const updateAnalysisProject = useCallback(async (projectId: string, request: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const updatedProject = {\r\n        id: projectId,\r\n        name: request.name || 'Updated Analysis Project',\r\n        description: request.description || 'Updated description',\r\n        status: request.status || 'draft',\r\n        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\r\n        updatedAt: new Date().toISOString(),\r\n        dataSource: 'HR Database',\r\n        stepCount: 3,\r\n        userId: 'user-1',\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: updatedProject,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.put(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.UPDATE}/${projectId}`, request, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error updating analysis project:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const deleteAnalysisProject = useCallback(async (projectId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Analysis project deleted successfully',\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.DELETE}/${projectId}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error deleting analysis project:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getProjectData = useCallback(async (projectId: string, options: any = {}): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Return empty data initially - data will be populated via file upload\r\n      const emptyData = {\r\n        rows: [],\r\n        columns: [],\r\n        totalRows: 0,\r\n        schema: {},\r\n      };\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100));\r\n\r\n      return {\r\n        success: true,\r\n        data: emptyData,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const params = new URLSearchParams();\r\n      // if (options.stepId) params.append('stepId', options.stepId);\r\n      // if (options.page) params.append('page', options.page.toString());\r\n      // if (options.pageSize) params.append('pageSize', options.pageSize.toString());\r\n      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.DATA}/${projectId}?${params}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting project data:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const getProjectSteps = useCallback(async (projectId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Mock response for development\r\n      const mockSteps = [\r\n        {\r\n          id: 'step-1',\r\n          projectId,\r\n          title: 'Data Loading',\r\n          status: 'completed',\r\n          type: 'data_loading',\r\n          order: 1,\r\n          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\r\n          updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\r\n          outputs: {\r\n            summary: \"Successfully loaded 5 employee records from the connected database.\",\r\n            code: `import pandas as pd\\nimport numpy as np\\n\\n# Load employee data from connected database\\ndf = pd.read_sql_query(\"\"\"\\n    SELECT id, name, age, department, salary, performance_rating as performance\\n    FROM employees \\n    WHERE active = true\\n\"\"\", connection)\\n\\nprint(f\"Loaded {len(df)} employee records\")\\ndf.head()`,\r\n          },\r\n        },\r\n        {\r\n          id: 'step-2',\r\n          projectId,\r\n          title: 'Data Analysis',\r\n          status: 'completed',\r\n          type: 'analysis',\r\n          order: 2,\r\n          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\r\n          updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\r\n          outputs: {\r\n            summary: \"Analyzed salary distributions and identified 2 high-performing employees.\",\r\n            code: `# Analyze salary distribution by department\\ndept_stats = df.groupby('department').agg({\\n    'salary': ['mean', 'median', 'std'],\\n    'performance': 'mean',\\n    'age': 'mean'\\n}).round(2)\\n\\nprint(\"Department Statistics:\")\\nprint(dept_stats)\\n\\n# Identify high performers\\nhigh_performers = df[df['performance'] >= 4.5]\\nprint(f\"\\\\nHigh performers ({len(high_performers)} employees):\")\\nprint(high_performers[['name', 'department', 'performance']])`,\r\n          },\r\n        },\r\n        {\r\n          id: 'step-3',\r\n          projectId,\r\n          title: 'Visualization',\r\n          status: 'completed',\r\n          type: 'visualization',\r\n          order: 3,\r\n          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\r\n          updatedAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\r\n          outputs: {\r\n            summary: \"Created scatter plot showing relationship between salary and performance across departments.\",\r\n            code: `import matplotlib.pyplot as plt\\nimport seaborn as sns\\n\\n# Create scatter plot of salary vs performance\\nplt.figure(figsize=(10, 6))\\nsns.scatterplot(data=df, x='performance', y='salary', hue='department', s=100)\\nplt.title('Salary vs Performance by Department')\\nplt.xlabel('Performance Rating')\\nplt.ylabel('Salary ($)')\\nplt.legend(title='Department')\\nplt.tight_layout()\\nplt.show()`,\r\n            visualization: {\r\n              type: 'chart',\r\n              config: {\r\n                title: 'Salary vs Performance by Department',\r\n                xAxis: 'performance',\r\n                yAxis: 'salary',\r\n                groupBy: 'department'\r\n              },\r\n              data: [\r\n                { x: 4.2, y: 85000, group: 'Engineering', name: 'John Doe' },\r\n                { x: 4.8, y: 65000, group: 'Marketing', name: 'Jane Smith' },\r\n                { x: 4.1, y: 92000, group: 'Engineering', name: 'Mike Johnson' },\r\n                { x: 4.6, y: 70000, group: 'Design', name: 'Sarah Wilson' },\r\n                { x: 3.9, y: 98000, group: 'Engineering', name: 'David Brown' },\r\n              ]\r\n            },\r\n          },\r\n        },\r\n      ];\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));\r\n\r\n      return {\r\n        success: true,\r\n        data: mockSteps,\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.STEPS}/${projectId}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error getting project steps:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const executeStep = useCallback(async (projectId: string, stepId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Step execution completed successfully',\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.EXECUTE_STEP}/${projectId}/${stepId}`, {}, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error executing step:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // File Upload Methods\r\n  const uploadProjectFile = useCallback(async (request: any): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      const { file, projectId, onProgress } = request;\r\n\r\n      // Create FormData for file upload\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('projectId', projectId);\r\n\r\n      // For now, simulate file processing and return mock data based on file type\r\n      if (onProgress) {\r\n        // Simulate upload progress\r\n        for (let i = 0; i <= 100; i += 10) {\r\n          await new Promise(resolve => setTimeout(resolve, 100));\r\n          onProgress({\r\n            loaded: (file.size * i) / 100,\r\n            total: file.size,\r\n            percentage: i\r\n          });\r\n        }\r\n      }\r\n\r\n      // Mock response based on file type\r\n      let mockPreviewData: any = {\r\n        rows: [],\r\n        columns: [],\r\n        totalRows: 0,\r\n        schema: {}\r\n      };\r\n\r\n      if (file.name.toLowerCase().includes('csv') || file.name.toLowerCase().includes('excel')) {\r\n        // Simulate parsing CSV/Excel data\r\n        mockPreviewData = {\r\n          rows: [\r\n            { id: 1, name: 'Alice Johnson', age: 28, salary: 75000, department: 'Engineering' },\r\n            { id: 2, name: 'Bob Smith', age: 34, salary: 82000, department: 'Marketing' },\r\n            { id: 3, name: 'Carol White', age: 29, salary: 78000, department: 'Design' },\r\n            { id: 4, name: 'David Brown', age: 31, salary: 85000, department: 'Engineering' },\r\n            { id: 5, name: 'Emma Davis', age: 26, salary: 72000, department: 'Sales' },\r\n          ],\r\n          columns: [\r\n            { name: 'id', type: 'number', nullable: false },\r\n            { name: 'name', type: 'string', nullable: false },\r\n            { name: 'age', type: 'number', nullable: false },\r\n            { name: 'salary', type: 'number', nullable: false },\r\n            { name: 'department', type: 'string', nullable: false },\r\n          ],\r\n          totalRows: 5,\r\n          schema: {\r\n            id: 'INTEGER',\r\n            name: 'VARCHAR(255)',\r\n            age: 'INTEGER',\r\n            salary: 'INTEGER',\r\n            department: 'VARCHAR(100)',\r\n          }\r\n        };\r\n      }\r\n\r\n      // Simulate final processing delay\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      return {\r\n        success: true,\r\n        data: {\r\n          fileId: `file-${Date.now()}`,\r\n          filename: file.name,\r\n          rowCount: mockPreviewData.totalRows,\r\n          columnCount: mockPreviewData.columns.length,\r\n          previewData: mockPreviewData\r\n        }\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Authorization': `Bearer ${token}`,\r\n      //   // Don't set Content-Type for FormData, let browser set it with boundary\r\n      // };\r\n      // \r\n      // const response = await axios.post(\r\n      //   `${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.UPLOAD_FILE}`, \r\n      //   formData, \r\n      //   { \r\n      //     headers,\r\n      //     onUploadProgress: (progressEvent) => {\r\n      //       if (onProgress && progressEvent.total) {\r\n      //         onProgress({\r\n      //           loaded: progressEvent.loaded,\r\n      //           total: progressEvent.total,\r\n      //           percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)\r\n      //         });\r\n      //       }\r\n      //     }\r\n      //   }\r\n      // );\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error uploading file:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  const deleteProjectFile = useCallback(async (projectId: string, fileId: string): Promise<any> => {\r\n    try {\r\n      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n\r\n      if (!token) {\r\n        throw new Error('No authentication token found');\r\n      }\r\n\r\n      // Simulate API delay\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n      return {\r\n        success: true,\r\n        message: 'File deleted successfully'\r\n      };\r\n\r\n      // Uncomment when backend is ready:\r\n      // const headers: Record<string, string> = {\r\n      //   'Content-Type': 'application/json',\r\n      //   'Authorization': `Bearer ${token}`,\r\n      // };\r\n      // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.ANALYSIS.DELETE_FILE}/${projectId}/${fileId}`, { headers });\r\n      // return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error deleting file:\", error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <ApiContext.Provider value={{\r\n      queryDatabases,\r\n      getDatabaseSchema,\r\n      listDatabases,\r\n      connectNewDatabase,\r\n      disconnectExistingDatabase,\r\n      askQuery,\r\n      loginUser,\r\n      registerUser,\r\n      refreshToken,\r\n      logoutUser,\r\n      getUserProfile,\r\n      updateUserProfile,\r\n      changePassword,\r\n      saveEmailPreferences,\r\n      savePrivacySettings,\r\n      exportUserData,\r\n      deleteAccount,\r\n      listUserChats,\r\n      getChatHistory,\r\n      listUserReports,\r\n      deleteChat,\r\n      completeOnboarding,\r\n      queryChart,\r\n      getChartTypes,\r\n      validateChartQuery,\r\n      getChartHealth,\r\n      listDashboards,\r\n      createDashboard,\r\n      getDashboard,\r\n      updateDashboard,\r\n      deleteDashboard,\r\n      listAnalysisProjects,\r\n      createAnalysisProject,\r\n      getAnalysisProject,\r\n      updateAnalysisProject,\r\n      deleteAnalysisProject,\r\n      getProjectData,\r\n      getProjectSteps,\r\n      executeStep,\r\n      uploadProjectFile,\r\n      deleteProjectFile,\r\n    }}>\r\n      {children}\r\n    </ApiContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useApi = () => {\r\n  const context = useContext(ApiContext);\r\n  if (context === undefined) {\r\n    throw new Error('useApi must be used within an ApiProvider');\r\n  }\r\n  return context;\r\n};"], "names": [], "mappings": ";;;;;AACA;AACA;AAyBA;AAAA;AA3BA;;;;;AA2CA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA8B;AAItD,MAAM,cAAiD,CAAC,EAAE,QAAQ,EAAE;IACzE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,6CAA6C;YAC7C,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,QAAQ,KAAK;YACtC,SAAS,MAAM,CAAC,iBAAiB,QAAQ,aAAa,IAAI;YAC1D,IAAI,QAAQ,UAAU,EAAE;gBACtB,SAAS,MAAM,CAAC,cAAc,QAAQ,UAAU;YAClD;YACA,IAAI,QAAQ,gBAAgB,EAAE;gBAC5B,SAAS,MAAM,CAAC,oBAAoB,KAAK,SAAS,CAAC,QAAQ,gBAAgB;YAC7E;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,SAAS,MAAM,CAAC,iBAAiB,KAAK,SAAS,CAAC,QAAQ,aAAa;YACvE;YACA,IAAI,QAAQ,cAAc,EAAE;gBAC1B,SAAS,MAAM,CAAC,kBAAkB,KAAK,SAAS,CAAC,QAAQ,cAAc;YACzE;YACA,sDAAsD;YACtD,IAAI,QAAQ,sBAAsB,EAAE;gBAClC,SAAS,MAAM,CAAC,0BAA0B;YAC5C;YAEA,MAAM,UAAkC;gBACtC,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAEpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,aAAa,CAAC,EAAE,UAAU;gBAAE;YAAQ;YAEzF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAC5D,MAAM,UAAkC;gBACtC,gBAAgB;YAClB;YAEA,IAAI,OAAO;gBACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;YAC9C;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,iBAAiB,CAAC,EAAE;gBAAE,OAAO;YAAK,GAAG;gBAAE;YAAQ;YACpG,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,wBAAwB,CAAC,EAAE,CAAC,GAAG;gBAAE;YAAQ;YAC9F,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,IAAI,EAAE;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,0BAA0B,CAAC,EAC9C,QACA;gBAAE;YAAQ;YAEZ,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpD,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAC5D,MAAM,UAAkC;gBACtC,gBAAgB;YAClB;YAEA,IAAI,OAAO;gBACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;YAC9C;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAsB,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,6BAA6B,CAAC,EAAE;gBAAE,OAAO;YAAK,GAAG;gBAAE;YAAQ;YACrI,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC3B,OACA,cACA,qBACA,iBACA,cACA;QAEA,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAC5D,MAAM,UAAkC;gBACtC,gBAAgB;YAClB;YAEA,IAAI,OAAO;gBACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;YAC9C;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,UAAU,CAAC,EAAE;gBAChE;gBACA,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,gBAAgB;YAClB,GAAG;gBAAE;YAAQ;YACb,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACnC,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,CAAC,YAAY,YAAY,QAAQ;YAC9C,OAAO,MAAM,CAAC,YAAY,YAAY,QAAQ;YAE9C,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,WAAW,CAAC,EAC/B,QACA;gBACE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEF,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;gBAC9B,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC,YAAY;gBAC1E,QAAQ,GAAG,CAAC,qCAAqC,SAAS,IAAI,CAAC,YAAY;gBAC3E,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,UAAU;gBACtE,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa,EAAE,SAAS,IAAI,CAAC,aAAa;gBAC5E,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,OAAO;gBAChE,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,UAAU;YACxE;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,MAAM,8HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,EAClD;gBACE,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,WAAW,YAAY,SAAS;YAClC,GACA;gBACE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAGF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI;YACF,MAAM,oBAAoB,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;YAEzE,IAAI,CAAC,mBAAmB;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,aAAa,CAAC,EACjC;gBAAE,eAAe;YAAkB,GACnC;gBACE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAGF,IAAI,SAAS,IAAI,CAAC,YAAY,EAAE;gBAC9B,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY,EAAE,SAAS,IAAI,CAAC,YAAY;gBAC1E,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,UAAU;gBACtE,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa,EAAE,SAAS,IAAI,CAAC,aAAa;gBAC5E,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,OAAO;gBAChE,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,UAAU;gBACtE,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,yCAAyC;YACzC,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YACjD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;YAClD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO;YAC5C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;YAC/C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;YAC/C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,OAAO;gBACT,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CACd,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,YAAY,CAAC,EAChC,CAAC,GACD;oBACE,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;wBAClC,gBAAgB;oBAClB;gBACF;YAEJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,yDAAyD;QAC3D,SAAU;YACR,6BAA6B;YAC7B,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YACjD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;YAClD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO;YAC5C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;YAC/C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;YAC/C,QAAQ,GAAG,CAAC;QACd;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,QAAQ,CAAC,EAAE,CAAC,GAAG;gBAClE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,SAAS,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,gBAAgB,CAAC;YACnD,QAAQ,GAAG,CAAC,yBAAyB;YACrC,QAAQ,GAAG,CAAC,0BAA0B,MAAM,SAAS,CAAC,GAAG,MAAM;YAE/D,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;gBAAE;YAAQ;YACxD,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAC9D,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;gBAC7B,QAAQ,KAAK,CAAC,mBAAmB,MAAM,MAAM,EAAE;gBAC/C,QAAQ,KAAK,CAAC,uBAAuB,MAAM,MAAM,EAAE;gBACnD,IAAI,MAAM,QAAQ,EAAE;oBAClB,QAAQ,KAAK,CAAC,uBAAuB,MAAM,QAAQ,CAAC,MAAM;oBAC1D,QAAQ,KAAK,CAAC,qBAAqB,MAAM,QAAQ,CAAC,IAAI;oBACtD,QAAQ,KAAK,CAAC,wBAAwB,MAAM,QAAQ,CAAC,OAAO;gBAC9D,OAAO,IAAI,MAAM,OAAO,EAAE;oBACxB,QAAQ,KAAK,CAAC,4CAA4C,MAAM,OAAO;gBACzE;YACF;YACA,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,cAAc;gBAAE,YAAY;YAAU;YAC5C,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,qBAAqB,CAAC,EAAE,aAAa;gBAAE;YAAQ;YACpG,OAAO,SAAS,IAAI,IAAI,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;gBAC/C,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,CAAC,IAAI;gBACjE,QAAQ,KAAK,CAAC,8BAA8B,MAAM,QAAQ,CAAC,MAAM;YACnE;YACA,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,UAA8B,CAAC,CAAC;QACzE,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO;oBAAE,SAAS,EAAE;oBAAE,aAAa;gBAAE;YACvC;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,oBAAoB,CAAC,EAAE,SAAS;gBAAE;YAAQ;YAC/F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;gBAC/C,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,CAAC,IAAI;gBACjE,QAAQ,KAAK,CAAC,8BAA8B,MAAM,QAAQ,CAAC,MAAM;YACnE;YACA,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAsB,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,iBAAiB,CAAC,EAAE;gBAAE,YAAY;YAAU,GAAG;gBAAE;YAAQ;YACnI,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,aAAa,CAAC,EAAE,MAAM;gBACxE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACxC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,qBAAqB,CAAC,EAAE,MAAM;gBACjF,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC9C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,uBAAuB,CAAC,EAAE,MAAM;gBAClF,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,sBAAsB,CAAC,EAAE,MAAM;gBACjF,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,iBAAiB,CAAC,EAAE,CAAC,GAAG;gBAC3E,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,IAAI,aAAa,CAAC,EAAE;gBACrE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBAClC,gBAAgB;gBAClB;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,oEAAoE;YACpE,MAAM,gBAAgB,oDAAyB;YAE/C,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,MAAM,8HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG;oBACnG,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;wBAClC,gBAAgB;oBAClB;gBACF;gBAEA,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAY;gBACnB,kEAAkE;gBAClE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,eAAe;oBACnD,QAAQ,IAAI,CAAC,CAAC,oBAAoB,EAAE,8HAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,0DAA0D,CAAC;oBAEtI,iCAAiC;oBACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,yBAAyB;oBAElF,OAAO;wBACL,SAAS;wBACT,SAAS;wBACT,MAAM;4BACJ,aAAa;4BACb,yBAAyB,IAAI,OAAO,WAAW;wBACjD;oBACF;gBACF;gBAEA,6DAA6D;gBAC7D,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,MAAM,8HAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,SAAS;gBAAE;YAAQ;YACvG,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,MAAM,8HAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;gBAAE;YAAQ;YAC7F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,MAAM,8HAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS;gBAAE;YAAQ;YAC1G,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,MAAM,8HAAA,CAAA,gBAAa,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;gBAAE;YAAQ;YAC9F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,wEAAwE;YACxE,MAAM,iBAAwB,EAAE;YAEhC,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,sGAAsG;QACtG,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,eAAe;gBACnB,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;gBAC7B,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW;gBAChC,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;gBAClC,SAAS;YACX;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,kHAAkH;QAClH,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,gBAAgB;gBACpB,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,SAAS;gBACT,SAAS,EAAE;YACb;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,oHAAoH;QACpH,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,aAAqB;QAC9D,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,mBAAmB;gBACvB,IAAI;gBACJ,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACtE,YAAY,IAAI,OAAO,WAAW;gBAClC,SAAS;YACX;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,gIAAgI;QAChI,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,0HAA0H;QAC1H,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,eAAe;gBACnB;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACrE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACrE,YAAY;oBACZ,WAAW;oBACX,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACrE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAChE,YAAY;oBACZ,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,QAAQ;oBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACrE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;oBACrE,YAAY;oBACZ,WAAW;oBACX,QAAQ;gBACV;aACD;YAED,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,UAAU;oBACV,YAAY,aAAa,MAAM;gBACjC;YACF;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,qGAAqG;QACrG,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,aAAa;gBACjB,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW,IAAI;gBACpC,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,YAAY,QAAQ,UAAU;gBAC9B,WAAW;gBACX,QAAQ;YACV;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,iHAAiH;QACjH,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,cAAc;gBAClB,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,QAAQ;gBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,YAAY;gBACZ,WAAW;gBACX,QAAQ;YACV;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,iHAAiH;QACjH,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QAClE,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,iBAAiB;gBACrB,IAAI;gBACJ,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,WAAW,IAAI,OAAO,WAAW;gBACjC,YAAY;gBACZ,WAAW;gBACX,QAAQ;YACV;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,6HAA6H;QAC7H,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,uHAAuH;QACvH,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB,UAAe,CAAC,CAAC;QAC5E,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,uEAAuE;YACvE,MAAM,YAAY;gBAChB,MAAM,EAAE;gBACR,SAAS,EAAE;gBACX,WAAW;gBACX,QAAQ,CAAC;YACX;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,wCAAwC;QACxC,+DAA+D;QAC/D,oEAAoE;QACpE,gFAAgF;QAChF,4HAA4H;QAC5H,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,gCAAgC;YAChC,MAAM,YAAY;gBAChB;oBACE,IAAI;oBACJ;oBACA,OAAO;oBACP,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAChE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAChE,SAAS;wBACP,SAAS;wBACT,MAAM,CAAC,8TAA8T,CAAC;oBACxU;gBACF;gBACA;oBACE,IAAI;oBACJ;oBACA,OAAO;oBACP,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;oBAChE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;oBAC5D,SAAS;wBACP,SAAS;wBACT,MAAM,CAAC,mcAAmc,CAAC;oBAC7c;gBACF;gBACA;oBACE,IAAI;oBACJ;oBACA,OAAO;oBACP,QAAQ;oBACR,MAAM;oBACN,OAAO;oBACP,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;oBAC5D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;oBAC5D,SAAS;wBACP,SAAS;wBACT,MAAM,CAAC,mYAAmY,CAAC;wBAC3Y,eAAe;4BACb,MAAM;4BACN,QAAQ;gCACN,OAAO;gCACP,OAAO;gCACP,OAAO;gCACP,SAAS;4BACX;4BACA,MAAM;gCACJ;oCAAE,GAAG;oCAAK,GAAG;oCAAO,OAAO;oCAAe,MAAM;gCAAW;gCAC3D;oCAAE,GAAG;oCAAK,GAAG;oCAAO,OAAO;oCAAa,MAAM;gCAAa;gCAC3D;oCAAE,GAAG;oCAAK,GAAG;oCAAO,OAAO;oCAAe,MAAM;gCAAe;gCAC/D;oCAAE,GAAG;oCAAK,GAAG;oCAAO,OAAO;oCAAU,MAAM;gCAAe;gCAC1D;oCAAE,GAAG;oCAAK,GAAG;oCAAO,OAAO;oCAAe,MAAM;gCAAc;6BAC/D;wBACH;oBACF;gBACF;aACD;YAED,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,MAAM;YAEvE,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,mHAAmH;QACnH,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QACxD,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,MAAM,KAAK,OAAO;YAExE,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,yIAAyI;QACzI,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG;YAExC,kCAAkC;YAClC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,aAAa;YAE7B,4EAA4E;YAC5E,IAAI,YAAY;gBACd,2BAA2B;gBAC3B,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;oBACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD,WAAW;wBACT,QAAQ,AAAC,KAAK,IAAI,GAAG,IAAK;wBAC1B,OAAO,KAAK,IAAI;wBAChB,YAAY;oBACd;gBACF;YACF;YAEA,mCAAmC;YACnC,IAAI,kBAAuB;gBACzB,MAAM,EAAE;gBACR,SAAS,EAAE;gBACX,WAAW;gBACX,QAAQ,CAAC;YACX;YAEA,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU;gBACxF,kCAAkC;gBAClC,kBAAkB;oBAChB,MAAM;wBACJ;4BAAE,IAAI;4BAAG,MAAM;4BAAiB,KAAK;4BAAI,QAAQ;4BAAO,YAAY;wBAAc;wBAClF;4BAAE,IAAI;4BAAG,MAAM;4BAAa,KAAK;4BAAI,QAAQ;4BAAO,YAAY;wBAAY;wBAC5E;4BAAE,IAAI;4BAAG,MAAM;4BAAe,KAAK;4BAAI,QAAQ;4BAAO,YAAY;wBAAS;wBAC3E;4BAAE,IAAI;4BAAG,MAAM;4BAAe,KAAK;4BAAI,QAAQ;4BAAO,YAAY;wBAAc;wBAChF;4BAAE,IAAI;4BAAG,MAAM;4BAAc,KAAK;4BAAI,QAAQ;4BAAO,YAAY;wBAAQ;qBAC1E;oBACD,SAAS;wBACP;4BAAE,MAAM;4BAAM,MAAM;4BAAU,UAAU;wBAAM;wBAC9C;4BAAE,MAAM;4BAAQ,MAAM;4BAAU,UAAU;wBAAM;wBAChD;4BAAE,MAAM;4BAAO,MAAM;4BAAU,UAAU;wBAAM;wBAC/C;4BAAE,MAAM;4BAAU,MAAM;4BAAU,UAAU;wBAAM;wBAClD;4BAAE,MAAM;4BAAc,MAAM;4BAAU,UAAU;wBAAM;qBACvD;oBACD,WAAW;oBACX,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,KAAK;wBACL,QAAQ;wBACR,YAAY;oBACd;gBACF;YACF;YAEA,kCAAkC;YAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;gBACL,SAAS;gBACT,MAAM;oBACJ,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBAC5B,UAAU,KAAK,IAAI;oBACnB,UAAU,gBAAgB,SAAS;oBACnC,aAAa,gBAAgB,OAAO,CAAC,MAAM;oBAC3C,aAAa;gBACf;YACF;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,6EAA6E;QAC7E,KAAK;QACL,GAAG;QACH,qCAAqC;QACrC,gEAAgE;QAChE,eAAe;QACf,OAAO;QACP,eAAe;QACf,6CAA6C;QAC7C,iDAAiD;QACjD,uBAAuB;QACvB,0CAA0C;QAC1C,wCAAwC;QACxC,uFAAuF;QACvF,cAAc;QACd,UAAU;QACV,QAAQ;QACR,MAAM;QACN,KAAK;QACL,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,WAAmB;QAC9D,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;YAE5D,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,qBAAqB;YACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QAEA,mCAAmC;QACnC,4CAA4C;QAC5C,wCAAwC;QACxC,wCAAwC;QACxC,KAAK;QACL,sIAAsI;QACtI,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,WAAW,QAAQ;QAAC,OAAO;YAC1B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,MAAM,SAAS;IACpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/auth-error-handling.ts"], "sourcesContent": ["// Enhanced error handling utilities for authentication and onboarding\n\nexport interface RetryConfig {\n  maxRetries: number;\n  baseDelay: number;\n  maxDelay: number;\n  backoffFactor: number;\n}\n\nexport interface AuthError {\n  code: string;\n  message: string;\n  retryable: boolean;\n  userMessage: string;\n  timestamp: string;\n}\n\nexport const DEFAULT_RETRY_CONFIG: RetryConfig = {\n  maxRetries: 3,\n  baseDelay: 1000, // 1 second\n  maxDelay: 10000, // 10 seconds\n  backoffFactor: 2,\n};\n\n/**\n * Exponential backoff retry utility\n */\nexport async function retryWithBackoff<T>(\n  operation: () => Promise<T>,\n  config: RetryConfig = DEFAULT_RETRY_CONFIG,\n  operationName: string = 'operation'\n): Promise<T> {\n  let lastError: Error;\n  \n  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {\n    try {\n      console.log(`Attempting ${operationName} (attempt ${attempt + 1}/${config.maxRetries + 1})`);\n      const result = await operation();\n      \n      if (attempt > 0) {\n        console.log(`${operationName} succeeded after ${attempt + 1} attempts`);\n      }\n      \n      return result;\n    } catch (error) {\n      lastError = error as Error;\n      console.warn(`${operationName} failed on attempt ${attempt + 1}:`, error);\n      \n      // Don't retry on the last attempt\n      if (attempt === config.maxRetries) {\n        break;\n      }\n      \n      // Calculate delay with exponential backoff\n      const delay = Math.min(\n        config.baseDelay * Math.pow(config.backoffFactor, attempt),\n        config.maxDelay\n      );\n      \n      console.log(`Retrying ${operationName} in ${delay}ms...`);\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n  \n  console.error(`${operationName} failed after ${config.maxRetries + 1} attempts`);\n  throw lastError!;\n}\n\n/**\n * Categorize authentication errors\n */\nexport function categorizeAuthError(error: any): AuthError {\n  const timestamp = new Date().toISOString();\n  \n  // Network errors\n  if (error.code === 'NETWORK_ERROR' || error.message?.includes('network') || error.message?.includes('fetch')) {\n    return {\n      code: 'NETWORK_ERROR',\n      message: error.message || 'Network error occurred',\n      retryable: true,\n      userMessage: 'Network connection issue. Please check your internet connection and try again.',\n      timestamp,\n    };\n  }\n  \n  // Token errors\n  if (error.response?.status === 401 || error.message?.includes('token') || error.message?.includes('unauthorized')) {\n    return {\n      code: 'TOKEN_ERROR',\n      message: error.message || 'Authentication token invalid',\n      retryable: false,\n      userMessage: 'Your session has expired. Please log in again.',\n      timestamp,\n    };\n  }\n  \n  // Server errors (5xx)\n  if (error.response?.status >= 500) {\n    return {\n      code: 'SERVER_ERROR',\n      message: error.message || 'Server error occurred',\n      retryable: true,\n      userMessage: 'Server is temporarily unavailable. Please try again in a moment.',\n      timestamp,\n    };\n  }\n  \n  // Email already exists (409 Conflict)\n  if (error.response?.status === 409) {\n    const errorData = error.response?.data?.error;\n    if (errorData?.code === 'EMAIL_ALREADY_EXISTS') {\n      return {\n        code: 'EMAIL_ALREADY_EXISTS',\n        message: errorData.message || 'Email already exists',\n        retryable: false,\n        userMessage: errorData.message || 'An account with this email address already exists. Please use a different email or try logging in.',\n        timestamp,\n      };\n    }\n\n    // Generic 409 conflict\n    return {\n      code: 'CONFLICT_ERROR',\n      message: error.response?.data?.error?.message || error.message || 'Conflict error occurred',\n      retryable: false,\n      userMessage: error.response?.data?.error?.message || 'There was a conflict with your request. Please try again.',\n      timestamp,\n    };\n  }\n\n  // Client errors (4xx)\n  if (error.response?.status >= 400 && error.response?.status < 500) {\n    // Check if there's a structured error response\n    const errorData = error.response?.data?.error;\n    if (errorData?.message) {\n      return {\n        code: errorData.code || 'CLIENT_ERROR',\n        message: errorData.message,\n        retryable: false,\n        userMessage: errorData.message,\n        timestamp,\n      };\n    }\n\n    return {\n      code: 'CLIENT_ERROR',\n      message: error.message || 'Client error occurred',\n      retryable: false,\n      userMessage: 'There was an issue with your request. Please try again or contact support.',\n      timestamp,\n    };\n  }\n  \n  // Onboarding specific errors\n  if (error.message?.includes('onboarding') || error.message?.includes('is_new_user')) {\n    return {\n      code: 'ONBOARDING_ERROR',\n      message: error.message || 'Onboarding error occurred',\n      retryable: true,\n      userMessage: 'There was an issue completing your setup. Please try again.',\n      timestamp,\n    };\n  }\n  \n  // Generic error\n  return {\n    code: 'UNKNOWN_ERROR',\n    message: error.message || 'An unknown error occurred',\n    retryable: true,\n    userMessage: 'An unexpected error occurred. Please try again.',\n    timestamp,\n  };\n}\n\n/**\n * Safe API call wrapper with error handling\n */\nexport async function safeApiCall<T>(\n  apiCall: () => Promise<T>,\n  operationName: string,\n  retryConfig?: Partial<RetryConfig>\n): Promise<{ data?: T; error?: AuthError }> {\n  try {\n    const data = await retryWithBackoff(\n      apiCall,\n      { ...DEFAULT_RETRY_CONFIG, ...retryConfig },\n      operationName\n    );\n    return { data };\n  } catch (error) {\n    const authError = categorizeAuthError(error);\n    console.error(`Safe API call failed for ${operationName}:`, authError);\n    return { error: authError };\n  }\n}\n\n/**\n * Validate authentication response structure\n */\nexport function validateAuthResponse(response: any): boolean {\n  if (!response || typeof response !== 'object') {\n    return false;\n  }\n  \n  // Required fields\n  const requiredFields = ['access_token', 'user_id'];\n  for (const field of requiredFields) {\n    if (!response[field] || typeof response[field] !== 'string') {\n      return false;\n    }\n  }\n  \n  // Optional but validated fields\n  if (response.is_new_user !== undefined && typeof response.is_new_user !== 'boolean') {\n    return false;\n  }\n  \n  return true;\n}\n\n/**\n * Normalize authentication response with defaults\n */\nexport function normalizeAuthResponse(response: any): any {\n  if (!validateAuthResponse(response)) {\n    throw new Error('Invalid authentication response structure');\n  }\n  \n  return {\n    ...response,\n    // Default is_new_user to false if not provided (for legacy users)\n    is_new_user: response.is_new_user ?? false,\n    // Ensure token_type is set\n    token_type: response.token_type || 'bearer',\n  };\n}\n\n/**\n * Check if error indicates infinite redirect loop\n */\nexport function isRedirectLoopError(error: any, redirectHistory: string[]): boolean {\n  // Check if we've been redirecting between the same routes repeatedly\n  if (redirectHistory.length >= 6) {\n    const recent = redirectHistory.slice(-6);\n    const uniqueRoutes = new Set(recent);\n    \n    // If we only have 2-3 unique routes in recent history, it's likely a loop\n    return uniqueRoutes.size <= 3;\n  }\n  \n  return false;\n}\n\n/**\n * Log error for debugging (production-safe)\n */\nexport function logAuthError(error: AuthError, context: string): void {\n  const logData = {\n    context,\n    code: error.code,\n    message: error.message,\n    timestamp: error.timestamp,\n    retryable: error.retryable,\n    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',\n    url: typeof window !== 'undefined' ? window.location.href : 'unknown',\n  };\n  \n  // In production, you might want to send this to an error tracking service\n  if (process.env.NODE_ENV === 'production') {\n    // Example: Send to error tracking service\n    // errorTrackingService.log(logData);\n    console.error('Auth Error:', logData);\n  } else {\n    console.error('Auth Error:', logData);\n  }\n}\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;;;;;;;AAiB/D,MAAM,uBAAoC;IAC/C,YAAY;IACZ,WAAW;IACX,UAAU;IACV,eAAe;AACjB;AAKO,eAAe,iBACpB,SAA2B,EAC3B,SAAsB,oBAAoB,EAC1C,gBAAwB,WAAW;IAEnC,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,OAAO,UAAU,EAAE,UAAW;QAC7D,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,UAAU,EAAE,UAAU,EAAE,CAAC,EAAE,OAAO,UAAU,GAAG,EAAE,CAAC,CAAC;YAC3F,MAAM,SAAS,MAAM;YAErB,IAAI,UAAU,GAAG;gBACf,QAAQ,GAAG,CAAC,GAAG,cAAc,iBAAiB,EAAE,UAAU,EAAE,SAAS,CAAC;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,YAAY;YACZ,QAAQ,IAAI,CAAC,GAAG,cAAc,mBAAmB,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE;YAEnE,kCAAkC;YAClC,IAAI,YAAY,OAAO,UAAU,EAAE;gBACjC;YACF;YAEA,2CAA2C;YAC3C,MAAM,QAAQ,KAAK,GAAG,CACpB,OAAO,SAAS,GAAG,KAAK,GAAG,CAAC,OAAO,aAAa,EAAE,UAClD,OAAO,QAAQ;YAGjB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,cAAc,IAAI,EAAE,MAAM,KAAK,CAAC;YACxD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;IACF;IAEA,QAAQ,KAAK,CAAC,GAAG,cAAc,cAAc,EAAE,OAAO,UAAU,GAAG,EAAE,SAAS,CAAC;IAC/E,MAAM;AACR;AAKO,SAAS,oBAAoB,KAAU;IAC5C,MAAM,YAAY,IAAI,OAAO,WAAW;IAExC,iBAAiB;IACjB,IAAI,MAAM,IAAI,KAAK,mBAAmB,MAAM,OAAO,EAAE,SAAS,cAAc,MAAM,OAAO,EAAE,SAAS,UAAU;QAC5G,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,WAAW;YACX,aAAa;YACb;QACF;IACF;IAEA,eAAe;IACf,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,OAAO,EAAE,SAAS,YAAY,MAAM,OAAO,EAAE,SAAS,iBAAiB;QACjH,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,WAAW;YACX,aAAa;YACb;QACF;IACF;IAEA,sBAAsB;IACtB,IAAI,MAAM,QAAQ,EAAE,UAAU,KAAK;QACjC,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,WAAW;YACX,aAAa;YACb;QACF;IACF;IAEA,sCAAsC;IACtC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM;QACxC,IAAI,WAAW,SAAS,wBAAwB;YAC9C,OAAO;gBACL,MAAM;gBACN,SAAS,UAAU,OAAO,IAAI;gBAC9B,WAAW;gBACX,aAAa,UAAU,OAAO,IAAI;gBAClC;YACF;QACF;QAEA,uBAAuB;QACvB,OAAO;YACL,MAAM;YACN,SAAS,MAAM,QAAQ,EAAE,MAAM,OAAO,WAAW,MAAM,OAAO,IAAI;YAClE,WAAW;YACX,aAAa,MAAM,QAAQ,EAAE,MAAM,OAAO,WAAW;YACrD;QACF;IACF;IAEA,sBAAsB;IACtB,IAAI,MAAM,QAAQ,EAAE,UAAU,OAAO,MAAM,QAAQ,EAAE,SAAS,KAAK;QACjE,+CAA+C;QAC/C,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM;QACxC,IAAI,WAAW,SAAS;YACtB,OAAO;gBACL,MAAM,UAAU,IAAI,IAAI;gBACxB,SAAS,UAAU,OAAO;gBAC1B,WAAW;gBACX,aAAa,UAAU,OAAO;gBAC9B;YACF;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,WAAW;YACX,aAAa;YACb;QACF;IACF;IAEA,6BAA6B;IAC7B,IAAI,MAAM,OAAO,EAAE,SAAS,iBAAiB,MAAM,OAAO,EAAE,SAAS,gBAAgB;QACnF,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,WAAW;YACX,aAAa;YACb;QACF;IACF;IAEA,gBAAgB;IAChB,OAAO;QACL,MAAM;QACN,SAAS,MAAM,OAAO,IAAI;QAC1B,WAAW;QACX,aAAa;QACb;IACF;AACF;AAKO,eAAe,YACpB,OAAyB,EACzB,aAAqB,EACrB,WAAkC;IAElC,IAAI;QACF,MAAM,OAAO,MAAM,iBACjB,SACA;YAAE,GAAG,oBAAoB;YAAE,GAAG,WAAW;QAAC,GAC1C;QAEF,OAAO;YAAE;QAAK;IAChB,EAAE,OAAO,OAAO;QACd,MAAM,YAAY,oBAAoB;QACtC,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,EAAE;QAC5D,OAAO;YAAE,OAAO;QAAU;IAC5B;AACF;AAKO,SAAS,qBAAqB,QAAa;IAChD,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;QAC7C,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QAAC;QAAgB;KAAU;IAClD,KAAK,MAAM,SAAS,eAAgB;QAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU;YAC3D,OAAO;QACT;IACF;IAEA,gCAAgC;IAChC,IAAI,SAAS,WAAW,KAAK,aAAa,OAAO,SAAS,WAAW,KAAK,WAAW;QACnF,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAAS,sBAAsB,QAAa;IACjD,IAAI,CAAC,qBAAqB,WAAW;QACnC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QACL,GAAG,QAAQ;QACX,kEAAkE;QAClE,aAAa,SAAS,WAAW,IAAI;QACrC,2BAA2B;QAC3B,YAAY,SAAS,UAAU,IAAI;IACrC;AACF;AAKO,SAAS,oBAAoB,KAAU,EAAE,eAAyB;IACvE,qEAAqE;IACrE,IAAI,gBAAgB,MAAM,IAAI,GAAG;QAC/B,MAAM,SAAS,gBAAgB,KAAK,CAAC,CAAC;QACtC,MAAM,eAAe,IAAI,IAAI;QAE7B,0EAA0E;QAC1E,OAAO,aAAa,IAAI,IAAI;IAC9B;IAEA,OAAO;AACT;AAKO,SAAS,aAAa,KAAgB,EAAE,OAAe;IAC5D,MAAM,UAAU;QACd;QACA,MAAM,MAAM,IAAI;QAChB,SAAS,MAAM,OAAO;QACtB,WAAW,MAAM,SAAS;QAC1B,WAAW,MAAM,SAAS;QAC1B,WAAW,6EAA6D;QACxE,KAAK,6EAAuD;IAC9D;IAEA,0EAA0E;IAC1E,uCAA2C;;IAI3C,OAAO;QACL,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/providers/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { TokenResponse, LoginRequest, RegisterRequest, AuthContextType, OAuthTokens } from '@/types';\r\nimport { useApi } from './ApiContext';\r\nimport { STORAGE_KEYS } from '@/lib/constants';\r\n// import { getRedirectPath } from '@/lib/utils/onboarding';\r\nimport { safeApiCall, normalizeAuthResponse, logAuthError } from '@/lib/utils/auth-error-handling';\r\nimport axios from 'axios';\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const [user, setUser] = useState<TokenResponse | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false); // Changed to false - no automatic loading\r\n  const [isNewUser, setIsNewUser] = useState<boolean>(false);\r\n  const [lastStateCheck, setLastStateCheck] = useState<number>(Date.now());\r\n  const [isCompletingOnboarding, setIsCompletingOnboarding] = useState<boolean>(false);\r\n  const [hasAttemptedAutoAuth, setHasAttemptedAutoAuth] = useState<boolean>(false);\r\n  const { loginUser, registerUser, refreshToken: refreshTokenApi, logoutUser, getUserProfile, completeOnboarding: completeOnboardingApi } = useApi();\r\n  const router = useRouter();\r\n  // const pathname = usePathname();\r\n\r\n  // Refresh user state from backend with enhanced error handling\r\n  const refreshUserState = useCallback(async () => {\r\n    if (!user) return;\r\n\r\n    const { data: profile, error } = await safeApiCall(\r\n      () => getUserProfile(),\r\n      'refresh user state',\r\n      { maxRetries: 2 } // Fewer retries for background operations\r\n    );\r\n\r\n    if (profile) {\r\n      setIsNewUser(profile.is_new_user || false);\r\n      setLastStateCheck(Date.now());\r\n      console.log('User state refreshed, is_new_user:', profile.is_new_user);\r\n    } else if (error) {\r\n      logAuthError(error, 'refreshUserState');\r\n      // Don't update state on error to avoid disrupting user experience\r\n      // But log the error for debugging\r\n    }\r\n  }, [user, getUserProfile, setIsNewUser, setLastStateCheck]);\r\n\r\n  // Check if state refresh is needed (every 5 minutes)\r\n  const shouldRefreshState = useCallback(() => {\r\n    const fiveMinutes = 5 * 60 * 1000;\r\n    return Date.now() - lastStateCheck > fiveMinutes;\r\n  }, [lastStateCheck]);\r\n\r\n  // Visibility change handler for tab focus\r\n  useEffect(() => {\r\n    const handleVisibilityChange = async () => {\r\n      if (!document.hidden && user && shouldRefreshState()) {\r\n        console.log('Tab became visible, refreshing user state...');\r\n        await refreshUserState();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);\r\n  }, [user, shouldRefreshState, refreshUserState]);\r\n\r\n  // Periodic state revalidation (every 10 minutes)\r\n  useEffect(() => {\r\n    if (!user) return;\r\n\r\n    const interval = setInterval(async () => {\r\n      if (shouldRefreshState()) {\r\n        console.log('Periodic state refresh triggered...');\r\n        await refreshUserState();\r\n      }\r\n    }, 10 * 60 * 1000); // 10 minutes\r\n\r\n    return () => clearInterval(interval);\r\n  }, [user, shouldRefreshState, refreshUserState]);\r\n\r\n  // Manual authentication initialization - only called when user explicitly signs in\r\n  const initializeAuthFromStorage = useCallback(async (): Promise<boolean> => {\r\n    console.log('Manually initializing authentication from storage...');\r\n    setIsLoading(true);\r\n\r\n    const accessToken = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n    const userId = localStorage.getItem(STORAGE_KEYS.USER_ID);\r\n    const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n    const expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);\r\n\r\n    if (accessToken && userId && refreshToken && expiresAt) {\r\n      try {\r\n        // Prepare token data structure and set it so that other components can access it while we validate.\r\n        const tokenData: TokenResponse = {\r\n          access_token: accessToken,\r\n          user_id: userId,\r\n          refresh_token: refreshToken,\r\n          expires_at: expiresAt,\r\n          token_type: 'bearer',\r\n        };\r\n\r\n        // Optimistically set the user so downstream hooks/components can read it while we validate.\r\n        setUser(tokenData);\r\n\r\n        // Validate access token by attempting to fetch the user profile.\r\n        const profile = await getUserProfile();\r\n        setIsNewUser(profile.is_new_user || false);\r\n        setLastStateCheck(Date.now());\r\n        console.log('User profile fetched, is_new_user:', profile.is_new_user);\r\n\r\n        // 🔄 If the access token is going to expire in the next 5 minutes, refresh it proactively.\r\n        try {\r\n          const expirationTime = new Date(expiresAt).getTime();\r\n          const bufferTime = 5 * 60 * 1000; // 5 minutes\r\n          if (Date.now() > (expirationTime - bufferTime)) {\r\n            console.log('Access token nearing expiry. Refreshing…');\r\n            const newTokenData = await refreshTokenApi();\r\n            setUser(newTokenData);\r\n          }\r\n        } catch (refreshCheckError) {\r\n          console.warn('Proactive refresh check failed:', refreshCheckError);\r\n        }\r\n\r\n        setIsLoading(false);\r\n        return true; // ✅ Stored token is still valid (and refreshed if necessary)\r\n              } catch (error: unknown) {\r\n        if (axios.isAxiosError(error) && error.response?.status === 401) {\r\n          // Stored access token is no longer valid – try to refresh it.\r\n          console.warn('Access token invalid (401). Attempting refresh…');\r\n          try {\r\n            const newTokenData = await refreshTokenApi();\r\n            console.log('Token refresh successful after 401');\r\n            setUser(newTokenData);\r\n            const refreshedProfile = await getUserProfile();\r\n            setIsNewUser(refreshedProfile.is_new_user || false);\r\n            setLastStateCheck(Date.now());\r\n            setIsLoading(false);\r\n            return true;\r\n          } catch (refreshError) {\r\n            console.error('Token refresh failed after 401:', refreshError);\r\n          }\r\n        } else {\r\n          console.error('Failed to fetch user profile during initialization:', error);\r\n        }\r\n\r\n        // At this point we either couldn't refresh or had another error – clear auth data.\r\n        localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n        localStorage.removeItem(STORAGE_KEYS.USER_ID);\r\n        localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);\r\n        setUser(null);\r\n        setIsLoading(false);\r\n        return false;\r\n      }\r\n    } else {\r\n      console.log('No complete auth data found in localStorage');\r\n      setIsLoading(false);\r\n      return false;\r\n    }\r\n  }, [refreshTokenApi, getUserProfile]);\r\n\r\n  // Check if user has valid stored authentication on app load (but don't auto-authenticate)\r\n  useEffect(() => {\r\n    if (!hasAttemptedAutoAuth) {\r\n      setHasAttemptedAutoAuth(true);\r\n      const hasStoredAuth = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN) &&\r\n                           localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n\r\n      if (hasStoredAuth) {\r\n        console.log('Found stored auth data, but not auto-authenticating. User must manually sign in.');\r\n      }\r\n    }\r\n  }, [hasAttemptedAutoAuth]);\r\n\r\n  // Disabled automatic redirects - users must manually navigate\r\n  // This allows the landing page to be truly public\r\n  // Protected routes will handle their own authentication checks\r\n\r\n  const login = async (credentials: LoginRequest) => {\r\n    const { data, error } = await safeApiCall(\r\n      () => loginUser(credentials),\r\n      'user login'\r\n    );\r\n\r\n    if (error) {\r\n      logAuthError(error, 'login');\r\n      throw new Error(error.userMessage);\r\n    }\r\n\r\n    if (data) {\r\n      const normalizedData = normalizeAuthResponse(data);\r\n      setUser(normalizedData);\r\n      setIsNewUser(normalizedData.is_new_user);\r\n      setLastStateCheck(Date.now());\r\n\r\n      // Redirect based on user status\r\n      if (normalizedData.is_new_user) {\r\n        router.push('/onboarding');\r\n      } else {\r\n        router.push('/chat');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Manual sign in function that checks stored tokens first\r\n  const signIn = async (credentials?: LoginRequest, shouldRedirect: boolean = true) => {\r\n    // First try to authenticate from stored tokens\r\n    const hasValidStoredAuth = await initializeAuthFromStorage();\r\n\r\n    if (hasValidStoredAuth) {\r\n      console.log('Authenticated from stored tokens');\r\n      // Only redirect if explicitly requested (for manual sign-in from landing page)\r\n      if (shouldRedirect) {\r\n        if (isNewUser) {\r\n          router.push('/onboarding');\r\n        } else {\r\n          router.push('/chat');\r\n        }\r\n      }\r\n      return;\r\n    }\r\n\r\n    // If no valid stored auth and credentials provided, perform login\r\n    if (credentials) {\r\n      await login(credentials);\r\n    } else if (shouldRedirect) {\r\n      // No stored auth and no credentials - redirect to login page only if redirect is requested\r\n      router.push('/login');\r\n    } else {\r\n      // For ProtectedRoute scenarios, throw error to indicate authentication failed\r\n      throw new Error('No valid authentication found');\r\n    }\r\n  };\r\n\r\n  const register = async (credentials: RegisterRequest) => {\r\n    const { data, error } = await safeApiCall(\r\n      () => registerUser(credentials),\r\n      'user registration'\r\n    );\r\n\r\n    if (error) {\r\n      logAuthError(error, 'register');\r\n      throw new Error(error.userMessage);\r\n    }\r\n\r\n    if (data) {\r\n      // Registration successful, now automatically log the user in\r\n      console.log('Registration successful, automatically logging in user...');\r\n\r\n      try {\r\n        // Use the same credentials to log in the user\r\n        const loginCredentials: LoginRequest = {\r\n          username: credentials.email, // Backend expects username field for email\r\n          password: credentials.password\r\n        };\r\n\r\n        const { data: loginData, error: loginError } = await safeApiCall(\r\n          () => loginUser(loginCredentials),\r\n          'auto-login after registration'\r\n        );\r\n\r\n        if (loginError) {\r\n          logAuthError(loginError, 'auto-login after registration');\r\n          // If auto-login fails, redirect to login with success message\r\n          console.warn('Auto-login failed after registration, redirecting to login page');\r\n          router.push('/login?registration=success');\r\n          return;\r\n        }\r\n\r\n        if (loginData) {\r\n          const normalizedData = normalizeAuthResponse(loginData);\r\n          setUser(normalizedData);\r\n          setIsNewUser(true); // New registrations are always new users\r\n          setLastStateCheck(Date.now());\r\n\r\n          console.log('Auto-login successful, redirecting to onboarding...');\r\n          // Redirect new user to onboarding\r\n          router.push('/onboarding');\r\n        }\r\n      } catch (autoLoginError) {\r\n        console.error('Auto-login failed after registration:', autoLoginError);\r\n        // Fallback to login page with success message\r\n        router.push('/login?registration=success');\r\n      }\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await logoutUser();\r\n    } catch (error) {\r\n      console.error('Logout API call failed:', error);\r\n    }\r\n\r\n    // Clear all auth data\r\n    localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\r\n    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\r\n    localStorage.removeItem(STORAGE_KEYS.USER_ID);\r\n    localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);\r\n    localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);\r\n\r\n    setUser(null);\r\n    setIsNewUser(false);\r\n    setHasAttemptedAutoAuth(false);\r\n\r\n    // Redirect to landing page instead of login\r\n    router.push('/');\r\n  };\r\n\r\n  const refreshUserToken = async () => {\r\n    try {\r\n      const newTokenData = await refreshTokenApi();\r\n      setUser(newTokenData);\r\n\r\n      // Extract is_new_user from refresh response if available\r\n      if (newTokenData.is_new_user !== undefined) {\r\n        setIsNewUser(newTokenData.is_new_user);\r\n        setLastStateCheck(Date.now());\r\n      } else if (shouldRefreshState()) {\r\n        // If refresh response doesn't include is_new_user and it's been a while, fetch it\r\n        await refreshUserState();\r\n      }\r\n    } catch (error) {\r\n      console.error('Token refresh failed:', error);\r\n      setUser(null);\r\n      setIsNewUser(false);\r\n      setLastStateCheck(Date.now());\r\n      router.push('/login');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const completeOnboarding = async () => {\r\n    try {\r\n      setIsCompletingOnboarding(true);\r\n      console.log('🚀 Starting onboarding completion...');\r\n\r\n      // Call the backend to mark onboarding as complete\r\n      await completeOnboardingApi();\r\n      console.log('✅ Backend onboarding completion successful');\r\n\r\n      // Fetch updated user profile to confirm is_new_user is now false\r\n      const updatedProfile = await getUserProfile();\r\n      console.log('✅ Updated profile fetched:', { is_new_user: updatedProfile.is_new_user });\r\n\r\n      setIsNewUser(updatedProfile.is_new_user || false);\r\n      setLastStateCheck(Date.now());\r\n\r\n      console.log('✅ User state updated, redirecting to dashboard...');\r\n\r\n      // Use setTimeout to ensure state updates are processed before redirect\r\n      setTimeout(() => {\r\n        setIsCompletingOnboarding(false);\r\n        router.push('/chat');\r\n      }, 100);\r\n\r\n    } catch (error) {\r\n      console.error('❌ Failed to complete onboarding:', error);\r\n      setIsCompletingOnboarding(false);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleOAuthCallback = useCallback(async (tokens: OAuthTokens) => {\r\n    try {\r\n      // Calculate expires_at from expires_in (seconds to ISO string)\r\n      const expiresAt = new Date(Date.now() + tokens.expires_in * 1000).toISOString();\r\n\r\n      // Store tokens in localStorage using existing STORAGE_KEYS\r\n      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.access_token);\r\n      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refresh_token);\r\n      localStorage.setItem(STORAGE_KEYS.USER_ID, tokens.user_id);\r\n      localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt);\r\n      localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, tokens.token_type || 'bearer');\r\n\r\n      // Create token data for auth context\r\n      const tokenData: TokenResponse = {\r\n        access_token: tokens.access_token,\r\n        token_type: tokens.token_type || 'bearer',\r\n        user_id: tokens.user_id,\r\n        expires_at: expiresAt,\r\n        refresh_token: tokens.refresh_token\r\n      };\r\n\r\n      // Update auth context\r\n      setUser(tokenData);\r\n\r\n      // For OAuth, we need to fetch user profile to get is_new_user status\r\n      try {\r\n        const profile = await getUserProfile();\r\n        setIsNewUser(profile.is_new_user || false);\r\n        setLastStateCheck(Date.now());\r\n        console.log('OAuth user profile fetched, is_new_user:', profile.is_new_user);\r\n      } catch (error) {\r\n        console.error('Failed to fetch user profile during OAuth callback:', error);\r\n        // Don't fail the OAuth flow if profile fetch fails\r\n        setIsNewUser(false);\r\n      }\r\n\r\n      // Don't redirect here - let the calling component handle the redirect\r\n    } catch (error) {\r\n      console.error('OAuth callback handling failed:', error);\r\n      throw error;\r\n    }\r\n  }, [setUser]);\r\n\r\n\r\n\r\n  return (\r\n    <AuthContext.Provider value={{\r\n      isAuthenticated: !!user,\r\n      user,\r\n      isLoading,\r\n      isNewUser,\r\n      isCompletingOnboarding,\r\n      login,\r\n      signIn,\r\n      initializeAuthFromStorage,\r\n      register,\r\n      logout,\r\n      setUser,\r\n      refreshUserToken,\r\n      handleOAuthCallback,\r\n      completeOnboarding\r\n    }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = () => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n}; "], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AAAA;AACA,4DAA4D;AAC5D;AACA;AATA;;;;;;;;AAWA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,0CAA0C;IAC7F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,KAAK,GAAG;IACrE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9E,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1E,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,oBAAoB,qBAAqB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IAC/I,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,kCAAkC;IAElC,+DAA+D;IAC/D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,CAAC,MAAM;QAEX,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,gJAAA,CAAA,cAAW,AAAD,EAC/C,IAAM,kBACN,sBACA;YAAE,YAAY;QAAE,EAAE,0CAA0C;;QAG9D,IAAI,SAAS;YACX,aAAa,QAAQ,WAAW,IAAI;YACpC,kBAAkB,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC,sCAAsC,QAAQ,WAAW;QACvE,OAAO,IAAI,OAAO;YAChB,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,OAAO;QACpB,kEAAkE;QAClE,kCAAkC;QACpC;IACF,GAAG;QAAC;QAAM;QAAgB;QAAc;KAAkB;IAE1D,qDAAqD;IACrD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM,cAAc,IAAI,KAAK;QAC7B,OAAO,KAAK,GAAG,KAAK,iBAAiB;IACvC,GAAG;QAAC;KAAe;IAEnB,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,IAAI,CAAC,SAAS,MAAM,IAAI,QAAQ,sBAAsB;gBACpD,QAAQ,GAAG,CAAC;gBACZ,MAAM;YACR;QACF;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO,IAAM,SAAS,mBAAmB,CAAC,oBAAoB;IAChE,GAAG;QAAC;QAAM;QAAoB;KAAiB;IAE/C,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;QAEX,MAAM,WAAW,YAAY;YAC3B,IAAI,sBAAsB;gBACxB,QAAQ,GAAG,CAAC;gBACZ,MAAM;YACR;QACF,GAAG,KAAK,KAAK,OAAO,aAAa;QAEjC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAM;QAAoB;KAAiB;IAE/C,mFAAmF;IACnF,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5C,QAAQ,GAAG,CAAC;QACZ,aAAa;QAEb,MAAM,cAAc,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;QAClE,MAAM,SAAS,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO;QACxD,MAAM,eAAe,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;QACpE,MAAM,YAAY,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;QAE9D,IAAI,eAAe,UAAU,gBAAgB,WAAW;YACtD,IAAI;gBACF,oGAAoG;gBACpG,MAAM,YAA2B;oBAC/B,cAAc;oBACd,SAAS;oBACT,eAAe;oBACf,YAAY;oBACZ,YAAY;gBACd;gBAEA,4FAA4F;gBAC5F,QAAQ;gBAER,iEAAiE;gBACjE,MAAM,UAAU,MAAM;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,kBAAkB,KAAK,GAAG;gBAC1B,QAAQ,GAAG,CAAC,sCAAsC,QAAQ,WAAW;gBAErE,2FAA2F;gBAC3F,IAAI;oBACF,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;oBAClD,MAAM,aAAa,IAAI,KAAK,MAAM,YAAY;oBAC9C,IAAI,KAAK,GAAG,KAAM,iBAAiB,YAAa;wBAC9C,QAAQ,GAAG,CAAC;wBACZ,MAAM,eAAe,MAAM;wBAC3B,QAAQ;oBACV;gBACF,EAAE,OAAO,mBAAmB;oBAC1B,QAAQ,IAAI,CAAC,mCAAmC;gBAClD;gBAEA,aAAa;gBACb,OAAO,MAAM,6DAA6D;YACpE,EAAE,OAAO,OAAgB;gBAC/B,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAC/D,8DAA8D;oBAC9D,QAAQ,IAAI,CAAC;oBACb,IAAI;wBACF,MAAM,eAAe,MAAM;wBAC3B,QAAQ,GAAG,CAAC;wBACZ,QAAQ;wBACR,MAAM,mBAAmB,MAAM;wBAC/B,aAAa,iBAAiB,WAAW,IAAI;wBAC7C,kBAAkB,KAAK,GAAG;wBAC1B,aAAa;wBACb,OAAO;oBACT,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,uDAAuD;gBACvE;gBAEA,mFAAmF;gBACnF,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;gBACjD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;gBAClD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO;gBAC5C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;gBAC/C,QAAQ;gBACR,aAAa;gBACb,OAAO;YACT;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb,OAAO;QACT;IACF,GAAG;QAAC;QAAiB;KAAe;IAEpC,0FAA0F;IAC1F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,sBAAsB;YACzB,wBAAwB;YACxB,MAAM,gBAAgB,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY,KAC/C,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;YAEpE,IAAI,eAAe;gBACjB,QAAQ,GAAG,CAAC;YACd;QACF;IACF,GAAG;QAAC;KAAqB;IAEzB,8DAA8D;IAC9D,kDAAkD;IAClD,+DAA+D;IAE/D,MAAM,QAAQ,OAAO;QACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,gJAAA,CAAA,cAAW,AAAD,EACtC,IAAM,UAAU,cAChB;QAGF,IAAI,OAAO;YACT,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YACpB,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,IAAI,MAAM;YACR,MAAM,iBAAiB,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE;YAC7C,QAAQ;YACR,aAAa,eAAe,WAAW;YACvC,kBAAkB,KAAK,GAAG;YAE1B,gCAAgC;YAChC,IAAI,eAAe,WAAW,EAAE;gBAC9B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,0DAA0D;IAC1D,MAAM,SAAS,OAAO,aAA4B,iBAA0B,IAAI;QAC9E,+CAA+C;QAC/C,MAAM,qBAAqB,MAAM;QAEjC,IAAI,oBAAoB;YACtB,QAAQ,GAAG,CAAC;YACZ,+EAA+E;YAC/E,IAAI,gBAAgB;gBAClB,IAAI,WAAW;oBACb,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;YACA;QACF;QAEA,kEAAkE;QAClE,IAAI,aAAa;YACf,MAAM,MAAM;QACd,OAAO,IAAI,gBAAgB;YACzB,2FAA2F;YAC3F,OAAO,IAAI,CAAC;QACd,OAAO;YACL,8EAA8E;YAC9E,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,gJAAA,CAAA,cAAW,AAAD,EACtC,IAAM,aAAa,cACnB;QAGF,IAAI,OAAO;YACT,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YACpB,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,IAAI,MAAM;YACR,6DAA6D;YAC7D,QAAQ,GAAG,CAAC;YAEZ,IAAI;gBACF,8CAA8C;gBAC9C,MAAM,mBAAiC;oBACrC,UAAU,YAAY,KAAK;oBAC3B,UAAU,YAAY,QAAQ;gBAChC;gBAEA,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,CAAA,GAAA,gJAAA,CAAA,cAAW,AAAD,EAC7D,IAAM,UAAU,mBAChB;gBAGF,IAAI,YAAY;oBACd,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,YAAY;oBACzB,8DAA8D;oBAC9D,QAAQ,IAAI,CAAC;oBACb,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,WAAW;oBACb,MAAM,iBAAiB,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE;oBAC7C,QAAQ;oBACR,aAAa,OAAO,yCAAyC;oBAC7D,kBAAkB,KAAK,GAAG;oBAE1B,QAAQ,GAAG,CAAC;oBACZ,kCAAkC;oBAClC,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,gBAAgB;gBACvB,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,8CAA8C;gBAC9C,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;QAEA,sBAAsB;QACtB,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY;QACjD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa;QAClD,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO;QAC5C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;QAC/C,aAAa,UAAU,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU;QAE/C,QAAQ;QACR,aAAa;QACb,wBAAwB;QAExB,4CAA4C;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,QAAQ;YAER,yDAAyD;YACzD,IAAI,aAAa,WAAW,KAAK,WAAW;gBAC1C,aAAa,aAAa,WAAW;gBACrC,kBAAkB,KAAK,GAAG;YAC5B,OAAO,IAAI,sBAAsB;gBAC/B,kFAAkF;gBAClF,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,QAAQ;YACR,aAAa;YACb,kBAAkB,KAAK,GAAG;YAC1B,OAAO,IAAI,CAAC;YACZ,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,0BAA0B;YAC1B,QAAQ,GAAG,CAAC;YAEZ,kDAAkD;YAClD,MAAM;YACN,QAAQ,GAAG,CAAC;YAEZ,iEAAiE;YACjE,MAAM,iBAAiB,MAAM;YAC7B,QAAQ,GAAG,CAAC,8BAA8B;gBAAE,aAAa,eAAe,WAAW;YAAC;YAEpF,aAAa,eAAe,WAAW,IAAI;YAC3C,kBAAkB,KAAK,GAAG;YAE1B,QAAQ,GAAG,CAAC;YAEZ,uEAAuE;YACvE,WAAW;gBACT,0BAA0B;gBAC1B,OAAO,IAAI,CAAC;YACd,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,0BAA0B;YAC1B,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7C,IAAI;YACF,+DAA+D;YAC/D,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,UAAU,GAAG,MAAM,WAAW;YAE7E,2DAA2D;YAC3D,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,YAAY,EAAE,OAAO,YAAY;YACnE,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,aAAa,EAAE,OAAO,aAAa;YACrE,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,OAAO,EAAE,OAAO,OAAO;YACzD,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU,EAAE;YAC9C,aAAa,OAAO,CAAC,8HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,OAAO,UAAU,IAAI;YAEnE,qCAAqC;YACrC,MAAM,YAA2B;gBAC/B,cAAc,OAAO,YAAY;gBACjC,YAAY,OAAO,UAAU,IAAI;gBACjC,SAAS,OAAO,OAAO;gBACvB,YAAY;gBACZ,eAAe,OAAO,aAAa;YACrC;YAEA,sBAAsB;YACtB,QAAQ;YAER,qEAAqE;YACrE,IAAI;gBACF,MAAM,UAAU,MAAM;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,kBAAkB,KAAK,GAAG;gBAC1B,QAAQ,GAAG,CAAC,4CAA4C,QAAQ,WAAW;YAC7E,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uDAAuD;gBACrE,mDAAmD;gBACnD,aAAa;YACf;QAEA,sEAAsE;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF,GAAG;QAAC;KAAQ;IAIZ,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B,iBAAiB,CAAC,CAAC;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/providers/PageHeaderContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';\r\n\r\ninterface PageHeaderInfo {\r\n  title: string;\r\n  subtitle?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n  breadcrumbs?: Array<{\r\n    label: string;\r\n    href?: string;\r\n    onClick?: () => void;\r\n  }>;\r\n}\r\n\r\ninterface PageHeaderActions {\r\n  onCreateChart?: () => void;\r\n  chartCount?: number;\r\n  maxCharts?: number;\r\n  onCreateAnalysis?: () => void;\r\n  isCreatingAnalysis?: boolean;\r\n  onCreateDashboard?: () => void;\r\n  onExport?: () => void;\r\n  onToggleChat?: () => void;\r\n  isChatOpen?: boolean;\r\n}\r\n\r\ninterface PageHeaderContextType {\r\n  pageInfo: PageHeaderInfo;\r\n  actions: PageHeaderActions;\r\n  setPageHeader: (info: PageHeaderInfo) => void;\r\n  setPageActions: (actions: PageHeaderActions) => void;\r\n  resetPageHeader: () => void;\r\n}\r\n\r\nconst defaultPageInfo: PageHeaderInfo = {\r\n  title: 'Agent Platform',\r\n  subtitle: 'AI-Powered Data Analytics',\r\n};\r\n\r\nconst defaultPageActions: PageHeaderActions = {\r\n  onCreateChart: undefined,\r\n  chartCount: 0,\r\n  maxCharts: 12,\r\n};\r\n\r\nconst PageHeaderContext = createContext<PageHeaderContextType | undefined>(undefined);\r\n\r\ninterface PageHeaderProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const PageHeaderProvider: React.FC<PageHeaderProviderProps> = ({ children }) => {\r\n  const [pageInfo, setPageInfo] = useState<PageHeaderInfo>(defaultPageInfo);\r\n  const [actions, setActions] = useState<PageHeaderActions>(defaultPageActions);\r\n\r\n  const setPageHeader = useCallback((info: PageHeaderInfo) => {\r\n    setPageInfo(info);\r\n  }, []);\r\n\r\n  const setPageActions = useCallback((newActions: PageHeaderActions) => {\r\n    setActions(newActions);\r\n  }, []);\r\n\r\n  const resetPageHeader = useCallback(() => {\r\n    setPageInfo(defaultPageInfo);\r\n    setActions(defaultPageActions);\r\n  }, []);\r\n\r\n  return (\r\n    <PageHeaderContext.Provider value={{ pageInfo, actions, setPageHeader, setPageActions, resetPageHeader }}>\r\n      {children}\r\n    </PageHeaderContext.Provider>\r\n  );\r\n};\r\n\r\nexport const usePageHeader = (): PageHeaderContextType => {\r\n  const context = useContext(PageHeaderContext);\r\n  if (context === undefined) {\r\n    throw new Error('usePageHeader must be used within a PageHeaderProvider');\r\n  }\r\n  return context;\r\n}; "], "names": [], "mappings": ";;;;;AACA;AADA;;;AAkCA,MAAM,kBAAkC;IACtC,OAAO;IACP,UAAU;AACZ;AAEA,MAAM,qBAAwC;IAC5C,eAAe;IACf,YAAY;IACZ,WAAW;AACb;AAEA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAMpE,MAAM,qBAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAE1D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY;QACZ,WAAW;IACb,GAAG,EAAE;IAEL,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;YAAE;YAAU;YAAS;YAAe;YAAgB;QAAgB;kBACpG;;;;;;AAGP;AAEO,MAAM,gBAAgB;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/common/ClientLayout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode } from 'react';\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { ApiProvider } from \"@/providers/ApiContext\";\r\nimport { AuthProvider } from \"@/providers/AuthContext\";\r\nimport { PageHeaderProvider } from \"@/providers/PageHeaderContext\";\r\n\r\n// Create a client\r\nconst queryClient = new QueryClient();\r\n\r\ninterface ClientLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      <ApiProvider>\r\n        <AuthProvider>\r\n          <PageHeaderProvider>\r\n            {children}\r\n          </PageHeaderProvider>\r\n        </AuthProvider>\r\n      </ApiProvider>\r\n    </QueryClientProvider>\r\n  );\r\n};\r\n\r\nexport default ClientLayout;"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOA,kBAAkB;AAClB,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW;AAMnC,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,8OAAC,+HAAA,CAAA,cAAW;sBACV,cAAA,8OAAC,gIAAA,CAAA,eAAY;0BACX,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;8BAChB;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 2520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/providers/ChatHistoryContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, ReactNode, useEffect, useCallback, useRef } from \"react\";\r\nimport axios from \"axios\";\r\nimport { useAuth } from \"./AuthContext\";\r\nimport { useApi } from \"./ApiContext\";\r\nimport {\r\n  ChatHistoryItem,\r\n  ChatHistoryContextType,\r\n  ChatMessage as ChatMessageType,\r\n  ChatListItem,\r\n  ApiChatMessage\r\n} from '@/types';\r\nimport { generateSessionId } from '@/lib/utils';\r\n\r\n// Local message interface for internal use\r\ninterface Message {\r\n  role: 'user' | 'agent';\r\n  content: string;\r\n  timestamp?: Date;\r\n  outputFiles?: Array<{\r\n    database_name: string;\r\n    file_path: string;\r\n    format: string;\r\n  }>;\r\n  sqlQueries?: Record<string, string>;\r\n}\r\n\r\nconst ChatHistoryContext = createContext<ChatHistoryContextType | undefined>(undefined);\r\n\r\nexport const ChatHistoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const { user, isAuthenticated, logout, isLoading: authLoading } = useAuth();\r\n  const { listUserChats, getChatHistory, deleteChat: apiDeleteChat } = useApi();\r\n  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);\r\n  const [activeChat, setActiveChatState] = useState<ChatHistoryItem | null>(null);\r\n  const [chatMessages, setChatMessages] = useState<{ [sessionId: string]: Message[] }>({});\r\n  const [isLoadingChats, setIsLoadingChats] = useState(false);\r\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false);\r\n  const [pendingFirstMessage, setPendingFirstMessage] = useState<string | null>(null);\r\n  const [isCreatingChat, setIsCreatingChat] = useState(false);\r\n\r\n  const ongoingLoads = useRef(new Set<string>());\r\n  const hasLoadedChats = useRef(false); // Track if we've already loaded chats for this session\r\n  const ongoingSetActiveChat = useRef(false); // Track if setActiveChat is in progress\r\n\r\n  // Generate session ID in the format expected by backend: sess_{8_hex_chars}\r\n  const generateSessionId = useCallback((): string => {\r\n    const hexChars = '0123456789abcdef';\r\n    let result = 'sess_';\r\n    for (let i = 0; i < 8; i++) {\r\n      result += hexChars[Math.floor(Math.random() * 16)];\r\n    }\r\n    return result;\r\n  }, []);\r\n\r\n  // Convert backend ChatListItem to frontend ChatHistoryItem\r\n  const convertBackendChat = useCallback((backendChat: ChatListItem): ChatHistoryItem => {\r\n    const lastUpdated = new Date(backendChat.last_seen);\r\n    return {\r\n      id: `chat_${backendChat.session_id}`, // Create a unique frontend ID\r\n      session_id: backendChat.session_id,\r\n      title: backendChat.title || \"Untitled Chat\",\r\n      created_at: lastUpdated, // Use last_seen as approximation for created_at\r\n      last_updated: lastUpdated,\r\n      message_count: backendChat.message_count || 0,\r\n    };\r\n  }, []);\r\n\r\n  // Convert backend ChatMessage to frontend Message\r\n  const convertBackendMessage = useCallback((backendMessage: ApiChatMessage): Message => {\r\n    return {\r\n      role: backendMessage.role === 'assistant' ? 'agent' : backendMessage.role as 'user',\r\n      content: backendMessage.content,\r\n      timestamp: new Date(), // Backend doesn't provide timestamp, use current time\r\n    };\r\n  }, []);\r\n\r\n  // Load conversation history for a specific session\r\n  const loadChatHistory = useCallback(async (sessionId: string): Promise<void> => {\r\n    if (!isAuthenticated || !user) return;\r\n\r\n    // Prevent concurrent loads for the same session\r\n    if (ongoingLoads.current.has(sessionId)) {\r\n      console.log(`Load for session ${sessionId} already in progress, skipping.`);\r\n      return;\r\n    }\r\n\r\n    setIsLoadingHistory(true);\r\n    ongoingLoads.current.add(sessionId); // Mark as loading\r\n\r\n    try {\r\n      const backendMessages = await getChatHistory(sessionId);\r\n      const convertedMessages = backendMessages.map(convertBackendMessage);\r\n      \r\n      setChatMessages((prev: { [sessionId: string]: Message[] }) => ({\r\n        ...prev,\r\n        [sessionId]: convertedMessages,\r\n      }));\r\n      // Update message_count in chatHistory to reflect actual loaded messages\r\n      setChatHistory((prevChatHistory: ChatHistoryItem[]) =>\r\n        prevChatHistory.map((chatItem) =>\r\n          chatItem.session_id === sessionId\r\n            ? { ...chatItem, message_count: convertedMessages.length, last_updated: new Date() }\r\n            : chatItem\r\n        )\r\n      );\r\n\r\n    } catch (error: unknown) {\r\n      console.error('Error loading chat history from backend:', error);\r\n      \r\n      // Handle 401 errors by logging out the user\r\n      if (axios.isAxiosError(error) && error.response?.status === 401) {\r\n        console.log('Authentication token invalid while loading chat history, logging out...');\r\n        logout();\r\n        return;\r\n      }\r\n      \r\n      // Handle 404 errors gracefully (chat doesn't exist yet)\r\n      if (axios.isAxiosError(error) && error.response?.status === 404) {\r\n        console.log('Chat history not found (404) - this is normal for new chats');\r\n        setChatMessages((prev: { [sessionId: string]: Message[] }) => ({\r\n          ...prev,\r\n          [sessionId]: [],\r\n        }));\r\n        // For 404, explicitly set message_count to 0 for this chat in chatHistory\r\n        setChatHistory((prevChatHistory: ChatHistoryItem[]) =>\r\n          prevChatHistory.map((chatItem) =>\r\n            chatItem.session_id === sessionId\r\n              ? { ...chatItem, message_count: 0, last_updated: new Date() }\r\n              : chatItem\r\n          )\r\n        );\r\n      } else {\r\n        // For other errors, still set empty array but log the error\r\n        console.error('Unexpected error loading chat history:', error);\r\n        setChatMessages((prev: { [sessionId: string]: Message[] }) => ({\r\n          ...prev,\r\n          [sessionId]: [],\r\n        }));\r\n        // Also set message_count to 0 here\r\n        setChatHistory((prevChatHistory: ChatHistoryItem[]) =>\r\n          prevChatHistory.map((chatItem) =>\r\n            chatItem.session_id === sessionId\r\n              ? { ...chatItem, message_count: 0, last_updated: new Date() }\r\n              : chatItem\r\n          )\r\n        );\r\n      }\r\n    } finally {\r\n      setIsLoadingHistory(false);\r\n      ongoingLoads.current.delete(sessionId); // Unmark as loading\r\n    }\r\n  }, [isAuthenticated, user, logout, getChatHistory, convertBackendMessage]);\r\n\r\n  const setActiveChat = useCallback(async (chat: ChatHistoryItem | null): Promise<void> => {\r\n    console.log('setActiveChat called with:', chat);\r\n    \r\n    // Prevent concurrent setActiveChat operations\r\n    if (ongoingSetActiveChat.current) {\r\n      console.log('setActiveChat already in progress, ignoring call');\r\n      return;\r\n    }\r\n    \r\n    // If chat is null, clear the active chat state\r\n    if (chat === null) {\r\n      setActiveChatState(null);\r\n      return;\r\n    }\r\n    \r\n    // If chat is already the active chat, skip to prevent unnecessary reloads\r\n    if (activeChat && activeChat.id === chat.id) {\r\n      console.log('Chat is already active, skipping to prevent duplicate calls');\r\n      return;\r\n    }\r\n    \r\n    ongoingSetActiveChat.current = true;\r\n    \r\n    try {\r\n      // Update the active chat state\r\n      setActiveChatState(chat);\r\n      \r\n      // Check if we already have messages for this chat\r\n      const existingMessages = chatMessages[chat.session_id] || [];\r\n      const messagesLoaded = existingMessages.length > 0;\r\n      \r\n      // Only load history if messages aren't already loaded\r\n      // Skip loading for new chats (no session_id yet)\r\n      if (!isLoadingHistory && !messagesLoaded && chat.session_id) {\r\n        console.log('Loading chat history for session:', chat.session_id);\r\n        await loadChatHistory(chat.session_id);\r\n      } else {\r\n        console.log('Skipping history load - either already loaded or new chat (no session_id yet)');\r\n      }\r\n    } finally {\r\n      ongoingSetActiveChat.current = false;\r\n    }\r\n  }, [loadChatHistory, chatMessages, activeChat, isLoadingHistory]);\r\n\r\n  const addChat = useCallback((title?: string): ChatHistoryItem => {\r\n    // Prevent multiple simultaneous chat creations\r\n    if (isCreatingChat) {\r\n      console.log('Chat creation already in progress, returning existing or throwing error');\r\n      // Return the most recent chat or throw an error\r\n      const latestChat = chatHistory[0];\r\n      if (latestChat) {\r\n        return latestChat;\r\n      }\r\n      throw new Error('Chat creation already in progress');\r\n    }\r\n\r\n    setIsCreatingChat(true);\r\n    \r\n    try {\r\n      const sessionId = generateSessionId();\r\n      const chatId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      const now = new Date();\r\n      \r\n      const newChat: ChatHistoryItem = {\r\n        id: chatId,\r\n        session_id: sessionId,\r\n        title: title || \"New Chat\",\r\n        created_at: now,\r\n        last_updated: now,\r\n        message_count: 0,\r\n      };\r\n\r\n      // Add to local state immediately for responsive UI\r\n      setChatHistory(prev => [newChat, ...prev]);\r\n      \r\n      return newChat;\r\n    } finally {\r\n      // Reset the creating state after a short delay to allow for proper state updates\r\n      setTimeout(() => {\r\n        setIsCreatingChat(false);\r\n      }, 500);\r\n    }\r\n  }, [generateSessionId, isCreatingChat, chatHistory]);\r\n\r\n  // Update a chat's session_id (used when backend creates the actual session_id)\r\n  const updateChatSessionId = useCallback((chatId: string, newSessionId: string): ChatHistoryItem | null => {\r\n    let updatedChat: ChatHistoryItem | null = null;\r\n    let oldSessionId: string | undefined = undefined;\r\n    \r\n    // First, get the old session_id and update the chat\r\n    setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => {\r\n      if (chat.id === chatId) {\r\n        oldSessionId = chat.session_id; // Capture the old session_id\r\n        updatedChat = { ...chat, session_id: newSessionId };\r\n        return updatedChat;\r\n      }\r\n      return chat;\r\n    }));\r\n\r\n    // Update the messages mapping with the new session_id if we have an old one\r\n    if (oldSessionId && oldSessionId !== newSessionId) {\r\n      setChatMessages((prev: { [sessionId: string]: Message[] }) => {\r\n        const messages = prev[oldSessionId!] || [];\r\n        const updated = { ...prev };\r\n        delete updated[oldSessionId!]; // Remove old session_id key\r\n        updated[newSessionId] = messages; // Add with new session_id key\r\n        return updated;\r\n      });\r\n    }\r\n    \r\n    return updatedChat;\r\n  }, []);\r\n\r\n  const removeChat = useCallback((id: string): void => {\r\n    setChatHistory((prev: ChatHistoryItem[]) => {\r\n      const chatToRemove = prev.find(chat => chat.id === id);\r\n      if (chatToRemove) {\r\n        setChatMessages((prevMessages: { [sessionId: string]: Message[] }) => {\r\n          const updated = { ...prevMessages };\r\n          delete updated[chatToRemove.session_id];\r\n          return updated;\r\n        });\r\n        \r\n        // If we're removing the active chat, clear it\r\n        setActiveChatState((current: ChatHistoryItem | null) => current?.id === id ? null : current);\r\n        \r\n        return prev.filter(chat => chat.id !== id);\r\n      }\r\n      return prev;\r\n    });\r\n  }, []);\r\n\r\n  const deleteChat = useCallback(async (id: string): Promise<void> => {\r\n    if (!isAuthenticated || !user) return;\r\n\r\n    // Find the chat to get the session_id\r\n    const chatToDelete = chatHistory.find(chat => chat.id === id);\r\n    if (!chatToDelete) {\r\n      console.warn('Chat not found for deletion:', id);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Call the backend API to delete the chat\r\n      await apiDeleteChat(chatToDelete.session_id);\r\n      console.log('Chat deleted successfully from backend:', chatToDelete.session_id);\r\n      \r\n      // Remove from local state after successful backend deletion\r\n      removeChat(id);\r\n      \r\n    } catch (error: unknown) {\r\n      console.error('Error deleting chat from backend:', error);\r\n      \r\n      // Handle 401 errors by logging out the user\r\n      if (axios.isAxiosError(error) && error.response?.status === 401) {\r\n        console.log('Authentication token invalid while deleting chat, logging out...');\r\n        logout();\r\n        return;\r\n      }\r\n      \r\n      // Handle 404 errors (chat already deleted) by removing locally\r\n      if (axios.isAxiosError(error) && error.response?.status === 404) {\r\n        console.log('Chat not found on backend (404) - removing locally');\r\n        removeChat(id);\r\n        return;\r\n      }\r\n      \r\n      // For other errors, don't remove locally and re-throw\r\n      throw error;\r\n    }\r\n  }, [isAuthenticated, user, chatHistory, apiDeleteChat, removeChat, logout]);\r\n\r\n  const renameChat = useCallback((id: string, newTitle: string): void => {\r\n    setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => \r\n      chat.id === id \r\n        ? { ...chat, title: newTitle, last_updated: new Date() }\r\n        : chat\r\n    ));\r\n  }, []);\r\n\r\n  // Generate a chat title from the first user message\r\n  const generateChatTitle = useCallback((firstMessage: string): string => {\r\n    // Take first 50 characters and clean up\r\n    let title = firstMessage.trim().substring(0, 50);\r\n    \r\n    // Remove common question words and clean up\r\n    title = title.replace(/^(what|how|when|where|why|who|can|could|would|should|is|are|do|does|did)\\s+/i, '');\r\n    \r\n    // Capitalize first letter\r\n    title = title.charAt(0).toUpperCase() + title.slice(1);\r\n    \r\n    // Add ellipsis if truncated\r\n    if (firstMessage.length > 50) {\r\n      title += '...';\r\n    }\r\n    \r\n    return title || 'New Chat';\r\n  }, []);\r\n\r\n  const addMessageToChat = useCallback((sessionId: string, message: Message): void => {\r\n    const messageWithTimestamp = {\r\n      ...message,\r\n      timestamp: message.timestamp || new Date(),\r\n    };\r\n\r\n    setChatMessages((prev: { [sessionId: string]: Message[] }) => ({\r\n      ...prev,\r\n      [sessionId]: [...(prev[sessionId] || []), messageWithTimestamp],\r\n    }));\r\n\r\n    // Update the last_updated time and message count for the chat\r\n    setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => \r\n      chat.session_id === sessionId \r\n        ? { \r\n            ...chat, \r\n            last_updated: new Date(),\r\n            message_count: chat.message_count + 1\r\n          }\r\n        : chat\r\n    ));\r\n\r\n    // Auto-generate title from first user message (for new chats)\r\n    if (message.role === 'user') {\r\n      setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => {\r\n        if (chat.session_id === sessionId && (chat.title === 'New Chat' || chat.title === 'Untitled Chat')) {\r\n          const title = generateChatTitle(message.content);\r\n          return { ...chat, title };\r\n        }\r\n        return chat;\r\n      }));\r\n    }\r\n  }, [generateChatTitle]);\r\n\r\n  // Load chats when user authentication state changes\r\n  useEffect(() => {\r\n    console.log('ChatHistoryContext useEffect triggered:', {\r\n      authLoading,\r\n      isAuthenticated,\r\n      hasAccessToken: !!user?.access_token,\r\n      userId: user?.user_id,\r\n      hasLoadedChats: hasLoadedChats.current\r\n    });\r\n\r\n    // Skip if still loading auth or not authenticated\r\n    if (authLoading || !isAuthenticated || !user?.access_token) {\r\n      console.log('Skipping chat load - auth loading or not authenticated');\r\n      if (!authLoading && !isAuthenticated) {\r\n        // Only clear data when definitely not authenticated (not during loading)\r\n        setChatHistory([]);\r\n        setChatMessages({});\r\n        setActiveChatState(null);\r\n        hasLoadedChats.current = false; // Reset the flag when user logs out\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Prevent loading chats multiple times for the same authenticated session\r\n    if (hasLoadedChats.current) {\r\n      console.log('Chats already loaded for this session, skipping...');\r\n      return;\r\n    }\r\n\r\n    // Load chats only once after authentication\r\n    console.log('Loading chats for authenticated user...');\r\n    hasLoadedChats.current = true; // Set flag before starting the load\r\n    setIsLoadingChats(true);\r\n    listUserChats()\r\n      .then(backendChats => {\r\n        console.log('Successfully loaded chats from backend:', backendChats.length);\r\n        const convertedChats = backendChats.map(convertBackendChat);\r\n        convertedChats.sort((a, b) => b.last_updated.getTime() - a.last_updated.getTime());\r\n        setChatHistory(convertedChats);\r\n      })\r\n      .catch((error: unknown) => {\r\n        console.error('Error loading chats from backend:', error);\r\n        hasLoadedChats.current = false; // Reset flag on error so we can retry\r\n        if (axios.isAxiosError(error) && error.response?.status === 401) {\r\n          console.log('401 error while loading chats, logging out...');\r\n          logout();\r\n          return;\r\n        }\r\n        setChatHistory([]);\r\n      })\r\n      .finally(() => {\r\n        setIsLoadingChats(false);\r\n      });\r\n\r\n  }, [authLoading, isAuthenticated, user?.access_token, user?.user_id]); // Removed function dependencies to prevent infinite loops\r\n\r\n  // 🏢 ENTERPRISE FUNCTION: Refresh chat list from backend\r\n  const refreshChatList = useCallback(async (): Promise<void> => {\r\n    if (!isAuthenticated || !user?.access_token) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log('Refreshing chat list from backend...');\r\n      const backendChats = await listUserChats();\r\n      const convertedChats = backendChats.map(convertBackendChat);\r\n      convertedChats.sort((a, b) => b.last_updated.getTime() - a.last_updated.getTime());\r\n      setChatHistory(convertedChats);\r\n      console.log('Chat list refreshed successfully');\r\n    } catch (error: unknown) {\r\n      console.error('Error refreshing chat list:', error);\r\n      if (axios.isAxiosError(error) && error.response?.status === 401) {\r\n        logout();\r\n      }\r\n    }\r\n  }, [isAuthenticated, user?.access_token]); // Removed function dependencies\r\n\r\n  // Load specific chat by ID - useful for direct navigation to /chat/{chatId}\r\n  const loadChatById = useCallback(async (chatId: string): Promise<ChatHistoryItem | null> => {\r\n    console.log('Loading chat by ID:', chatId);\r\n\r\n    // First check if chat is already in our chat history\r\n    let chat = chatHistory.find(c => c.id === chatId);\r\n\r\n    if (chat) {\r\n      console.log('Chat found in existing history:', chat);\r\n      await setActiveChat(chat);\r\n      return chat;\r\n    }\r\n\r\n    // If not found and we haven't loaded chats yet, wait for them to load\r\n    if (isLoadingChats) {\r\n      console.log('Chats still loading, waiting...');\r\n      // Return null and let the component handle the loading state\r\n      return null;\r\n    }\r\n\r\n    // If chats are loaded but chat not found, the chat doesn't exist\r\n    console.log('Chat not found in loaded chat history - chat may not exist:', chatId);\r\n    return null;\r\n  }, [chatHistory, isLoadingChats, setActiveChat]);\r\n\r\n  return (\r\n    <ChatHistoryContext.Provider value={{\r\n      chatHistory,\r\n      activeChat,\r\n      chatMessages,\r\n      isLoadingChats,\r\n      isLoadingHistory,\r\n      pendingFirstMessage,\r\n      setPendingFirstMessage,\r\n      addChat,\r\n      updateChatSessionId,\r\n      deleteChat,\r\n      renameChat,\r\n      setActiveChat,\r\n      loadChatById,\r\n      addMessageToChat,\r\n      loadChatHistory,\r\n      refreshChatList,\r\n      generateSessionId,\r\n    }}>\r\n      {children}\r\n    </ChatHistoryContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useChatHistory = () => {\r\n  const ctx = useContext(ChatHistoryContext);\r\n  if (!ctx) throw new Error(\"useChatHistory must be used within a ChatHistoryProvider\");\r\n  return ctx;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AA2BA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsC;AAEtE,MAAM,sBAAyD,CAAC,EAAE,QAAQ,EAAE;IACjF,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,UAAO,AAAD;IACxE,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACpE,MAAM,CAAC,YAAY,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC,CAAC;IACtF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI;IAChC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,uDAAuD;IAC7F,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,wCAAwC;IAEpF,4EAA4E;IAC5E,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,MAAM,WAAW;QACjB,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,UAAU,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI;QACpD;QACA,OAAO;IACT,GAAG,EAAE;IAEL,2DAA2D;IAC3D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,cAAc,IAAI,KAAK,YAAY,SAAS;QAClD,OAAO;YACL,IAAI,CAAC,KAAK,EAAE,YAAY,UAAU,EAAE;YACpC,YAAY,YAAY,UAAU;YAClC,OAAO,YAAY,KAAK,IAAI;YAC5B,YAAY;YACZ,cAAc;YACd,eAAe,YAAY,aAAa,IAAI;QAC9C;IACF,GAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,cAAc,UAAU,eAAe,IAAI;YACzE,SAAS,eAAe,OAAO;YAC/B,WAAW,IAAI;QACjB;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAE/B,gDAAgD;QAChD,IAAI,aAAa,OAAO,CAAC,GAAG,CAAC,YAAY;YACvC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU,+BAA+B,CAAC;YAC1E;QACF;QAEA,oBAAoB;QACpB,aAAa,OAAO,CAAC,GAAG,CAAC,YAAY,kBAAkB;QAEvD,IAAI;YACF,MAAM,kBAAkB,MAAM,eAAe;YAC7C,MAAM,oBAAoB,gBAAgB,GAAG,CAAC;YAE9C,gBAAgB,CAAC,OAA6C,CAAC;oBAC7D,GAAG,IAAI;oBACP,CAAC,UAAU,EAAE;gBACf,CAAC;YACD,wEAAwE;YACxE,eAAe,CAAC,kBACd,gBAAgB,GAAG,CAAC,CAAC,WACnB,SAAS,UAAU,KAAK,YACpB;wBAAE,GAAG,QAAQ;wBAAE,eAAe,kBAAkB,MAAM;wBAAE,cAAc,IAAI;oBAAO,IACjF;QAIV,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,4CAA4C;YAE1D,4CAA4C;YAC5C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAC/D,QAAQ,GAAG,CAAC;gBACZ;gBACA;YACF;YAEA,wDAAwD;YACxD,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAC/D,QAAQ,GAAG,CAAC;gBACZ,gBAAgB,CAAC,OAA6C,CAAC;wBAC7D,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE,EAAE;oBACjB,CAAC;gBACD,0EAA0E;gBAC1E,eAAe,CAAC,kBACd,gBAAgB,GAAG,CAAC,CAAC,WACnB,SAAS,UAAU,KAAK,YACpB;4BAAE,GAAG,QAAQ;4BAAE,eAAe;4BAAG,cAAc,IAAI;wBAAO,IAC1D;YAGV,OAAO;gBACL,4DAA4D;gBAC5D,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,gBAAgB,CAAC,OAA6C,CAAC;wBAC7D,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE,EAAE;oBACjB,CAAC;gBACD,mCAAmC;gBACnC,eAAe,CAAC,kBACd,gBAAgB,GAAG,CAAC,CAAC,WACnB,SAAS,UAAU,KAAK,YACpB;4BAAE,GAAG,QAAQ;4BAAE,eAAe;4BAAG,cAAc,IAAI;wBAAO,IAC1D;YAGV;QACF,SAAU;YACR,oBAAoB;YACpB,aAAa,OAAO,CAAC,MAAM,CAAC,YAAY,oBAAoB;QAC9D;IACF,GAAG;QAAC;QAAiB;QAAM;QAAQ;QAAgB;KAAsB;IAEzE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,8CAA8C;QAC9C,IAAI,qBAAqB,OAAO,EAAE;YAChC,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,+CAA+C;QAC/C,IAAI,SAAS,MAAM;YACjB,mBAAmB;YACnB;QACF;QAEA,0EAA0E;QAC1E,IAAI,cAAc,WAAW,EAAE,KAAK,KAAK,EAAE,EAAE;YAC3C,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,qBAAqB,OAAO,GAAG;QAE/B,IAAI;YACF,+BAA+B;YAC/B,mBAAmB;YAEnB,kDAAkD;YAClD,MAAM,mBAAmB,YAAY,CAAC,KAAK,UAAU,CAAC,IAAI,EAAE;YAC5D,MAAM,iBAAiB,iBAAiB,MAAM,GAAG;YAEjD,sDAAsD;YACtD,iDAAiD;YACjD,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,KAAK,UAAU,EAAE;gBAC3D,QAAQ,GAAG,CAAC,qCAAqC,KAAK,UAAU;gBAChE,MAAM,gBAAgB,KAAK,UAAU;YACvC,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF,SAAU;YACR,qBAAqB,OAAO,GAAG;QACjC;IACF,GAAG;QAAC;QAAiB;QAAc;QAAY;KAAiB;IAEhE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,+CAA+C;QAC/C,IAAI,gBAAgB;YAClB,QAAQ,GAAG,CAAC;YACZ,gDAAgD;YAChD,MAAM,aAAa,WAAW,CAAC,EAAE;YACjC,IAAI,YAAY;gBACd,OAAO;YACT;YACA,MAAM,IAAI,MAAM;QAClB;QAEA,kBAAkB;QAElB,IAAI;YACF,MAAM,YAAY;YAClB,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAC9E,MAAM,MAAM,IAAI;YAEhB,MAAM,UAA2B;gBAC/B,IAAI;gBACJ,YAAY;gBACZ,OAAO,SAAS;gBAChB,YAAY;gBACZ,cAAc;gBACd,eAAe;YACjB;YAEA,mDAAmD;YACnD,eAAe,CAAA,OAAQ;oBAAC;uBAAY;iBAAK;YAEzC,OAAO;QACT,SAAU;YACR,iFAAiF;YACjF,WAAW;gBACT,kBAAkB;YACpB,GAAG;QACL;IACF,GAAG;QAAC;QAAmB;QAAgB;KAAY;IAEnD,+EAA+E;IAC/E,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB;QACvD,IAAI,cAAsC;QAC1C,IAAI,eAAmC;QAEvC,oDAAoD;QACpD,eAAe,CAAC,OAA4B,KAAK,GAAG,CAAC,CAAA;gBACnD,IAAI,KAAK,EAAE,KAAK,QAAQ;oBACtB,eAAe,KAAK,UAAU,EAAE,6BAA6B;oBAC7D,cAAc;wBAAE,GAAG,IAAI;wBAAE,YAAY;oBAAa;oBAClD,OAAO;gBACT;gBACA,OAAO;YACT;QAEA,4EAA4E;QAC5E,IAAI,gBAAgB,iBAAiB,cAAc;YACjD,gBAAgB,CAAC;gBACf,MAAM,WAAW,IAAI,CAAC,aAAc,IAAI,EAAE;gBAC1C,MAAM,UAAU;oBAAE,GAAG,IAAI;gBAAC;gBAC1B,OAAO,OAAO,CAAC,aAAc,EAAE,4BAA4B;gBAC3D,OAAO,CAAC,aAAa,GAAG,UAAU,8BAA8B;gBAChE,OAAO;YACT;QACF;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,eAAe,CAAC;YACd,MAAM,eAAe,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACnD,IAAI,cAAc;gBAChB,gBAAgB,CAAC;oBACf,MAAM,UAAU;wBAAE,GAAG,YAAY;oBAAC;oBAClC,OAAO,OAAO,CAAC,aAAa,UAAU,CAAC;oBACvC,OAAO;gBACT;gBAEA,8CAA8C;gBAC9C,mBAAmB,CAAC,UAAoC,SAAS,OAAO,KAAK,OAAO;gBAEpF,OAAO,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACzC;YACA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAE/B,sCAAsC;QACtC,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1D,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC,gCAAgC;YAC7C;QACF;QAEA,IAAI;YACF,0CAA0C;YAC1C,MAAM,cAAc,aAAa,UAAU;YAC3C,QAAQ,GAAG,CAAC,2CAA2C,aAAa,UAAU;YAE9E,4DAA4D;YAC5D,WAAW;QAEb,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,qCAAqC;YAEnD,4CAA4C;YAC5C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAC/D,QAAQ,GAAG,CAAC;gBACZ;gBACA;YACF;YAEA,+DAA+D;YAC/D,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAC/D,QAAQ,GAAG,CAAC;gBACZ,WAAW;gBACX;YACF;YAEA,sDAAsD;YACtD,MAAM;QACR;IACF,GAAG;QAAC;QAAiB;QAAM;QAAa;QAAe;QAAY;KAAO;IAE1E,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAY;QAC1C,eAAe,CAAC,OAA4B,KAAK,GAAG,CAAC,CAAA,OACnD,KAAK,EAAE,KAAK,KACR;oBAAE,GAAG,IAAI;oBAAE,OAAO;oBAAU,cAAc,IAAI;gBAAO,IACrD;IAER,GAAG,EAAE;IAEL,oDAAoD;IACpD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,wCAAwC;QACxC,IAAI,QAAQ,aAAa,IAAI,GAAG,SAAS,CAAC,GAAG;QAE7C,4CAA4C;QAC5C,QAAQ,MAAM,OAAO,CAAC,gFAAgF;QAEtG,0BAA0B;QAC1B,QAAQ,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;QAEpD,4BAA4B;QAC5B,IAAI,aAAa,MAAM,GAAG,IAAI;YAC5B,SAAS;QACX;QAEA,OAAO,SAAS;IAClB,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACvD,MAAM,uBAAuB;YAC3B,GAAG,OAAO;YACV,WAAW,QAAQ,SAAS,IAAI,IAAI;QACtC;QAEA,gBAAgB,CAAC,OAA6C,CAAC;gBAC7D,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;uBAAK,IAAI,CAAC,UAAU,IAAI,EAAE;oBAAG;iBAAqB;YACjE,CAAC;QAED,8DAA8D;QAC9D,eAAe,CAAC,OAA4B,KAAK,GAAG,CAAC,CAAA,OACnD,KAAK,UAAU,KAAK,YAChB;oBACE,GAAG,IAAI;oBACP,cAAc,IAAI;oBAClB,eAAe,KAAK,aAAa,GAAG;gBACtC,IACA;QAGN,8DAA8D;QAC9D,IAAI,QAAQ,IAAI,KAAK,QAAQ;YAC3B,eAAe,CAAC,OAA4B,KAAK,GAAG,CAAC,CAAA;oBACnD,IAAI,KAAK,UAAU,KAAK,aAAa,CAAC,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK,KAAK,eAAe,GAAG;wBAClG,MAAM,QAAQ,kBAAkB,QAAQ,OAAO;wBAC/C,OAAO;4BAAE,GAAG,IAAI;4BAAE;wBAAM;oBAC1B;oBACA,OAAO;gBACT;QACF;IACF,GAAG;QAAC;KAAkB;IAEtB,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,2CAA2C;YACrD;YACA;YACA,gBAAgB,CAAC,CAAC,MAAM;YACxB,QAAQ,MAAM;YACd,gBAAgB,eAAe,OAAO;QACxC;QAEA,kDAAkD;QAClD,IAAI,eAAe,CAAC,mBAAmB,CAAC,MAAM,cAAc;YAC1D,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,yEAAyE;gBACzE,eAAe,EAAE;gBACjB,gBAAgB,CAAC;gBACjB,mBAAmB;gBACnB,eAAe,OAAO,GAAG,OAAO,oCAAoC;YACtE;YACA;QACF;QAEA,0EAA0E;QAC1E,IAAI,eAAe,OAAO,EAAE;YAC1B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,4CAA4C;QAC5C,QAAQ,GAAG,CAAC;QACZ,eAAe,OAAO,GAAG,MAAM,oCAAoC;QACnE,kBAAkB;QAClB,gBACG,IAAI,CAAC,CAAA;YACJ,QAAQ,GAAG,CAAC,2CAA2C,aAAa,MAAM;YAC1E,MAAM,iBAAiB,aAAa,GAAG,CAAC;YACxC,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,CAAC,OAAO,KAAK,EAAE,YAAY,CAAC,OAAO;YAC/E,eAAe;QACjB,GACC,KAAK,CAAC,CAAC;YACN,QAAQ,KAAK,CAAC,qCAAqC;YACnD,eAAe,OAAO,GAAG,OAAO,sCAAsC;YACtE,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAC/D,QAAQ,GAAG,CAAC;gBACZ;gBACA;YACF;YACA,eAAe,EAAE;QACnB,GACC,OAAO,CAAC;YACP,kBAAkB;QACpB;IAEJ,GAAG;QAAC;QAAa;QAAiB,MAAM;QAAc,MAAM;KAAQ,GAAG,0DAA0D;IAEjI,yDAAyD;IACzD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,mBAAmB,CAAC,MAAM,cAAc;YAC3C;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,MAAM;YAC3B,MAAM,iBAAiB,aAAa,GAAG,CAAC;YACxC,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,CAAC,OAAO,KAAK,EAAE,YAAY,CAAC,OAAO;YAC/E,eAAe;YACf,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAC/D;YACF;QACF;IACF,GAAG;QAAC;QAAiB,MAAM;KAAa,GAAG,gCAAgC;IAE3E,4EAA4E;IAC5E,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,qDAAqD;QACrD,IAAI,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE1C,IAAI,MAAM;YACR,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,MAAM,cAAc;YACpB,OAAO;QACT;QAEA,sEAAsE;QACtE,IAAI,gBAAgB;YAClB,QAAQ,GAAG,CAAC;YACZ,6DAA6D;YAC7D,OAAO;QACT;QAEA,iEAAiE;QACjE,QAAQ,GAAG,CAAC,+DAA+D;QAC3E,OAAO;IACT,GAAG;QAAC;QAAa;QAAgB;KAAc;IAE/C,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;YAClC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,MAAM,iBAAiB;IAC5B,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACvB,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM;IAC1B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider, useTheme as useNextTheme } from \"next-themes\"\n\ninterface ThemeProviderProps {\n  children: React.ReactNode\n  [key: string]: any\n}\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n\n// Custom hook that handles client-side rendering\nexport const useTheme = () => {\n  const [mounted, setMounted] = React.useState(false)\n  const { theme, setTheme, resolvedTheme } = useNextTheme()\n  \n  // After mounting, we have access to the theme\n  React.useEffect(() => setMounted(true), [])\n  \n  return {\n    theme: mounted ? theme : undefined,\n    setTheme,\n    resolvedTheme: mounted ? resolvedTheme : undefined,\n    mounted\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;AAGO,MAAM,WAAW;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAY,AAAD;IAEtD,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE,IAAM,WAAW,OAAO,EAAE;IAE1C,OAAO;QACL,OAAO,UAAU,QAAQ;QACzB;QACA,eAAe,UAAU,gBAAgB;QACzC;IACF;AACF", "debugId": null}}]}