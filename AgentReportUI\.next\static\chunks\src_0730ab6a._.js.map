{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ComponentType<{ className?: string }>;\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />);\nBreadcrumb.displayName = \"Breadcrumb\";\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n));\nBreadcrumbList.displayName = \"BreadcrumbList\";\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n));\nBreadcrumbItem.displayName = \"BreadcrumbItem\";\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean;\n  }\n>(({ asChild, className, ...props }, ref) => {\n  if (asChild) {\n    return <React.Fragment {...props} />;\n  }\n\n  return (\n    <a\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  );\n});\nBreadcrumbLink.displayName = \"BreadcrumbLink\";\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n));\nBreadcrumbPage.displayName = \"BreadcrumbPage\";\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:size-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n);\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\";\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <svg\n      width=\"15\"\n      height=\"15\"\n      viewBox=\"0 0 15 15\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z\"\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n    <span className=\"sr-only\">More</span>\n  </span>\n);\nBreadcrumbEllipsis.displayName = \"BreadcrumbEllipsis\";\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAKhC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,6LAAC;QAAI,KAAK;QAAK,cAAW;QAAc,GAAG,KAAK;;;;;;;AACzE,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,IAAI,SAAS;QACX,qBAAO,6LAAC,6JAAA,CAAA,WAAc;YAAE,GAAG,KAAK;;;;;;IAClC;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,iBAC3B,6LAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;OAXxB;AAcN,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,6LAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;0BAEN,cAAA,6LAAC;oBACC,GAAE;oBACF,MAAK;oBACL,UAAS;oBACT,UAAS;;;;;;;;;;;0BAGb,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;OAxBxB;AA2BN,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { Menu, LayoutDashboard, ChevronRight, Plus, Brain, Download, MessageSquare } from 'lucide-react';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { useTheme } from \"@/providers/theme-provider\";\r\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const { theme, setTheme } = useTheme();\r\n  const { pageInfo, actions } = usePageHeader();\r\n  const pathname = usePathname();\r\n  \r\n  // Check if we have breadcrumbs to show\r\n  const hasBreadcrumbs = pageInfo.breadcrumbs && pageInfo.breadcrumbs.length > 1;\r\n  \r\n  // Get chart-related state from context actions\r\n  const { \r\n    onCreateChart, \r\n    chartCount = 0, \r\n    maxCharts = 12, \r\n    onCreateAnalysis, \r\n    isCreatingAnalysis = false,\r\n    onCreateDashboard,\r\n    onExport,\r\n    onToggleChat,\r\n    isChatOpen = false\r\n  } = actions;\r\n  const canCreateChart = chartCount < maxCharts;\r\n  \r\n  // Show Create Chart Button on dashboard pages when the callback is provided\r\n  const isDashboardPage = pathname === '/dashboard';\r\n  const isAIWorkflowsPage = pathname === '/ai-workflows';\r\n  const showCreateChartButton = isDashboardPage && onCreateChart;\r\n  const showCreateDashboardButton = isDashboardPage && onCreateDashboard;\r\n  const showCreateAnalysisButton = isAIWorkflowsPage && onCreateAnalysis;\r\n  const showProjectActions = isAIWorkflowsPage && (onExport || onToggleChat);\r\n\r\n  // Get the appropriate icon for breadcrumbs\r\n  const getBreadcrumbIcon = () => {\r\n    if (pathname === '/dashboard') return LayoutDashboard;\r\n    if (pathname === '/ai-workflows') return Brain;\r\n    return pageInfo.icon || LayoutDashboard;\r\n  };\r\n\r\n  const BreadcrumbIcon = getBreadcrumbIcon();\r\n\r\n  return (\r\n    <header className=\"flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12\">\r\n      <div className=\"flex items-center gap-3 text-sidebar-text-primary\">\r\n        {isAuthenticated && (\r\n          <div className=\"lg:hidden\">\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\" className=\"h-7 w-7\">\r\n                  <Menu className=\"h-4 w-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n            </Sheet>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Dynamic Breadcrumb Navigation or Page Title */}\r\n        {hasBreadcrumbs ? (\r\n          <Breadcrumb>\r\n            <BreadcrumbList>\r\n              {pageInfo.breadcrumbs!.map((breadcrumb, index) => (\r\n                <React.Fragment key={breadcrumb.label}>\r\n                  <BreadcrumbItem>\r\n                    {index === 0 ? (\r\n                      // First breadcrumb item (with icon)\r\n                      breadcrumb.onClick || breadcrumb.href ? (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </BreadcrumbLink>\r\n                      ) : (\r\n                        <div className=\"flex items-center space-x-1.5 text-sm font-medium\">\r\n                          <BreadcrumbIcon className=\"h-4 w-4\" />\r\n                          <span>{breadcrumb.label}</span>\r\n                        </div>\r\n                      )\r\n                    ) : (\r\n                      // Subsequent breadcrumb items\r\n                      index === pageInfo.breadcrumbs!.length - 1 ? (\r\n                        <BreadcrumbPage className=\"font-medium text-sm\">\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbPage>\r\n                      ) : (\r\n                        <BreadcrumbLink \r\n                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}\r\n                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}\r\n                          className=\"cursor-pointer hover:text-primary text-sm font-medium\"\r\n                        >\r\n                          {breadcrumb.label}\r\n                        </BreadcrumbLink>\r\n                      )\r\n                    )}\r\n                  </BreadcrumbItem>\r\n                  \r\n                  {/* Separator */}\r\n                  {index < pageInfo.breadcrumbs!.length - 1 && (\r\n                    <BreadcrumbSeparator>\r\n                      <ChevronRight className=\"h-3 w-3\" />\r\n                    </BreadcrumbSeparator>\r\n                  )}\r\n                </React.Fragment>\r\n              ))}\r\n            </BreadcrumbList>\r\n          </Breadcrumb>\r\n        ) : (\r\n          <h1 className=\"text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary\">\r\n            {pageInfo.title}\r\n          </h1>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side actions */}\r\n      <div className=\"flex items-center gap-2\">\r\n        {showCreateChartButton && (\r\n          <Button\r\n            onClick={onCreateChart}\r\n            disabled={!canCreateChart}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (canCreateChart) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Chart\r\n          </Button>\r\n        )}\r\n        {showCreateDashboardButton && (\r\n          <Button\r\n            onClick={onCreateDashboard}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            Dashboard\r\n          </Button>\r\n        )}\r\n        {showCreateAnalysisButton && (\r\n          <Button\r\n            onClick={onCreateAnalysis}\r\n            disabled={isCreatingAnalysis}\r\n            className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n            style={{\r\n              backgroundColor: 'var(--surface-selected)',\r\n              color: 'var(--sidebar-text-primary)'\r\n            }}\r\n            title={isCreatingAnalysis ? \"Analysis is being created\" : undefined}\r\n            onMouseEnter={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (!isCreatingAnalysis) {\r\n                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n              }\r\n            }}\r\n          >\r\n            <Plus className=\"h-3 w-3 mr-1.5\" />\r\n            {isCreatingAnalysis ? 'Creating...' : 'Analysis'}\r\n          </Button>\r\n        )}\r\n        {showProjectActions && (\r\n          <>\r\n            {onExport && (\r\n              <Button\r\n                onClick={onExport}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <Download className=\"h-3 w-3 mr-1.5\" />\r\n                Export\r\n              </Button>\r\n            )}\r\n            {onToggleChat && (\r\n              <Button\r\n                onClick={onToggleChat}\r\n                className=\"border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs\"\r\n                style={{\r\n                  backgroundColor: isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = isChatOpen ? 'var(--interactive-bg-secondary-hover)' : 'var(--surface-selected)';\r\n                }}\r\n              >\r\n                <MessageSquare className=\"h-3 w-3 mr-1.5\" />\r\n                Chat\r\n              </Button>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AAXA;;;;;;;;;;AAoBA,MAAM,SAAS;;IACb,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAC1C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,uCAAuC;IACvC,MAAM,iBAAiB,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG;IAE7E,+CAA+C;IAC/C,MAAM,EACJ,aAAa,EACb,aAAa,CAAC,EACd,YAAY,EAAE,EACd,gBAAgB,EAChB,qBAAqB,KAAK,EAC1B,iBAAiB,EACjB,QAAQ,EACR,YAAY,EACZ,aAAa,KAAK,EACnB,GAAG;IACJ,MAAM,iBAAiB,aAAa;IAEpC,4EAA4E;IAC5E,MAAM,kBAAkB,aAAa;IACrC,MAAM,oBAAoB,aAAa;IACvC,MAAM,wBAAwB,mBAAmB;IACjD,MAAM,4BAA4B,mBAAmB;IACrD,MAAM,2BAA2B,qBAAqB;IACtD,MAAM,qBAAqB,qBAAqB,CAAC,YAAY,YAAY;IAEzE,2CAA2C;IAC3C,MAAM,oBAAoB;QACxB,IAAI,aAAa,cAAc,OAAO,+NAAA,CAAA,kBAAe;QACrD,IAAI,aAAa,iBAAiB,OAAO,uMAAA,CAAA,QAAK;QAC9C,OAAO,SAAS,IAAI,IAAI,+NAAA,CAAA,kBAAe;IACzC;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;oBACZ,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;sCACJ,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAO,WAAU;8CAC9C,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQzB,+BACC,6LAAC,yIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;sCACZ,SAAS,WAAW,CAAE,GAAG,CAAC,CAAC,YAAY,sBACtC,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;sDACb,6LAAC,yIAAA,CAAA,iBAAc;sDACZ,UAAU,IACT,oCAAoC;4CACpC,WAAW,OAAO,IAAI,WAAW,IAAI,iBACnC,6LAAC,yIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;;kEAEV,6LAAC;wDAAe,WAAU;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;qEAGzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAe,WAAU;;;;;;kEAC1B,6LAAC;kEAAM,WAAW,KAAK;;;;;;;;;;;uDAI3B,8BAA8B;4CAC9B,UAAU,SAAS,WAAW,CAAE,MAAM,GAAG,kBACvC,6LAAC,yIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,WAAW,KAAK;;;;;qEAGnB,6LAAC,yIAAA,CAAA,iBAAc;gDACZ,GAAI,WAAW,IAAI,GAAG;oDAAE,MAAM,WAAW,IAAI;gDAAC,IAAI,CAAC,CAAC;gDACpD,GAAI,WAAW,OAAO,GAAG;oDAAE,SAAS,WAAW,OAAO;gDAAC,IAAI,CAAC,CAAC;gDAC9D,WAAU;0DAET,WAAW,KAAK;;;;;;;;;;;wCAOxB,QAAQ,SAAS,WAAW,CAAE,MAAM,GAAG,mBACtC,6LAAC,yIAAA,CAAA,sBAAmB;sDAClB,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;mCAxCT,WAAW,KAAK;;;;;;;;;;;;;;6CAgD3C,6LAAC;wBAAG,WAAU;kCACX,SAAS,KAAK;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAU;;oBACZ,uCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,CAAC,iBAAiB,CAAC,WAAW,EAAE,UAAU,eAAe,CAAC,GAAG;wBACpE,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,gBAAgB;gCAClB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,2CACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC1C;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAmB;;;;;;;oBAItC,0CACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,OAAO;wBACT;wBACA,OAAO,qBAAqB,8BAA8B;wBAC1D,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,CAAC,oBAAoB;gCACvB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4BAC1C;wBACF;;0CAEA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,qBAAqB,gBAAgB;;;;;;;oBAGzC,oCACC;;4BACG,0BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;;kDAEA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;4BAI1C,8BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO;oCACL,iBAAiB,aAAa,0CAA0C;oCACxE,OAAO;gCACT;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,aAAa,0CAA0C;gCACjG;;kDAEA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAmB;;;;;;;;;;;;;;;;;;;;;AAS5D;GAtOM;;QACwB,mIAAA,CAAA,UAAO;QACP,yIAAA,CAAA,WAAQ;QACN,yIAAA,CAAA,gBAAa;QAC1B,qIAAA,CAAA,cAAW;;;KAJxB;uCAwOS", "debugId": null}}, {"offset": {"line": 1090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground text-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+nBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Link from 'next/link';\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { MoreVertical, MessageCircle, Database, Plus, BarChart3, MessageCirclePlus, LayoutDashboard, Brain, User, Settings, LogOut } from 'lucide-react';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface SidebarProps {\r\n  onNewChat: () => void;\r\n  onToggleCollapse?: (collapsed: boolean) => void;\r\n  isCreatingNewChat?: boolean;\r\n}\r\n\r\nconst Sidebar: React.FC<SidebarProps> = ({ onNewChat, onToggleCollapse, isCreatingNewChat = false }) => {\r\n  const { \r\n    chatHistory, \r\n    isLoadingChats, \r\n    deleteChat,\r\n    renameChat, \r\n  } = useChatHistory();\r\n  const { logout, user } = useAuth();\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);\r\n  const [renameId, setRenameId] = useState<string | null>(null);\r\n  const [renameValue, setRenameValue] = useState<string>(\"\");\r\n  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);\r\n\r\n  console.log('Sidebar component rendered. Current pathname:', pathname);\r\n\r\n  // Extract chatId from pathname (e.g., /chat/123)\r\n  const currentChatId = pathname?.split('/').includes('chat') ? pathname.split('/').pop() : null;\r\n  console.log('Current chatId extracted from pathname:', currentChatId);\r\n\r\n  const handleRename = (id: string, currentTitle: string) => {\r\n    console.log('Renaming chat with id:', id, 'and current title:', currentTitle);\r\n    setRenameId(id);\r\n    setRenameValue(currentTitle);\r\n    setMenuOpenId(null);\r\n  };\r\n\r\n  const handleRenameSubmit = (id: string) => {\r\n    console.log('Submitting rename for chat with id:', id, 'and new title:', renameValue);\r\n    if (renameValue.trim()) {\r\n      renameChat(id, renameValue.trim());\r\n    }\r\n    setRenameId(null);\r\n    setRenameValue(\"\");\r\n  };\r\n\r\n  const handleDelete = async (chatId: string) => {\r\n    try {\r\n      await deleteChat(chatId);\r\n      setMenuOpenId(null);\r\n\r\n      // 🚚 After successful deletion, handle navigation if the deleted chat was active\r\n      if (currentChatId === chatId) {\r\n        // Get the updated chat list (the state will have been updated by deleteChat)\r\n        const remainingChats = chatHistory.filter(chat => chat.id !== chatId);\r\n\r\n        if (remainingChats.length > 0) {\r\n          // Navigate to the most recently updated chat (first in list)\r\n          router.push(`/chat/${remainingChats[0].id}`);\r\n        } else {\r\n          // No chats left – start a fresh chat\r\n          onNewChat();\r\n          // Fallback navigate to generic chat route to trigger new-chat UI\r\n          router.push('/chat');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to delete chat:', error);\r\n      // You could add a toast notification here if you have one\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp: Date) => {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - timestamp.getTime();\r\n    const diffHours = diffMs / (1000 * 60 * 60);\r\n    const diffDays = diffMs / (1000 * 60 * 60 * 24);\r\n\r\n    if (diffHours < 1) {\r\n      return 'Just now';\r\n    } else if (diffHours < 24) {\r\n      return `${Math.floor(diffHours)}h ago`;\r\n    } else if (diffDays < 7) {\r\n      return `${Math.floor(diffDays)}d ago`;\r\n    } else {\r\n      return timestamp.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const handleToggleCollapse = () => {\r\n    const newCollapsedState = !isCollapsed;\r\n    setIsCollapsed(newCollapsedState);\r\n    onToggleCollapse?.(newCollapsedState);\r\n  };\r\n\r\n  return (\r\n    <aside \r\n      className={`hidden lg:flex flex-col ${isCollapsed ? 'w-16' : 'w-sidebar'} h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto`}\r\n      style={{\r\n        backgroundColor: 'var(--sidebar-bg)',\r\n        borderRight: '1px solid var(--sidebar-border)',\r\n        scrollbarColor: 'var(--sidebar-surface-tertiary) transparent'\r\n      }}\r\n    >\r\n      {/* Toggle Button */}\r\n      <div className={`sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ${isCollapsed ? 'flex justify-center' : 'flex justify-end'}`}\r\n        style={{ backgroundColor: 'var(--sidebar-bg)' }}\r\n      >\r\n        <Button \r\n          variant=\"ghost\" \r\n          size=\"icon\"\r\n          className=\"w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu\"\r\n          style={{ \r\n            color: 'var(--sidebar-icon) !important',\r\n            backgroundColor: 'transparent !important'\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');\r\n            e.currentTarget.style.transform = 'scale(1.05)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');\r\n            e.currentTarget.style.transform = 'scale(1)';\r\n          }}\r\n          onClick={handleToggleCollapse}\r\n        >\r\n          <svg \r\n            className=\"h-5 w-5 transition-transform duration-300 ease-in-out\" \r\n            viewBox=\"0 0 20 20\" \r\n            fill=\"currentColor\" \r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            style={{\r\n              transform: isCollapsed ? 'scaleX(-1)' : 'scaleX(1)'\r\n            }}\r\n          >\r\n            <path d=\"M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z\"></path>\r\n          </svg>\r\n        </Button>\r\n      </div>\r\n      \r\n      {!isCollapsed && (\r\n        <div className=\"flex flex-col h-full px-3\">\r\n          {/* Navigation Links */}\r\n          <div className=\"space-y-1 mb-4\">\r\n            <Link href=\"/dashboard\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/dashboard' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/dashboard' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/dashboard' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/dashboard') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <LayoutDashboard className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Dashboard\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/reports\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/reports' ? '' : ''}`}\r\n                style={{\r\n                  color: pathname === '/reports' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/reports' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/reports') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <BarChart3 className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Reports\r\n              </Button>\r\n            </Link>\r\n            \r\n            <Link href=\"/datasources\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/datasources' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/datasources' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/datasources') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Database className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                Data Sources\r\n              </Button>\r\n            </Link>\r\n\r\n            <Link href=\"/ai-workflows\">\r\n              <Button \r\n                variant=\"ghost\" \r\n                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}\r\n                style={{\r\n                  color: pathname === '/ai-workflows' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',\r\n                  backgroundColor: pathname === '/ai-workflows' ? 'var(--surface-selected)' : 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (pathname !== '/ai-workflows') {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }\r\n                }}\r\n              >\r\n                <Brain className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n                AI Workflows\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n          \r\n          {/* New Chat Button */}\r\n          <div className=\"mb-4\">\r\n            <Button \r\n              variant=\"ghost\" \r\n              className=\"w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10\" \r\n              onClick={onNewChat}\r\n              disabled={isCreatingNewChat}\r\n              style={{\r\n                color: 'var(--sidebar-text-secondary)',\r\n                backgroundColor: 'transparent'\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (!isCreatingNewChat) {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }\r\n              }}\r\n            >\r\n              <Plus className=\"h-4 w-4\" style={{ color: 'var(--sidebar-icon)' }} />\r\n              {isCreatingNewChat ? 'Creating...' : 'New Chat'}\r\n            </Button>\r\n          </div>\r\n          \r\n          {/* Chat History Section */}\r\n          <div className=\"flex flex-col gap-1 overflow-y-auto flex-1 pb-4\">\r\n            <h3 \r\n              className=\"text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10\"\r\n              style={{ \r\n                color: 'var(--sidebar-text-tertiary)',\r\n                backgroundColor: 'var(--sidebar-bg)'\r\n              }}\r\n            >\r\n              Chat History\r\n            </h3>\r\n            \r\n            {isLoadingChats && (\r\n              <div className=\"flex items-center justify-center py-8\">\r\n                <div className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Loading chats...\r\n                </div>\r\n              </div>\r\n            )}\r\n            \r\n            {!isLoadingChats && chatHistory.map((chat) => {\r\n              const isActive = chat.id === currentChatId;\r\n              const isRenaming = renameId === chat.id;\r\n              return (\r\n                <div\r\n                  key={chat.id}\r\n                  className={`group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? '' : ''}`}\r\n                  style={{\r\n                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (!isActive) {\r\n                      e.currentTarget.style.backgroundColor = 'transparent';\r\n                    }\r\n                  }}\r\n                  onClick={() => router.push(`/chat/${chat.id}`)}\r\n                >\r\n                  {isRenaming ? (\r\n                    <form\r\n                      onSubmit={e => { e.preventDefault(); handleRenameSubmit(chat.id); }}\r\n                      className=\"flex items-center gap-2 w-full\"\r\n                    >\r\n                      <input\r\n                        autoFocus\r\n                        className=\"truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1\"\r\n                        style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        value={renameValue}\r\n                        onChange={e => setRenameValue(e.target.value)}\r\n                        onKeyDown={e => { if (e.key === 'Escape') setRenameId(null); }}\r\n                      />\r\n                      <Button \r\n                        type=\"submit\" \r\n                        size=\"sm\" \r\n                        variant=\"ghost\" \r\n                        className=\"text-blue-500 px-2 text-xs rounded border-0\"\r\n                      >\r\n                        Save\r\n                      </Button>\r\n                    </form>\r\n                  ) : (\r\n                    <>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <span \r\n                          className=\"truncate text-sm font-normal block\"\r\n                          style={{ color: 'var(--sidebar-text-primary)' }}\r\n                        >\r\n                          {chat.title}\r\n                        </span>\r\n                      </div>\r\n                      \r\n                      <div className=\"relative ml-2 flex-shrink-0\" onClick={e => e.stopPropagation()}>\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button\r\n                              type=\"button\"\r\n                              size=\"icon\"\r\n                              variant=\"ghost\"\r\n                              className=\"w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0\"\r\n                              style={{ color: 'var(--sidebar-icon)' }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                              aria-label=\"More actions\"\r\n                            >\r\n                              <MoreVertical className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent \r\n                            align=\"start\" \r\n                            side=\"bottom\" \r\n                            sideOffset={8} \r\n                            className=\"border-none shadow-xl rounded-xl p-2\"\r\n                            style={{\r\n                              backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                              color: 'var(--sidebar-text-primary)'\r\n                            }}\r\n                          >\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleRename(chat.id, chat.title)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: 'var(--sidebar-text-primary)',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Rename\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDelete(chat.id)}\r\n                              className=\"rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                              style={{ \r\n                                color: '#ff8583',\r\n                                backgroundColor: 'transparent'\r\n                              }}\r\n                              onMouseEnter={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                              }}\r\n                              onMouseLeave={(e) => {\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                              }}\r\n                            >\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              );\r\n            })}\r\n            \r\n            {!isLoadingChats && chatHistory.length === 0 && (\r\n              <div className=\"text-center py-8\">\r\n                <MessageCirclePlus \r\n                  className=\"h-12 w-12 mx-auto mb-3\" \r\n                  style={{ color: 'var(--sidebar-text-tertiary)' }}\r\n                />\r\n                <p className=\"text-xs\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  No chats yet\r\n                </p>\r\n                <p className=\"text-xs mt-1\" style={{ color: 'var(--sidebar-text-tertiary)' }}>\r\n                  Start a new conversation\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* Profile Section */}\r\n          <div className=\"mt-auto pt-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <div className=\"flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-all duration-200 hover:bg-opacity-80\" \r\n                     style={{ backgroundColor: 'transparent' }}\r\n                     onMouseEnter={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                     }}\r\n                     onMouseLeave={(e) => {\r\n                       e.currentTarget.style.backgroundColor = 'transparent';\r\n                     }}>\r\n                  <div\r\n                    className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600\"\r\n                    style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                  />\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <span className=\"text-sm font-medium block truncate\" style={{ color: 'var(--sidebar-text-primary)' }}>\r\n                      {'User'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent \r\n                align=\"start\" \r\n                side=\"top\" \r\n                sideOffset={8} \r\n                className=\"border-none shadow-xl rounded-xl p-2\"\r\n                style={{\r\n                  backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                  color: 'var(--sidebar-text-primary)'\r\n                }}\r\n              >\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <User className=\"w-4 h-4\" />\r\n                    Profile\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem asChild>\r\n                  <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                        style={{ \r\n                          color: 'var(--sidebar-text-primary)',\r\n                          backgroundColor: 'transparent'\r\n                        }}\r\n                        onMouseEnter={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                        }}\r\n                        onMouseLeave={(e) => {\r\n                          e.currentTarget.style.backgroundColor = 'transparent';\r\n                        }}>\r\n                    <Settings className=\"w-4 h-4\" />\r\n                    Settings\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem \r\n                  onClick={logout} \r\n                  className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                  style={{ \r\n                    color: '#ff8583',\r\n                    backgroundColor: 'transparent'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.currentTarget.style.backgroundColor = 'transparent';\r\n                  }}\r\n                >\r\n                  <LogOut className=\"w-4 h-4\" />\r\n                  Logout\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Profile Section for Collapsed State */}\r\n      {isCollapsed && (\r\n        <div className=\"mt-auto p-2 pb-4 border-t\" style={{ borderColor: 'var(--sidebar-border)' }}>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <div className=\"flex justify-center\">\r\n                <div\r\n                  className=\"bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 border border-gray-300 dark:border-gray-600 cursor-pointer transition-all duration-200 hover:scale-105\"\r\n                  style={{backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y\")'}}\r\n                />\r\n              </div>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent \r\n              align=\"start\" \r\n              side=\"right\" \r\n              sideOffset={8} \r\n              className=\"border-none shadow-xl rounded-xl p-2\"\r\n              style={{\r\n                backgroundColor: 'var(--sidebar-surface-secondary)',\r\n                color: 'var(--sidebar-text-primary)'\r\n              }}\r\n            >\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/profile\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <User className=\"w-4 h-4\" />\r\n                  Profile\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem asChild>\r\n                <Link href=\"/settings\" className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                      style={{ \r\n                        color: 'var(--sidebar-text-primary)',\r\n                        backgroundColor: 'transparent'\r\n                      }}\r\n                      onMouseEnter={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';\r\n                      }}\r\n                      onMouseLeave={(e) => {\r\n                        e.currentTarget.style.backgroundColor = 'transparent';\r\n                      }}>\r\n                  <Settings className=\"w-4 h-4\" />\r\n                  Settings\r\n                </Link>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem \r\n                onClick={logout} \r\n                className=\"flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200\"\r\n                style={{ \r\n                  color: '#ff8583',\r\n                  backgroundColor: 'transparent'\r\n                }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.backgroundColor = 'transparent';\r\n                }}\r\n              >\r\n                <LogOut className=\"w-4 h-4\" />\r\n                Logout\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      )}\r\n    </aside>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAgBA,MAAM,UAAkC,CAAC,EAAE,SAAS,EAAE,gBAAgB,EAAE,oBAAoB,KAAK,EAAE;;IACjG,MAAM,EACJ,WAAW,EACX,cAAc,EACd,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,QAAQ,GAAG,CAAC,iDAAiD;IAE7D,iDAAiD;IACjD,MAAM,gBAAgB,UAAU,MAAM,KAAK,SAAS,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG,KAAK;IAC1F,QAAQ,GAAG,CAAC,2CAA2C;IAEvD,MAAM,eAAe,CAAC,IAAY;QAChC,QAAQ,GAAG,CAAC,0BAA0B,IAAI,sBAAsB;QAChE,YAAY;QACZ,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,uCAAuC,IAAI,kBAAkB;QACzE,IAAI,YAAY,IAAI,IAAI;YACtB,WAAW,IAAI,YAAY,IAAI;QACjC;QACA,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW;YACjB,cAAc;YAEd,iFAAiF;YACjF,IAAI,kBAAkB,QAAQ;gBAC5B,6EAA6E;gBAC7E,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAE9D,IAAI,eAAe,MAAM,GAAG,GAAG;oBAC7B,6DAA6D;oBAC7D,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC7C,OAAO;oBACL,qCAAqC;oBACrC;oBACA,iEAAiE;oBACjE,OAAO,IAAI,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,0DAA0D;QAC5D;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,YAAY,SAAS,CAAC,OAAO,KAAK,EAAE;QAC1C,MAAM,WAAW,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;QAE9C,IAAI,YAAY,GAAG;YACjB,OAAO;QACT,OAAO,IAAI,YAAY,IAAI;YACzB,OAAO,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK,CAAC;QACxC,OAAO,IAAI,WAAW,GAAG;YACvB,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,KAAK,CAAC;QACvC,OAAO;YACL,OAAO,UAAU,kBAAkB;QACrC;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,oBAAoB,CAAC;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,wBAAwB,EAAE,cAAc,SAAS,YAAY,kFAAkF,CAAC;QAC5J,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,gBAAgB;QAClB;;0BAGA,6LAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,wBAAwB,oBAAoB;gBACzI,OAAO;oBAAE,iBAAiB;gBAAoB;0BAE9C,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAO;wBACL,OAAO;wBACP,iBAAiB;oBACnB;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,yCAAyC;wBAC/F,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,cAAc,CAAC;wBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,oBAAoB,eAAe;wBACrE,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;oBACpC;oBACA,SAAS;8BAET,cAAA,6LAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,OAAO;4BACL,WAAW,cAAc,eAAe;wBAC1C;kCAEA,cAAA,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;YAKb,CAAC,6BACA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,eAAe,KAAK,IAAI;oCACvJ,OAAO;wCACL,OAAO,aAAa,eAAe,gCAAgC;wCACnE,iBAAiB,aAAa,eAAe,4BAA4B;oCAC3E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,cAAc;4CAC7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,+NAAA,CAAA,kBAAe;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAKpF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,oGAAoG,EAAE,aAAa,aAAa,KAAK,IAAI;oCACrJ,OAAO;wCACL,OAAO,aAAa,aAAa,gCAAgC;wCACjE,iBAAiB,aAAa,aAAa,4BAA4B;oCACzE;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,YAAY;4CAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK9E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,iBAAiB,gCAAgC;wCACrE,iBAAiB,aAAa,iBAAiB,4BAA4B;oCAC7E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,gBAAgB;4CAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;0CAK7E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAW,CAAC,mGAAmG,CAAC;oCAChH,OAAO;wCACL,OAAO,aAAa,kBAAkB,gCAAgC;wCACtE,iBAAiB,aAAa,kBAAkB,4BAA4B;oCAC9E;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,aAAa,iBAAiB;4CAChC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;;sDAEA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,OAAO;gDAAE,OAAO;4CAAsB;;;;;;wCAAK;;;;;;;;;;;;;;;;;;kCAO5E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;4BACV,OAAO;gCACL,OAAO;gCACP,iBAAiB;4BACnB;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,CAAC,mBAAmB;oCACtB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gCAC1C;4BACF;;8CAEA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAAsB;;;;;;gCAC/D,oBAAoB,gBAAgB;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO;oCACP,iBAAiB;gCACnB;0CACD;;;;;;4BAIA,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,OAAO;wCAAE,OAAO;oCAA+B;8CAAG;;;;;;;;;;;4BAM9E,CAAC,kBAAkB,YAAY,GAAG,CAAC,CAAC;gCACnC,MAAM,WAAW,KAAK,EAAE,KAAK;gCAC7B,MAAM,aAAa,aAAa,KAAK,EAAE;gCACvC,qBACE,6LAAC;oCAEC,WAAW,CAAC,wGAAwG,EAAE,WAAW,KAAK,IAAI;oCAC1I,OAAO;wCACL,iBAAiB,WAAW,4BAA4B;oCAC1D;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,CAAC,UAAU;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;oCACF;oCACA,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;8CAE5C,2BACC,6LAAC;wCACC,UAAU,CAAA;4CAAO,EAAE,cAAc;4CAAI,mBAAmB,KAAK,EAAE;wCAAG;wCAClE,WAAU;;0DAEV,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO;oDAAE,OAAO;gDAA8B;gDAC9C,OAAO;gDACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC5C,WAAW,CAAA;oDAAO,IAAI,EAAE,GAAG,KAAK,UAAU,YAAY;gDAAO;;;;;;0DAE/D,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;6DAKH;;0DACE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAA8B;8DAE7C,KAAK,KAAK;;;;;;;;;;;0DAIf,6LAAC;gDAAI,WAAU;gDAA8B,SAAS,CAAA,IAAK,EAAE,eAAe;0DAC1E,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,OAAO;oEAAE,OAAO;gEAAsB;gEACtC,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAc,CAAC;oEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gEAC1C;gEACA,cAAW;0EAEX,cAAA,6LAAC,6NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,6LAAC,+IAAA,CAAA,sBAAmB;4DAClB,OAAM;4DACN,MAAK;4DACL,YAAY;4DACZ,WAAU;4DACV,OAAO;gEACL,iBAAiB;gEACjB,OAAO;4DACT;;8EAEA,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;oEAC/C,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;8EAGD,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,aAAa,KAAK,EAAE;oEACnC,WAAU;oEACV,OAAO;wEACL,OAAO;wEACP,iBAAiB;oEACnB;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oEAC1C;8EACD;;;;;;;;;;;;;;;;;;;;;;;;;mCA7GN,KAAK,EAAE;;;;;4BAuHlB;4BAEC,CAAC,kBAAkB,YAAY,MAAM,KAAK,mBACzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uOAAA,CAAA,oBAAiB;wCAChB,WAAU;wCACV,OAAO;4CAAE,OAAO;wCAA+B;;;;;;kDAEjD,6LAAC;wCAAE,WAAU;wCAAU,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;wCAAe,OAAO;4CAAE,OAAO;wCAA+B;kDAAG;;;;;;;;;;;;;;;;;;kCAQpF,6LAAC;wBAAI,WAAU;wBAA6B,OAAO;4BAAE,aAAa;wBAAwB;kCACxF,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC;wCAAI,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAc;wCACxC,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACH,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAC,iBAAiB;gDAA0U;;;;;;0DAErW,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;oDAAqC,OAAO;wDAAE,OAAO;oDAA8B;8DAChG;;;;;;;;;;;;;;;;;;;;;;8CAKT,6LAAC,+IAAA,CAAA,sBAAmB;oCAClB,OAAM;oCACN,MAAK;oCACL,YAAY;oCACZ,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,OAAO;oCACT;;sDAEA,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAC1B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;gDAC3B,OAAO;oDACL,OAAO;oDACP,iBAAiB;gDACnB;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;;kEACJ,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6LAAC,+IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,iBAAiB;4CACnB;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC1C;;8DAEA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzC,6BACC,6LAAC;gBAAI,WAAU;gBAA4B,OAAO;oBAAE,aAAa;gBAAwB;0BACvF,cAAA,6LAAC,+IAAA,CAAA,eAAY;;sCACX,6LAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAC,iBAAiB;oCAA0U;;;;;;;;;;;;;;;;sCAIzW,6LAAC,+IAAA,CAAA,sBAAmB;4BAClB,OAAM;4BACN,MAAK;4BACL,YAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,iBAAiB;gCACjB,OAAO;4BACT;;8CAEA,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;wCAC1B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIhC,6LAAC,+IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;wCAC3B,OAAO;4CACL,OAAO;4CACP,iBAAiB;wCACnB;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;wCACA,cAAc,CAAC;4CACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC1C;;0DACJ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIpC,6LAAC,+IAAA,CAAA,mBAAgB;oCACf,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;oCACnB;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oCAC1C;;sDAEA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GA1kBM;;QAMA,0IAAA,CAAA,iBAAc;QACO,mIAAA,CAAA,UAAO;QACf,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KATpB;uCA4kBS", "debugId": null}}, {"offset": {"line": 2419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { ReactNode, useState, useRef, useCallback } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useChatHistory } from \"@/providers/ChatHistoryContext\";\r\nimport { PageHeaderProvider } from '@/providers/PageHeaderContext';\r\nimport Header from './Header';\r\nimport Sidebar from './Sidebar';\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\r\n  const { setActiveChat } = useChatHistory();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);\r\n  const [isCreatingNewChat, setIsCreatingNewChat] = useState<boolean>(false);\r\n  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const handleNewChat = useCallback(async () => {\r\n    // Prevent multiple simultaneous new chat creations\r\n    if (isCreatingNewChat) {\r\n      console.log('New chat creation already in progress, ignoring click');\r\n      return;\r\n    }\r\n\r\n    // Clear any existing debounce timeout\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n\r\n    // Set debounce timeout to prevent rapid clicking\r\n    debounceTimeoutRef.current = setTimeout(async () => {\r\n      setIsCreatingNewChat(true);\r\n      \r\n      try {\r\n        // Clear the active chat to show the welcome message\r\n        await setActiveChat(null);\r\n        \r\n        // Navigate to the base chat route without a specific chat ID\r\n        router.push('/chat');\r\n      } catch (error) {\r\n        console.error('Error creating new chat:', error);\r\n      } finally {\r\n        setIsCreatingNewChat(false);\r\n      }\r\n    }, 300); // 300ms debounce to prevent rapid clicking\r\n  }, [isCreatingNewChat, setActiveChat, router]);\r\n\r\n  const handleToggleCollapse = (collapsed: boolean) => {\r\n    setSidebarCollapsed(collapsed);\r\n  };\r\n\r\n  // Cleanup timeout on unmount\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <PageHeaderProvider>\r\n      <div className=\"flex h-screen bg-sidebar-bg\" style={{ fontFamily: 'var(--font-inter), var(--font-noto-sans), sans-serif' }}>\r\n      <Sidebar \r\n        onNewChat={handleNewChat} \r\n        onToggleCollapse={handleToggleCollapse}\r\n        isCreatingNewChat={isCreatingNewChat}\r\n      />\r\n      <main className=\"flex flex-col flex-1 min-w-0 bg-sidebar-bg overflow-y-auto\">\r\n        <Header />\r\n        <div className=\"flex-1\">\r\n          {children}\r\n        </div>\r\n      </main>\r\n    </div>\r\n    </PageHeaderProvider>\r\n  );\r\n};\r\n\r\nexport default Layout;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAYA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAChC,mDAAmD;YACnD,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,sCAAsC;YACtC,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;YAEA,iDAAiD;YACjD,mBAAmB,OAAO,GAAG;qDAAW;oBACtC,qBAAqB;oBAErB,IAAI;wBACF,oDAAoD;wBACpD,MAAM,cAAc;wBAEpB,6DAA6D;wBAC7D,OAAO,IAAI,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,qBAAqB;oBACvB;gBACF;oDAAG,MAAM,2CAA2C;QACtD;4CAAG;QAAC;QAAmB;QAAe;KAAO;IAE7C,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,6JAAA,CAAA,UAAK,CAAC,SAAS;4BAAC;YACd;oCAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC,yIAAA,CAAA,qBAAkB;kBACjB,cAAA,6LAAC;YAAI,WAAU;YAA8B,OAAO;gBAAE,YAAY;YAAuD;;8BACzH,6LAAC,0IAAA,CAAA,UAAO;oBACN,WAAW;oBACX,kBAAkB;oBAClB,mBAAmB;;;;;;8BAErB,6LAAC;oBAAK,WAAU;;sCACd,6LAAC,yIAAA,CAAA,UAAM;;;;;sCACP,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMX;GApEM;;QACsB,0IAAA,CAAA,iBAAc;QACzB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHxB;uCAsES", "debugId": null}}, {"offset": {"line": 2564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/hooks/usePageTitle.ts"], "sourcesContent": ["import { useEffect, useMemo } from 'react';\r\nimport { usePageHeader } from '@/providers/PageHeaderContext';\r\n\r\ninterface UsePageTitleOptions {\r\n  title: string;\r\n  subtitle?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n}\r\n\r\n/**\r\n * Custom hook for setting page-specific header information\r\n * \r\n * Industry standard approach for managing page titles and metadata.\r\n * Automatically handles cleanup when component unmounts.\r\n * \r\n * @param options - Page header configuration\r\n */\r\nexport const usePageTitle = (options: UsePageTitleOptions) => {\r\n  const { setPageHeader, resetPageHeader } = usePageHeader();\r\n\r\n  useEffect(() => {\r\n    // Set the page header when component mounts or options change\r\n    setPageHeader(options);\r\n\r\n    // Cleanup: reset to default when component unmounts\r\n    return () => {\r\n      resetPageHeader();\r\n    };\r\n  }, [\r\n    options.title, \r\n    options.subtitle, \r\n    options.icon, \r\n    setPageHeader, \r\n    resetPageHeader\r\n  ]);\r\n};\r\n\r\n/**\r\n * Simplified hook for just setting a page title\r\n * \r\n * @param title - Page title\r\n * @param icon - Optional icon component\r\n */\r\nexport const useSimplePageTitle = (title: string, icon?: React.ComponentType<{ className?: string }>) => {\r\n  usePageTitle({ title, icon });\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAgBO,MAAM,eAAe,CAAC;;IAC3B,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,8DAA8D;YAC9D,cAAc;YAEd,oDAAoD;YACpD;0CAAO;oBACL;gBACF;;QACF;iCAAG;QACD,QAAQ,KAAK;QACb,QAAQ,QAAQ;QAChB,QAAQ,IAAI;QACZ;QACA;KACD;AACH;GAlBa;;QACgC,yIAAA,CAAA,gBAAa;;;AAyBnD,MAAM,qBAAqB,CAAC,OAAe;;IAChD,aAAa;QAAE;QAAO;IAAK;AAC7B;IAFa;;QACX", "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/message-input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\n\r\n// --- Utility Function ---\r\ntype ClassValue = string | number | boolean | null | undefined;\r\nfunction cn(...inputs: ClassValue[]): string { \r\n  return inputs.filter(Boolean).join(\" \"); \r\n}\r\n\r\n// --- Radix UI Components ---\r\nconst TooltipProvider = TooltipPrimitive.Provider;\r\nconst Tooltip = TooltipPrimitive.Root;\r\nconst TooltipTrigger = TooltipPrimitive.Trigger;\r\nconst TooltipContent = React.forwardRef<\r\n  React.ElementRef<typeof TooltipPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & { showArrow?: boolean }\r\n>(({ className, sideOffset = 4, showArrow = false, ...props }, ref) => (\r\n  <TooltipPrimitive.Portal>\r\n    <TooltipPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"relative z-50 max-w-[280px] rounded-md bg-popover text-popover-foreground px-1.5 py-1 text-xs animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {props.children}\r\n      {showArrow && <TooltipPrimitive.Arrow className=\"-my-px fill-popover\" />}\r\n    </TooltipPrimitive.Content>\r\n  </TooltipPrimitive.Portal>\r\n));\r\nTooltipContent.displayName = TooltipPrimitive.Content.displayName;\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\nconst DialogPortal = DialogPrimitive.Portal;\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/60 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-[90vw] md:max-w-[800px] translate-x-[-50%] translate-y-[-50%] gap-4 border-none bg-transparent p-0 shadow-none duration-300 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className=\"relative bg-card dark:bg-[#303030] rounded-[28px] overflow-hidden shadow-2xl p-1\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"absolute right-3 top-3 z-10 rounded-full bg-background/50 dark:bg-[#303030] p-1 hover:bg-accent dark:hover:bg-[#515151] transition-all\">\r\n          <XIcon className=\"h-5 w-5 text-muted-foreground dark:text-gray-200 hover:text-foreground dark:hover:text-white\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </div>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\n// --- Icon Components ---\r\nconst PlusIcon = (props: React.SVGProps<SVGSVGElement>) => (\r\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" {...props}>\r\n    <path d=\"M12 5V19\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n    <path d=\"M5 12H19\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n  </svg>\r\n);\r\n\r\nconst SendIcon = (props: React.SVGProps<SVGSVGElement>) => (\r\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" {...props}>\r\n    <path d=\"M12 5.25L12 18.75\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n    <path d=\"M18.75 12L12 5.25L5.25 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n  </svg>\r\n);\r\n\r\nconst XIcon = (props: React.SVGProps<SVGSVGElement>) => (\r\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" {...props}>\r\n    <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" />\r\n    <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" />\r\n  </svg>\r\n);\r\n\r\n// --- Types ---\r\ninterface MessageInputProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onSubmit'> {\r\n  onSubmit?: (message: string, file?: File) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\n// --- Main Component ---\r\nexport const MessageInput = React.forwardRef<HTMLTextAreaElement, MessageInputProps>(\r\n  ({ className, onSubmit, isLoading = false, ...props }, ref) => {\r\n    // --- State ---\r\n    const internalTextareaRef = React.useRef<HTMLTextAreaElement>(null);\r\n    const fileInputRef = React.useRef<HTMLInputElement>(null);\r\n    const [message, setMessage] = React.useState(\"\");\r\n    const [attachedFile, setAttachedFile] = React.useState<File | null>(null);\r\n    const [imagePreview, setImagePreview] = React.useState<string | null>(null);\r\n    const [isImageDialogOpen, setIsImageDialogOpen] = React.useState(false);\r\n\r\n    // --- Refs ---\r\n    React.useImperativeHandle(ref, () => internalTextareaRef.current!, []);\r\n\r\n    // --- Auto-resize textarea ---\r\n    React.useLayoutEffect(() => {\r\n      const textarea = internalTextareaRef.current;\r\n      if (textarea) {\r\n        textarea.style.height = \"auto\";\r\n        const newHeight = Math.min(textarea.scrollHeight, 200);\r\n        textarea.style.height = `${newHeight}px`;\r\n      }\r\n    }, [message]);\r\n\r\n    // --- Computed Values ---\r\n    const canSubmit = (message.trim().length > 0 || attachedFile) && !isLoading;\r\n\r\n    // --- Event Handlers ---\r\n    const handleInputChange = React.useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n      setMessage(e.target.value);\r\n      if (props.onChange) props.onChange(e);\r\n    }, [props.onChange]);\r\n\r\n    const handleKeyDown = React.useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n      if (e.key === 'Enter' && !e.shiftKey) {\r\n        e.preventDefault();\r\n        handleSubmit();\r\n      }\r\n      if (props.onKeyDown) props.onKeyDown(e);\r\n    }, [props.onKeyDown]);\r\n\r\n    const handleSubmit = React.useCallback(() => {\r\n      if (!canSubmit) return;\r\n      \r\n      const messageToSend = message.trim();\r\n      console.log('MessageInput: Submitting message', { message: messageToSend, file: attachedFile });\r\n      \r\n      // Call parent handler\r\n      onSubmit?.(messageToSend, attachedFile || undefined);\r\n      \r\n      // Reset form state\r\n      setMessage(\"\");\r\n      setAttachedFile(null);\r\n      setImagePreview(null);\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = \"\";\r\n      }\r\n    }, [message, attachedFile, canSubmit, onSubmit]);\r\n\r\n    const handleFileSelect = React.useCallback(() => {\r\n      fileInputRef.current?.click();\r\n    }, []);\r\n\r\n    const handleFileChange = React.useCallback((event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = event.target.files?.[0];\r\n      if (file && file.type.startsWith(\"image/\")) {\r\n        setAttachedFile(file);\r\n        \r\n        const reader = new FileReader();\r\n        reader.onloadend = () => {\r\n          setImagePreview(reader.result as string);\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n      // Clear the input to allow selecting the same file again\r\n      event.target.value = \"\";\r\n    }, []);\r\n\r\n    const handleRemoveFile = React.useCallback((e: React.MouseEvent<HTMLButtonElement>) => {\r\n      e.stopPropagation();\r\n      setAttachedFile(null);\r\n      setImagePreview(null);\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = \"\";\r\n      }\r\n    }, []);\r\n\r\n    // --- Render ---\r\n    return (\r\n      <div className={cn(\r\n        \"flex flex-col rounded-[28px] p-2 shadow-sm transition-colors bg-white border dark:bg-[#303030] dark:border-transparent cursor-text\",\r\n        className\r\n      )}>\r\n        {/* Hidden file input */}\r\n        <input\r\n          type=\"file\"\r\n          ref={fileInputRef}\r\n          onChange={handleFileChange}\r\n          className=\"hidden\"\r\n          accept=\"image/*\"\r\n          aria-label=\"Upload image\"\r\n        />\r\n        \r\n        {/* Image preview */}\r\n        {imagePreview && (\r\n          <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>\r\n            <div className=\"relative mb-1 w-fit rounded-[1rem] px-1 pt-1\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"transition-transform hover:scale-105\"\r\n                onClick={() => setIsImageDialogOpen(true)}\r\n                aria-label=\"View full size image\"\r\n              >\r\n                <img\r\n                  src={imagePreview}\r\n                  alt=\"Attached image preview\"\r\n                  className=\"h-14 w-14 rounded-[1rem] object-cover\"\r\n                />\r\n              </button>\r\n              <button\r\n                onClick={handleRemoveFile}\r\n                className=\"absolute right-2 top-2 z-10 flex h-4 w-4 items-center justify-center rounded-full bg-white/50 dark:bg-[#303030] text-black dark:text-white transition-colors hover:bg-accent dark:hover:bg-[#515151]\"\r\n                aria-label=\"Remove attached image\"\r\n              >\r\n                <XIcon className=\"h-3 w-3\" />\r\n              </button>\r\n            </div>\r\n            <DialogContent>\r\n              <img\r\n                src={imagePreview}\r\n                alt=\"Full size attached image\"\r\n                className=\"w-full max-h-[95vh] object-contain rounded-[24px]\"\r\n              />\r\n            </DialogContent>\r\n          </Dialog>\r\n        )}\r\n        \r\n        {/* Message textarea */}\r\n        <textarea\r\n          ref={internalTextareaRef}\r\n          rows={1}\r\n          value={message}\r\n          onChange={handleInputChange}\r\n          onKeyDown={handleKeyDown}\r\n          placeholder=\"Message...\"\r\n          disabled={isLoading}\r\n          className=\"w-full resize-none border-0 bg-transparent p-3 text-foreground dark:text-white placeholder:text-muted-foreground dark:placeholder:text-gray-300 focus:ring-0 focus-visible:outline-none min-h-12 disabled:opacity-50\"\r\n          aria-label=\"Type your message\"\r\n          {...props}\r\n        />\r\n        \r\n        {/* Action buttons */}\r\n        <div className=\"mt-0.5 p-1 pt-0\">\r\n          <TooltipProvider delayDuration={100}>\r\n            <div className=\"flex items-center justify-between\">\r\n              {/* File attachment button */}\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleFileSelect}\r\n                    disabled={isLoading}\r\n                    className=\"flex h-8 w-8 items-center justify-center rounded-full text-foreground dark:text-white transition-colors hover:bg-accent dark:hover:bg-[#515151] focus-visible:outline-none disabled:opacity-50\"\r\n                    aria-label=\"Attach image\"\r\n                  >\r\n                    <PlusIcon className=\"h-5 w-5\" />\r\n                  </button>\r\n                </TooltipTrigger>\r\n                <TooltipContent side=\"top\" showArrow={true}>\r\n                  <p>Attach image</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n\r\n              {/* Send button */}\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleSubmit}\r\n                    disabled={!canSubmit}\r\n                    className=\"flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none bg-black text-white hover:bg-black/80 dark:bg-white dark:text-black dark:hover:bg-white/80 disabled:bg-black/40 dark:disabled:bg-[#515151]\"\r\n                    aria-label=\"Send message\"\r\n                  >\r\n                    <SendIcon className=\"h-5 w-5\" />\r\n                  </button>\r\n                </TooltipTrigger>\r\n                <TooltipContent side=\"top\" showArrow={true}>\r\n                  <p>Send message</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </div>\r\n          </TooltipProvider>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nMessageInput.displayName = \"MessageInput\";\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AAIA,SAAS,GAAG,GAAG,MAAoB;IACjC,OAAO,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC;AACrC;AAEA,8BAA8B;AAC9B,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AACjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AACrC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAC/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,YAAY,KAAK,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,YAAY;YACZ,WAAW,GACT,4XACA;YAED,GAAG,KAAK;;gBAER,MAAM,QAAQ;gBACd,2BAAa,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;KAfhD;AAmBN,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW;AAEjE,MAAM,SAAS,qKAAA,CAAA,OAAoB;AACnC,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAC3C,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAC7C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,GACT,2KACA;QAED,GAAG,KAAK;;;;;;MAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,GACT,sWACA;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC,qKAAA,CAAA,QAAqB;4BAAC,WAAU;;8CAC/B,6LAAC;oCAAM,WAAU;;;;;;8CACjB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAlB9B;AAwBN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,0BAA0B;AAC1B,MAAM,WAAW,CAAC,sBAChB,6LAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;QAA8B,GAAG,KAAK;;0BACtG,6LAAC;gBAAK,GAAE;gBAAW,QAAO;gBAAe,aAAY;gBAAM,eAAc;gBAAQ,gBAAe;;;;;;0BAChG,6LAAC;gBAAK,GAAE;gBAAW,QAAO;gBAAe,aAAY;gBAAM,eAAc;gBAAQ,gBAAe;;;;;;;;;;;;MAH9F;AAON,MAAM,WAAW,CAAC,sBAChB,6LAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,OAAM;QAA8B,GAAG,KAAK;;0BACtG,6LAAC;gBAAK,GAAE;gBAAoB,QAAO;gBAAe,aAAY;gBAAI,eAAc;gBAAQ,gBAAe;;;;;;0BACvG,6LAAC;gBAAK,GAAE;gBAA4B,QAAO;gBAAe,aAAY;gBAAI,eAAc;gBAAQ,gBAAe;;;;;;;;;;;;MAH7G;AAON,MAAM,QAAQ,CAAC,sBACb,6LAAC;QAAI,OAAM;QAAK,QAAO;QAAK,SAAQ;QAAY,MAAK;QAAO,QAAO;QAAe,aAAY;QAAI,eAAc;QAAQ,gBAAe;QAAS,GAAG,KAAK;;0BACtJ,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;;;;;;0BAC/B,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;;;;;;;MAH7B;AAcC,MAAM,6BAAe,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WACzC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAE,GAAG,OAAO,EAAE;;IACrD,gBAAgB;IAChB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAuB;IAC9D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAoB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAe;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEjE,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,sBAAyB,AAAD,EAAE;4CAAK,IAAM,oBAAoB,OAAO;2CAAG,EAAE;IAErE,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,kBAAqB,AAAD;wCAAE;YACpB,MAAM,WAAW,oBAAoB,OAAO;YAC5C,IAAI,UAAU;gBACZ,SAAS,KAAK,CAAC,MAAM,GAAG;gBACxB,MAAM,YAAY,KAAK,GAAG,CAAC,SAAS,YAAY,EAAE;gBAClD,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;YAC1C;QACF;uCAAG;QAAC;KAAQ;IAEZ,0BAA0B;IAC1B,MAAM,YAAY,CAAC,QAAQ,IAAI,GAAG,MAAM,GAAG,KAAK,YAAY,KAAK,CAAC;IAElE,yBAAyB;IACzB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;uDAAE,CAAC;YAC3C,WAAW,EAAE,MAAM,CAAC,KAAK;YACzB,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC;QACrC;sDAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;mDAAE,CAAC;YACvC,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;gBACpC,EAAE,cAAc;gBAChB;YACF;YACA,IAAI,MAAM,SAAS,EAAE,MAAM,SAAS,CAAC;QACvC;kDAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;kDAAE;YACrC,IAAI,CAAC,WAAW;YAEhB,MAAM,gBAAgB,QAAQ,IAAI;YAClC,QAAQ,GAAG,CAAC,oCAAoC;gBAAE,SAAS;gBAAe,MAAM;YAAa;YAE7F,sBAAsB;YACtB,WAAW,eAAe,gBAAgB;YAE1C,mBAAmB;YACnB,WAAW;YACX,gBAAgB;YAChB,gBAAgB;YAChB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;iDAAG;QAAC;QAAS;QAAc;QAAW;KAAS;IAE/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACzC,aAAa,OAAO,EAAE;QACxB;qDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE,CAAC;YAC1C,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;YACpC,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBAC1C,gBAAgB;gBAEhB,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS;kEAAG;wBACjB,gBAAgB,OAAO,MAAM;oBAC/B;;gBACA,OAAO,aAAa,CAAC;YACvB;YACA,yDAAyD;YACzD,MAAM,MAAM,CAAC,KAAK,GAAG;QACvB;qDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE,CAAC;YAC1C,EAAE,eAAe;YACjB,gBAAgB;YAChB,gBAAgB;YAChB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;qDAAG,EAAE;IAEL,iBAAiB;IACjB,qBACE,6LAAC;QAAI,WAAW,GACd,sIACA;;0BAGA,6LAAC;gBACC,MAAK;gBACL,KAAK;gBACL,UAAU;gBACV,WAAU;gBACV,QAAO;gBACP,cAAW;;;;;;YAIZ,8BACC,6LAAC;gBAAO,MAAM;gBAAmB,cAAc;;kCAC7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,qBAAqB;gCACpC,cAAW;0CAEX,cAAA,6LAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAM,WAAU;;;;;;;;;;;;;;;;;kCAGrB,6LAAC;kCACC,cAAA,6LAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBACC,KAAK;gBACL,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,aAAY;gBACZ,UAAU;gBACV,WAAU;gBACV,cAAW;gBACV,GAAG,KAAK;;;;;;0BAIX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAgB,eAAe;8BAC9B,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAe,OAAO;kDACrB,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC;gDAAS,WAAU;;;;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAe,MAAK;wCAAM,WAAW;kDACpC,cAAA,6LAAC;sDAAE;;;;;;;;;;;;;;;;;0CAKP,6LAAC;;kDACC,6LAAC;wCAAe,OAAO;kDACrB,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC;gDAAS,WAAU;;;;;;;;;;;;;;;;kDAGxB,6LAAC;wCAAe,MAAK;wCAAM,WAAW;kDACpC,cAAA,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;;AAGF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/placeholders-and-vanish-input.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AnimatePresence, motion } from \"framer-motion\";\r\nimport { useCallback, useEffect, useRef, useState } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport function PlaceholdersAndVanishInput({\r\n  placeholders,\r\n  onChange,\r\n  onSubmit,\r\n  disabled = false,\r\n}: {\r\n  placeholders: string[];\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;\r\n  disabled?: boolean;\r\n}) {\r\n  const [currentPlaceholder, setCurrentPlaceholder] = useState(0);\r\n\r\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const startAnimation = () => {\r\n    intervalRef.current = setInterval(() => {\r\n      setCurrentPlaceholder((prev) => (prev + 1) % placeholders.length);\r\n    }, 3000);\r\n  };\r\n  const handleVisibilityChange = () => {\r\n    if (document.visibilityState !== \"visible\" && intervalRef.current) {\r\n      clearInterval(intervalRef.current); // Clear the interval when the tab is not visible\r\n      intervalRef.current = null;\r\n    } else if (document.visibilityState === \"visible\") {\r\n      startAnimation(); // Restart the interval when the tab becomes visible\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    startAnimation();\r\n    document.addEventListener(\"visibilitychange\", handleVisibilityChange);\r\n\r\n    return () => {\r\n      if (intervalRef.current) {\r\n        clearInterval(intervalRef.current);\r\n      }\r\n      document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n    };\r\n  }, [placeholders]);\r\n\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const newDataRef = useRef<any[]>([]);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const [value, setValue] = useState(\"\");\r\n  const [animating, setAnimating] = useState(false);\r\n\r\n  const draw = useCallback(() => {\r\n    if (!inputRef.current) return;\r\n    const canvas = canvasRef.current;\r\n    if (!canvas) return;\r\n    const ctx = canvas.getContext(\"2d\");\r\n    if (!ctx) return;\r\n\r\n    canvas.width = 800;\r\n    canvas.height = 800;\r\n    ctx.clearRect(0, 0, 800, 800);\r\n    const computedStyles = getComputedStyle(inputRef.current);\r\n\r\n    const fontSize = parseFloat(computedStyles.getPropertyValue(\"font-size\"));\r\n    ctx.font = `${fontSize * 2}px ${computedStyles.fontFamily}`;\r\n    ctx.fillStyle = \"#FFF\";\r\n    ctx.fillText(value, 16, 40);\r\n\r\n    const imageData = ctx.getImageData(0, 0, 800, 800);\r\n    const pixelData = imageData.data;\r\n    const newData: any[] = [];\r\n\r\n    for (let t = 0; t < 800; t++) {\r\n      let i = 4 * t * 800;\r\n      for (let n = 0; n < 800; n++) {\r\n        let e = i + 4 * n;\r\n        if (\r\n          pixelData[e] !== 0 &&\r\n          pixelData[e + 1] !== 0 &&\r\n          pixelData[e + 2] !== 0\r\n        ) {\r\n          newData.push({\r\n            x: n,\r\n            y: t,\r\n            color: [\r\n              pixelData[e],\r\n              pixelData[e + 1],\r\n              pixelData[e + 2],\r\n              pixelData[e + 3],\r\n            ],\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    newDataRef.current = newData.map(({ x, y, color }) => ({\r\n      x,\r\n      y,\r\n      r: 1,\r\n      color: `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${color[3]})`,\r\n    }));\r\n  }, [value]);\r\n\r\n  useEffect(() => {\r\n    draw();\r\n  }, [value, draw]);\r\n\r\n  const animate = (start: number) => {\r\n    const animateFrame = (pos: number = 0) => {\r\n      requestAnimationFrame(() => {\r\n        const newArr = [];\r\n        for (let i = 0; i < newDataRef.current.length; i++) {\r\n          const current = newDataRef.current[i];\r\n          if (current.x < pos) {\r\n            newArr.push(current);\r\n          } else {\r\n            if (current.r <= 0) {\r\n              current.r = 0;\r\n              continue;\r\n            }\r\n            current.x += Math.random() > 0.5 ? 1 : -1;\r\n            current.y += Math.random() > 0.5 ? 1 : -1;\r\n            current.r -= 0.05 * Math.random();\r\n            newArr.push(current);\r\n          }\r\n        }\r\n        newDataRef.current = newArr;\r\n        const ctx = canvasRef.current?.getContext(\"2d\");\r\n        if (ctx) {\r\n          ctx.clearRect(pos, 0, 800, 800);\r\n          newDataRef.current.forEach((t) => {\r\n            const { x: n, y: i, r: s, color: color } = t;\r\n            if (n > pos) {\r\n              ctx.beginPath();\r\n              ctx.rect(n, i, s, s);\r\n              ctx.fillStyle = color;\r\n              ctx.strokeStyle = color;\r\n              ctx.stroke();\r\n            }\r\n          });\r\n        }\r\n        if (newDataRef.current.length > 0) {\r\n          animateFrame(pos - 8);\r\n        } else {\r\n          setValue(\"\");\r\n          setAnimating(false);\r\n        }\r\n      });\r\n    };\r\n    animateFrame(start);\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === \"Enter\" && !animating && !disabled) {\r\n      vanishAndSubmit();\r\n    }\r\n  };\r\n\r\n  const vanishAndSubmit = () => {\r\n    if (disabled) return;\r\n    \r\n    setAnimating(true);\r\n    draw();\r\n\r\n    const value = inputRef.current?.value || \"\";\r\n    if (value && inputRef.current) {\r\n      const maxX = newDataRef.current.reduce(\r\n        (prev, current) => (current.x > prev ? current.x : prev),\r\n        0\r\n      );\r\n      animate(maxX);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault();\r\n    if (disabled) return;\r\n    \r\n    vanishAndSubmit();\r\n    onSubmit && onSubmit(e);\r\n  };\r\n  return (\r\n    <form\r\n      className={cn(\r\n        \"w-full relative max-w-xl mx-auto h-12 rounded-[28px] overflow-hidden transition duration-200\",\r\n        \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700\",\r\n        \"shadow-sm dark:shadow-gray-900/10\",\r\n        value && \"\",\r\n        disabled && \"opacity-50 cursor-not-allowed\"\r\n      )}\r\n      onSubmit={handleSubmit}\r\n    >\r\n      <canvas\r\n        className={cn(\r\n          \"absolute pointer-events-none text-base transform scale-50 top-[20%] left-2 sm:left-8 origin-top-left pr-20\",\r\n          !animating ? \"opacity-0\" : \"opacity-100\"\r\n        )}\r\n        ref={canvasRef}\r\n      />\r\n      <input\r\n        onChange={(e) => {\r\n          if (!animating && !disabled) {\r\n            setValue(e.target.value);\r\n            onChange && onChange(e);\r\n          }\r\n        }}\r\n        onKeyDown={handleKeyDown}\r\n        ref={inputRef}\r\n        value={value}\r\n        type=\"text\"\r\n        disabled={disabled}\r\n        className={cn(\r\n          \"w-full relative text-sm sm:text-base z-50 border-none bg-transparent h-full rounded-[28px] focus:outline-none focus:ring-0 pl-4 sm:pl-10 pr-20\",\r\n          \"text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400\",\r\n          animating && \"text-transparent\",\r\n          disabled && \"cursor-not-allowed\"\r\n        )}\r\n      />\r\n\r\n      <button\r\n        disabled={!value || disabled}\r\n        type=\"submit\"\r\n        className={cn(\r\n          \"absolute right-2 top-1/2 z-50 -translate-y-1/2 h-8 w-8 rounded-full transition duration-200 flex items-center justify-center disabled:opacity-30\",\r\n          value \r\n            ? \"bg-white dark:bg-gray-200 text-gray-800 dark:text-gray-900\" \r\n            : \"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500\"\r\n        )}\r\n      >\r\n        <motion.svg\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"2\"\r\n          strokeLinecap=\"round\"\r\n          strokeLinejoin=\"round\"\r\n          className={cn(\r\n            \"h-4 w-4\",\r\n            value \r\n              ? \"text-gray-800 dark:text-gray-900\" \r\n              : \"text-gray-400 dark:text-gray-500\"\r\n          )}\r\n        >\r\n          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\r\n          <motion.path\r\n            d=\"M5 12l14 0\"\r\n            initial={{\r\n              strokeDasharray: \"50%\",\r\n              strokeDashoffset: \"50%\",\r\n            }}\r\n            animate={{\r\n              strokeDashoffset: value ? 0 : \"50%\",\r\n            }}\r\n            transition={{\r\n              duration: 0.3,\r\n              ease: \"linear\",\r\n            }}\r\n          />\r\n          <path d=\"M13 18l6 -6\" />\r\n          <path d=\"M13 6l6 6\" />\r\n        </motion.svg>\r\n      </button>\r\n\r\n      <div className=\"absolute inset-0 flex items-center rounded-full pointer-events-none\">\r\n        <AnimatePresence mode=\"wait\">\r\n          {!value && (\r\n            <motion.p\r\n              initial={{\r\n                y: 5,\r\n                opacity: 0,\r\n              }}\r\n              key={`current-placeholder-${currentPlaceholder}`}\r\n              animate={{\r\n                y: 0,\r\n                opacity: 1,\r\n              }}\r\n              exit={{\r\n                y: -15,\r\n                opacity: 0,\r\n              }}\r\n              transition={{\r\n                duration: 0.3,\r\n                ease: \"linear\",\r\n              }}\r\n              className=\"text-sm sm:text-base font-normal pl-4 sm:pl-12 text-left w-[calc(100%-2rem)] truncate text-gray-500 dark:text-gray-400\"\r\n            >\r\n              {placeholders[currentPlaceholder]}\r\n            </motion.p>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAAA;;;AAJA;;;;AAMO,SAAS,2BAA2B,EACzC,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,WAAW,KAAK,EAMjB;;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAClD,MAAM,iBAAiB;QACrB,YAAY,OAAO,GAAG,YAAY;YAChC,sBAAsB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAClE,GAAG;IACL;IACA,MAAM,yBAAyB;QAC7B,IAAI,SAAS,eAAe,KAAK,aAAa,YAAY,OAAO,EAAE;YACjE,cAAc,YAAY,OAAO,GAAG,iDAAiD;YACrF,YAAY,OAAO,GAAG;QACxB,OAAO,IAAI,SAAS,eAAe,KAAK,WAAW;YACjD,kBAAkB,oDAAoD;QACxE;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR;YACA,SAAS,gBAAgB,CAAC,oBAAoB;YAE9C;wDAAO;oBACL,IAAI,YAAY,OAAO,EAAE;wBACvB,cAAc,YAAY,OAAO;oBACnC;oBACA,SAAS,mBAAmB,CAAC,oBAAoB;gBACnD;;QACF;+CAAG;QAAC;KAAa;IAEjB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAS,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YACvB,IAAI,CAAC,SAAS,OAAO,EAAE;YACvB,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YACb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAChB,IAAI,SAAS,CAAC,GAAG,GAAG,KAAK;YACzB,MAAM,iBAAiB,iBAAiB,SAAS,OAAO;YAExD,MAAM,WAAW,WAAW,eAAe,gBAAgB,CAAC;YAC5D,IAAI,IAAI,GAAG,GAAG,WAAW,EAAE,GAAG,EAAE,eAAe,UAAU,EAAE;YAC3D,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,OAAO,IAAI;YAExB,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,KAAK;YAC9C,MAAM,YAAY,UAAU,IAAI;YAChC,MAAM,UAAiB,EAAE;YAEzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,IAAI,IAAI,IAAI,IAAI;gBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;oBAC5B,IAAI,IAAI,IAAI,IAAI;oBAChB,IACE,SAAS,CAAC,EAAE,KAAK,KACjB,SAAS,CAAC,IAAI,EAAE,KAAK,KACrB,SAAS,CAAC,IAAI,EAAE,KAAK,GACrB;wBACA,QAAQ,IAAI,CAAC;4BACX,GAAG;4BACH,GAAG;4BACH,OAAO;gCACL,SAAS,CAAC,EAAE;gCACZ,SAAS,CAAC,IAAI,EAAE;gCAChB,SAAS,CAAC,IAAI,EAAE;gCAChB,SAAS,CAAC,IAAI,EAAE;6BACjB;wBACH;oBACF;gBACF;YACF;YAEA,WAAW,OAAO,GAAG,QAAQ,GAAG;gEAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;wBACrD;wBACA;wBACA,GAAG;wBACH,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;oBACnE,CAAC;;QACH;uDAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR;QACF;+CAAG;QAAC;QAAO;KAAK;IAEhB,MAAM,UAAU,CAAC;QACf,MAAM,eAAe,CAAC,MAAc,CAAC;YACnC,sBAAsB;gBACpB,MAAM,SAAS,EAAE;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,OAAO,CAAC,MAAM,EAAE,IAAK;oBAClD,MAAM,UAAU,WAAW,OAAO,CAAC,EAAE;oBACrC,IAAI,QAAQ,CAAC,GAAG,KAAK;wBACnB,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,IAAI,QAAQ,CAAC,IAAI,GAAG;4BAClB,QAAQ,CAAC,GAAG;4BACZ;wBACF;wBACA,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;wBACxC,QAAQ,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;wBACxC,QAAQ,CAAC,IAAI,OAAO,KAAK,MAAM;wBAC/B,OAAO,IAAI,CAAC;oBACd;gBACF;gBACA,WAAW,OAAO,GAAG;gBACrB,MAAM,MAAM,UAAU,OAAO,EAAE,WAAW;gBAC1C,IAAI,KAAK;oBACP,IAAI,SAAS,CAAC,KAAK,GAAG,KAAK;oBAC3B,WAAW,OAAO,CAAC,OAAO,CAAC,CAAC;wBAC1B,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,OAAO,KAAK,EAAE,GAAG;wBAC3C,IAAI,IAAI,KAAK;4BACX,IAAI,SAAS;4BACb,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG;4BAClB,IAAI,SAAS,GAAG;4BAChB,IAAI,WAAW,GAAG;4BAClB,IAAI,MAAM;wBACZ;oBACF;gBACF;gBACA,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;oBACjC,aAAa,MAAM;gBACrB,OAAO;oBACL,SAAS;oBACT,aAAa;gBACf;YACF;QACF;QACA,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,aAAa,CAAC,UAAU;YAChD;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,UAAU;QAEd,aAAa;QACb;QAEA,MAAM,QAAQ,SAAS,OAAO,EAAE,SAAS;QACzC,IAAI,SAAS,SAAS,OAAO,EAAE;YAC7B,MAAM,OAAO,WAAW,OAAO,CAAC,MAAM,CACpC,CAAC,MAAM,UAAa,QAAQ,CAAC,GAAG,OAAO,QAAQ,CAAC,GAAG,MACnD;YAEF,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,UAAU;QAEd;QACA,YAAY,SAAS;IACvB;IACA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gGACA,yEACA,qCACA,SAAS,IACT,YAAY;QAEd,UAAU;;0BAEV,6LAAC;gBACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8GACA,CAAC,YAAY,cAAc;gBAE7B,KAAK;;;;;;0BAEP,6LAAC;gBACC,UAAU,CAAC;oBACT,IAAI,CAAC,aAAa,CAAC,UAAU;wBAC3B,SAAS,EAAE,MAAM,CAAC,KAAK;wBACvB,YAAY,SAAS;oBACvB;gBACF;gBACA,WAAW;gBACX,KAAK;gBACL,OAAO;gBACP,MAAK;gBACL,UAAU;gBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kJACA,0FACA,aAAa,oBACb,YAAY;;;;;;0BAIhB,6LAAC;gBACC,UAAU,CAAC,SAAS;gBACpB,MAAK;gBACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oJACA,QACI,+DACA;0BAGN,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;oBACf,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,WACA,QACI,qCACA;;sCAGN,6LAAC;4BAAK,QAAO;4BAAO,GAAE;4BAAgB,MAAK;;;;;;sCAC3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,GAAE;4BACF,SAAS;gCACP,iBAAiB;gCACjB,kBAAkB;4BACpB;4BACA,SAAS;gCACP,kBAAkB,QAAQ,IAAI;4BAChC;4BACA,YAAY;gCACV,UAAU;gCACV,MAAM;4BACR;;;;;;sCAEF,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,CAAC,uBACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BACP,GAAG;4BACH,SAAS;wBACX;wBAEA,SAAS;4BACP,GAAG;4BACH,SAAS;wBACX;wBACA,MAAM;4BACJ,GAAG,CAAC;4BACJ,SAAS;wBACX;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;wBACR;wBACA,WAAU;kCAET,YAAY,CAAC,mBAAmB;uBAf5B,CAAC,oBAAoB,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;;;;AAsB9D;GAnSgB;KAAA", "debugId": null}}, {"offset": {"line": 3534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/chat/ChatStartScreen.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useMemo } from \"react\";\r\nimport { PlaceholdersAndVanishInput } from \"@/components/ui/placeholders-and-vanish-input\";\r\nimport { usePageTitle } from '@/hooks/usePageTitle';\r\nimport { Plus } from 'lucide-react';\r\n\r\ninterface ChatStartScreenProps {\r\n  onSendMessage: (message: string) => void;\r\n}\r\n\r\nexport function ChatStartScreen({ onSendMessage }: ChatStartScreenProps) {\r\n  const [isSending, setIsSending] = useState(false);\r\n  const lastSubmitTime = useRef(0);\r\n  const RATE_LIMIT_MS = 1000; // Prevent submissions within 1 second of each other\r\n\r\n  // Set page title for new chat screen - memoized to prevent re-renders\r\n  const pageConfig = useMemo(() => ({\r\n    title: 'New Chat',\r\n    icon: Plus\r\n  }), []);\r\n\r\n  usePageTitle(pageConfig);\r\n\r\n  const placeholders = [\r\n    \"What would you like to know about your data?\",\r\n    \"Ask me to analyze your database records\",\r\n    \"Need help with data insights? Ask me anything\",\r\n    \"What trends should we explore in your data?\",\r\n    \"Looking for specific metrics or KPIs? Just ask\",\r\n  ];\r\n\r\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\r\n    e.preventDefault();\r\n    \r\n    // Rate limiting check\r\n    const now = Date.now();\r\n    if (now - lastSubmitTime.current < RATE_LIMIT_MS) {\r\n      console.log('Rate limit exceeded, ignoring submission');\r\n      return;\r\n    }\r\n    \r\n    // Prevent multiple submissions while one is in progress\r\n    if (isSending) {\r\n      console.log('Message already being sent, ignoring submission');\r\n      return;\r\n    }\r\n    \r\n    const input = e.currentTarget.querySelector('input');\r\n    if (input && input.value.trim()) {\r\n      setIsSending(true);\r\n      lastSubmitTime.current = now;\r\n      \r\n      try {\r\n        onSendMessage(input.value.trim());\r\n        // Clear the input after successful submission\r\n        input.value = '';\r\n      } catch (error) {\r\n        console.error('Error sending message:', error);\r\n        setIsSending(false);\r\n      }\r\n      \r\n      // Reset sending state after a delay to prevent rapid re-submission\r\n      setTimeout(() => {\r\n        setIsSending(false);\r\n      }, 2000);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    // Handle input changes if needed\r\n  };\r\n\r\n  return (\r\n    <div \r\n      className=\"fixed inset-0 flex flex-col overflow-hidden overflow-x-hidden z-50 bg-sidebar-bg\"\r\n      style={{ \r\n        left: 'var(--sidebar-width)', // Responsive sidebar width\r\n        top: 'var(--header-height)' // Responsive header height\r\n      }}\r\n    >\r\n      <div className=\"flex flex-1 items-center justify-center min-h-0 @lg/thread:items-end\">\r\n        <div className=\"max-w-2xl w-full mx-auto px-6\">\r\n          {/* Main Content */}\r\n          <div className=\"text-center mb-8\">\r\n            <div className=\"mb-6\">\r\n              <h1 className=\"text-sidebar-text-primary text-[28px] leading-[34px] font-semibold tracking-[0.38px] mb-3 motion-safe:transition-all duration-200 inline-flex min-h-10.5 items-baseline whitespace-pre-wrap opacity-100\">\r\n                Ready when you are.\r\n              </h1>\r\n              {isSending && (\r\n                <p className=\"text-sidebar-text-secondary text-[15px] leading-[18px] tracking-[-0.23px] motion-safe:transition-opacity duration-200\">\r\n                  Sending your message...\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Input Section */}\r\n          <div className=\"mb-6 motion-safe:transition-all duration-200\">\r\n            <PlaceholdersAndVanishInput\r\n              placeholders={placeholders}\r\n              onChange={handleChange}\r\n              onSubmit={handleSubmit}\r\n              disabled={isSending}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,gBAAgB,EAAE,aAAa,EAAwB;;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,gBAAgB,MAAM,oDAAoD;IAEhF,sEAAsE;IACtE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE,IAAM,CAAC;gBAChC,OAAO;gBACP,MAAM,qMAAA,CAAA,OAAI;YACZ,CAAC;8CAAG,EAAE;IAEN,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;IAEb,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,sBAAsB;QACtB,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,eAAe,OAAO,GAAG,eAAe;YAChD,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,wDAAwD;QACxD,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,MAAM,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC;QAC5C,IAAI,SAAS,MAAM,KAAK,CAAC,IAAI,IAAI;YAC/B,aAAa;YACb,eAAe,OAAO,GAAG;YAEzB,IAAI;gBACF,cAAc,MAAM,KAAK,CAAC,IAAI;gBAC9B,8CAA8C;gBAC9C,MAAM,KAAK,GAAG;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,aAAa;YACf;YAEA,mEAAmE;YACnE,WAAW;gBACT,aAAa;YACf,GAAG;QACL;IACF;IAEA,MAAM,eAAe,CAAC;IACpB,iCAAiC;IACnC;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,MAAM;YACN,KAAK,uBAAuB,2BAA2B;QACzD;kBAEA,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0M;;;;;;gCAGvN,2BACC,6LAAC;oCAAE,WAAU;8CAAwH;;;;;;;;;;;;;;;;;kCAQ3I,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qKAAA,CAAA,6BAA0B;4BACzB,cAAc;4BACd,UAAU;4BACV,UAAU;4BACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GAnGgB;;QAWd,+HAAA,CAAA,eAAY;;;KAXE", "debugId": null}}, {"offset": {"line": 3697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/services/tokenStreamingService.ts"], "sourcesContent": ["import axios from 'axios';\nimport { getApiBaseUrl, STORAGE_KEYS } from '@/lib/constants';\nimport { SSEEvent } from '@/types/streaming';\n\nexport interface StreamingOptions {\n  sessionId: string;\n  query: string;\n  outputFormat?: string;\n  onTokenReceived?: (token: string) => void;\n  onComplete?: (finalContent: string, completeResponse?: any) => void;\n  onError?: (error: string) => void;\n}\n\nexport class TokenStreamingService {\n  private eventSource: EventSource | null = null;\n  private accumulatedContent = '';\n  private isConnected = false;\n  private isCompleted = false; // Track if we've already completed to prevent duplicates\n\n  constructor() {\n    this.cleanup = this.cleanup.bind(this);\n  }\n\n  async startStreaming(options: StreamingOptions): Promise<void> {\n    const {\n      sessionId,\n      query,\n      outputFormat = 'excel',\n      onTokenReceived,\n      onComplete,\n      onError\n    } = options;\n\n    // Reset state\n    this.accumulatedContent = '';\n    this.isCompleted = false;\n    this.cleanup();\n\n    // Get auth token\n    const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\n    if (!token) {\n      const error = 'No authentication token found';\n      onError?.(error);\n      return;\n    }\n\n    try {\n      // Since EventSource only supports GET but backend expects POST with JSON body,\n      // we need to first make a POST request to initiate streaming, then connect to SSE\n      const baseUrl = getApiBaseUrl();\n\n      // First, make a POST request to start the streaming session using FormData\n      const formData = new FormData();\n      formData.append('query', query);\n      formData.append('output_format', outputFormat);\n      formData.append('session_id', sessionId);\n      formData.append('enable_token_streaming', 'true');\n\n      console.log('🔗 Making POST request to:', `${baseUrl}/ask/question`);\n      console.log('📤 Request data (FormData):');\n      console.log('  - query:', query);\n      console.log('  - output_format:', outputFormat);\n      console.log('  - session_id:', sessionId);\n      console.log('  - enable_token_streaming: true');\n\n      const initResponse = await fetch(`${baseUrl}/ask/question`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          // Don't set Content-Type - let browser set it with boundary for FormData\n        },\n        body: formData,\n      });\n\n      console.log('📥 Response status:', initResponse.status);\n      console.log('📥 Response headers:', Object.fromEntries(initResponse.headers.entries()));\n\n      if (!initResponse.ok) {\n        // Log the error response for debugging\n        let errorDetails = '';\n        try {\n          const errorBody = await initResponse.text();\n          console.error('❌ Error response body:', errorBody);\n          errorDetails = ` - ${errorBody}`;\n        } catch (e) {\n          console.error('❌ Could not read error response body');\n        }\n        throw new Error(`Failed to initiate streaming: ${initResponse.status} ${initResponse.statusText}${errorDetails}`);\n      }\n\n      // Check if the response indicates streaming is available\n      const contentType = initResponse.headers.get('content-type');\n      console.log('📋 Response content-type:', contentType);\n\n      if (contentType?.includes('text/event-stream')) {\n        // The response is already an SSE stream - handle it directly with fetch\n        console.log('🔄 Processing SSE stream from POST response...');\n        this.handleSSEResponse(initResponse, onTokenReceived, onComplete, onError);\n      } else {\n        // The response is JSON, check if it contains a streaming URL or session info\n        const responseData = await initResponse.json();\n        console.log('📄 JSON response received:', responseData);\n\n        if (responseData.streaming_url) {\n          // Backend provided a streaming URL\n          this.connectToSSE(responseData.streaming_url, onTokenReceived, onComplete, onError);\n        } else {\n          // No streaming available, return the regular response\n          const content = responseData.summary || responseData.message || responseData.content || 'No response available';\n          onComplete?.(content, responseData);\n        }\n      }\n\n    } catch (error) {\n      console.error('Error starting streaming connection:', error);\n      const errorMsg = error instanceof Error ? error.message : 'Failed to start streaming connection';\n      onError?.(errorMsg);\n    }\n  }\n\n  private async handleSSEResponse(\n    response: Response,\n    onTokenReceived?: (token: string) => void,\n    onComplete?: (finalContent: string, completeResponse?: any) => void,\n    onError?: (error: string) => void\n  ): Promise<void> {\n    if (!response.body) {\n      onError?.('No response body available for streaming');\n      return;\n    }\n\n    const reader = response.body.getReader();\n    const decoder = new TextDecoder();\n\n    try {\n      this.isConnected = true;\n      console.log('✅ SSE stream connected');\n\n      while (true) {\n        const { done, value } = await reader.read();\n\n        if (done) {\n          console.log('📡 SSE stream ended');\n          break;\n        }\n\n        const chunk = decoder.decode(value, { stream: true });\n        this.processSSEChunk(chunk, onTokenReceived, onComplete, onError);\n      }\n    } catch (error) {\n      console.error('❌ Error reading SSE stream:', error);\n      onError?.('Error reading streaming response');\n    } finally {\n      reader.releaseLock();\n      this.isConnected = false;\n    }\n  }\n\n  private connectToSSE(\n    url: string,\n    onTokenReceived?: (token: string) => void,\n    onComplete?: (finalContent: string, completeResponse?: any) => void,\n    onError?: (error: string) => void\n  ): void {\n    console.log('🔗 Connecting to SSE URL:', url);\n\n    this.eventSource = new EventSource(url);\n\n    this.eventSource.onopen = () => {\n      console.log('✅ SSE connection opened');\n      this.isConnected = true;\n    };\n\n    this.eventSource.onmessage = (event) => {\n      this.processSSEEvent(event.data, onTokenReceived, onComplete, onError);\n    };\n\n    this.eventSource.onerror = (error) => {\n      console.error('❌ SSE connection error:', error);\n      this.isConnected = false;\n\n      if (this.eventSource?.readyState === EventSource.CLOSED) {\n        onError?.('Connection to server lost');\n        this.cleanup();\n      }\n    };\n  }\n\n  private processSSEChunk(\n    chunk: string,\n    onTokenReceived?: (token: string) => void,\n    onComplete?: (finalContent: string, completeResponse?: any) => void,\n    onError?: (error: string) => void\n  ): void {\n    // Process SSE chunk data\n    const lines = chunk.split('\\n');\n\n    for (const line of lines) {\n      if (line.startsWith('data: ')) {\n        const eventData = line.substring(6);\n        this.processSSEEvent(eventData, onTokenReceived, onComplete, onError);\n      }\n    }\n  }\n\n  private processSSEEvent(\n    eventData: string,\n    onTokenReceived?: (token: string) => void,\n    onComplete?: (finalContent: string, completeResponse?: any) => void,\n    onError?: (error: string) => void\n  ): void {\n    try {\n      const data = JSON.parse(eventData);\n      console.log('📨 SSE event received:', data);\n\n      // Skip processing if already completed\n      if (this.isCompleted && ['token_complete', 'conversation_complete'].includes(data.type)) {\n        console.log('⏭️ Skipping duplicate completion event:', data.type);\n        return;\n      }\n\n      switch (data.type) {\n        case 'token_stream':\n          // Backend sends token directly in the event, not in data object\n          const token = data.token;\n\n          console.log('🔍 Raw token received:', token);\n\n          if (token && typeof token === 'string') {\n            console.log(`⏰ Processing token: \"${token}\"`);\n\n            // Accumulate the raw tokens for final content\n            this.accumulatedContent += token;\n\n            // Send the token directly to the UI for real-time display\n            onTokenReceived?.(token);\n          }\n          break;\n\n        case 'token_complete':\n          // Backend sends complete_response in data object\n          const completeResponse = data.data?.complete_response || data.data?.message;\n          const fullResponseData = data.data; // Pass the complete data object\n\n          console.log('🎯 Processing token_complete event');\n          console.log('Complete response:', completeResponse);\n          console.log('Full response data:', fullResponseData);\n\n          // Only process if we haven't completed yet and have a complete response\n          if (!this.isCompleted && completeResponse) {\n            console.log('✅ Using complete response from token_complete');\n            this.isCompleted = true;\n            onComplete?.(completeResponse, fullResponseData);\n            this.cleanup();\n          }\n          break;\n\n        case 'conversation_complete':\n          // Backend sends final message in data object\n          const finalMessage = data.data?.message;\n\n          console.log('🎯 Processing conversation_complete event');\n          console.log('Final message:', finalMessage);\n\n          // Only process if we haven't completed yet\n          if (!this.isCompleted) {\n            if (finalMessage) {\n              console.log('✅ Using final message from conversation_complete');\n              this.isCompleted = true;\n              onComplete?.(finalMessage);\n            } else if (this.accumulatedContent) {\n              console.log('✅ Using accumulated content from tokens');\n              this.isCompleted = true;\n              onComplete?.(this.accumulatedContent);\n            }\n            this.cleanup();\n          }\n          break;\n\n        case 'error':\n          const errorMsg = data.data?.error || data.error || 'Unknown streaming error';\n          onError?.(errorMsg);\n          this.cleanup();\n          break;\n\n        default:\n          // Ignore agent_status and agent_result events\n          if (!['agent_status', 'agent_result'].includes(data.type)) {\n            console.warn('Unknown SSE event type:', data.type);\n          }\n      }\n    } catch (parseError) {\n      console.error('Error parsing SSE event:', parseError);\n      console.error('Raw event data:', eventData);\n      // Don't treat parse errors as fatal - continue streaming\n    }\n  }\n\n  stopStreaming(): void {\n    this.cleanup();\n  }\n\n  private extractCleanToken(token: string): string | null {\n    console.log(`🔍 Extracting clean token from: \"${token}\"`);\n\n    // Skip JSON structure tokens that don't contain actual content\n    const skipTokens = [\n      '{\\n  \"',\n      '\\n  \"message',\n      'message\": \"',\n      '\",\\n}',\n      '.\"\\n}',\n      '{\\n  \"message\": \"',\n      '\"',\n      '\\n',\n      '  ',\n      '{',\n      '}',\n      '\": \"',\n    ];\n\n    // If token is just JSON structure, skip it\n    if (skipTokens.some(skipToken => token === skipToken)) {\n      console.log(`⏭️ Skipping JSON structure token: \"${token}\"`);\n      return null;\n    }\n\n    // If token contains JSON structure at the beginning, extract the content part\n    if (token.includes('\": \"')) {\n      const match = token.match(/\": \"(.+)/);\n      const extracted = match ? match[1] : null;\n      console.log(`📝 Extracted from JSON key-value: \"${extracted}\"`);\n      return extracted;\n    }\n\n    // If token ends with JSON structure, clean it\n    if (token.endsWith('\",') || token.endsWith('.\"\\n}') || token.endsWith('\"')) {\n      const cleaned = token.replace(/[\",\\n}]+$/, '');\n      console.log(`🧹 Cleaned trailing JSON: \"${cleaned}\"`);\n      return cleaned;\n    }\n\n    // If token starts with JSON structure, remove it\n    if (token.startsWith('{\\n  \"') || token.startsWith('\"')) {\n      const cleaned = token.replace(/^[{\\n\\s\"]+/, '');\n      console.log(`🧹 Cleaned leading JSON: \"${cleaned}\"`);\n      return cleaned || null;\n    }\n\n    // Return clean content tokens as-is\n    console.log(`✅ Clean content token: \"${token}\"`);\n    return token;\n  }\n\n  private cleanup(): void {\n    if (this.eventSource) {\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n    this.isConnected = false;\n    this.isCompleted = false;\n  }\n\n  getConnectionStatus(): boolean {\n    return this.isConnected;\n  }\n\n  getAccumulatedContent(): string {\n    return this.accumulatedContent;\n  }\n}\n\n// Export a singleton instance\nexport const tokenStreamingService = new TokenStreamingService();\n"], "names": [], "mappings": ";;;;AACA;AAAA;;AAYO,MAAM;IACH,cAAkC,KAAK;IACvC,qBAAqB,GAAG;IACxB,cAAc,MAAM;IACpB,cAAc,MAAM;IAE5B,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;IACvC;IAEA,MAAM,eAAe,OAAyB,EAAiB;QAC7D,MAAM,EACJ,SAAS,EACT,KAAK,EACL,eAAe,OAAO,EACtB,eAAe,EACf,UAAU,EACV,OAAO,EACR,GAAG;QAEJ,cAAc;QACd,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO;QAEZ,iBAAiB;QACjB,MAAM,QAAQ,aAAa,OAAO,CAAC,iIAAA,CAAA,eAAY,CAAC,YAAY;QAC5D,IAAI,CAAC,OAAO;YACV,MAAM,QAAQ;YACd,UAAU;YACV;QACF;QAEA,IAAI;YACF,+EAA+E;YAC/E,kFAAkF;YAClF,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD;YAE5B,2EAA2E;YAC3E,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YACzB,SAAS,MAAM,CAAC,iBAAiB;YACjC,SAAS,MAAM,CAAC,cAAc;YAC9B,SAAS,MAAM,CAAC,0BAA0B;YAE1C,QAAQ,GAAG,CAAC,8BAA8B,GAAG,QAAQ,aAAa,CAAC;YACnE,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,cAAc;YAC1B,QAAQ,GAAG,CAAC,sBAAsB;YAClC,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,QAAQ,GAAG,CAAC;YAEZ,MAAM,eAAe,MAAM,MAAM,GAAG,QAAQ,aAAa,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBAEpC;gBACA,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,uBAAuB,aAAa,MAAM;YACtD,QAAQ,GAAG,CAAC,wBAAwB,OAAO,WAAW,CAAC,aAAa,OAAO,CAAC,OAAO;YAEnF,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,uCAAuC;gBACvC,IAAI,eAAe;gBACnB,IAAI;oBACF,MAAM,YAAY,MAAM,aAAa,IAAI;oBACzC,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,eAAe,CAAC,GAAG,EAAE,WAAW;gBAClC,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC;gBAChB;gBACA,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE,aAAa,UAAU,GAAG,cAAc;YAClH;YAEA,yDAAyD;YACzD,MAAM,cAAc,aAAa,OAAO,CAAC,GAAG,CAAC;YAC7C,QAAQ,GAAG,CAAC,6BAA6B;YAEzC,IAAI,aAAa,SAAS,sBAAsB;gBAC9C,wEAAwE;gBACxE,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,YAAY;YACpE,OAAO;gBACL,6EAA6E;gBAC7E,MAAM,eAAe,MAAM,aAAa,IAAI;gBAC5C,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,IAAI,aAAa,aAAa,EAAE;oBAC9B,mCAAmC;oBACnC,IAAI,CAAC,YAAY,CAAC,aAAa,aAAa,EAAE,iBAAiB,YAAY;gBAC7E,OAAO;oBACL,sDAAsD;oBACtD,MAAM,UAAU,aAAa,OAAO,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,IAAI;oBACxF,aAAa,SAAS;gBACxB;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC1D,UAAU;QACZ;IACF;IAEA,MAAc,kBACZ,QAAkB,EAClB,eAAyC,EACzC,UAAmE,EACnE,OAAiC,EAClB;QACf,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,UAAU;YACV;QACF;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,SAAS;QACtC,MAAM,UAAU,IAAI;QAEpB,IAAI;YACF,IAAI,CAAC,WAAW,GAAG;YACnB,QAAQ,GAAG,CAAC;YAEZ,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBAEzC,IAAI,MAAM;oBACR,QAAQ,GAAG,CAAC;oBACZ;gBACF;gBAEA,MAAM,QAAQ,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBACnD,IAAI,CAAC,eAAe,CAAC,OAAO,iBAAiB,YAAY;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,UAAU;QACZ,SAAU;YACR,OAAO,WAAW;YAClB,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEQ,aACN,GAAW,EACX,eAAyC,EACzC,UAAmE,EACnE,OAAiC,EAC3B;QACN,QAAQ,GAAG,CAAC,6BAA6B;QAEzC,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY;QAEnC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;YACxB,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG;QACrB;QAEA,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,CAAC;YAC5B,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,EAAE,iBAAiB,YAAY;QAChE;QAEA,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,CAAC;YAC1B,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,WAAW,GAAG;YAEnB,IAAI,IAAI,CAAC,WAAW,EAAE,eAAe,YAAY,MAAM,EAAE;gBACvD,UAAU;gBACV,IAAI,CAAC,OAAO;YACd;QACF;IACF;IAEQ,gBACN,KAAa,EACb,eAAyC,EACzC,UAAmE,EACnE,OAAiC,EAC3B;QACN,yBAAyB;QACzB,MAAM,QAAQ,MAAM,KAAK,CAAC;QAE1B,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,UAAU,CAAC,WAAW;gBAC7B,MAAM,YAAY,KAAK,SAAS,CAAC;gBACjC,IAAI,CAAC,eAAe,CAAC,WAAW,iBAAiB,YAAY;YAC/D;QACF;IACF;IAEQ,gBACN,SAAiB,EACjB,eAAyC,EACzC,UAAmE,EACnE,OAAiC,EAC3B;QACN,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,QAAQ,GAAG,CAAC,0BAA0B;YAEtC,uCAAuC;YACvC,IAAI,IAAI,CAAC,WAAW,IAAI;gBAAC;gBAAkB;aAAwB,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACvF,QAAQ,GAAG,CAAC,2CAA2C,KAAK,IAAI;gBAChE;YACF;YAEA,OAAQ,KAAK,IAAI;gBACf,KAAK;oBACH,gEAAgE;oBAChE,MAAM,QAAQ,KAAK,KAAK;oBAExB,QAAQ,GAAG,CAAC,0BAA0B;oBAEtC,IAAI,SAAS,OAAO,UAAU,UAAU;wBACtC,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;wBAE5C,8CAA8C;wBAC9C,IAAI,CAAC,kBAAkB,IAAI;wBAE3B,0DAA0D;wBAC1D,kBAAkB;oBACpB;oBACA;gBAEF,KAAK;oBACH,iDAAiD;oBACjD,MAAM,mBAAmB,KAAK,IAAI,EAAE,qBAAqB,KAAK,IAAI,EAAE;oBACpE,MAAM,mBAAmB,KAAK,IAAI,EAAE,gCAAgC;oBAEpE,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,sBAAsB;oBAClC,QAAQ,GAAG,CAAC,uBAAuB;oBAEnC,wEAAwE;oBACxE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,kBAAkB;wBACzC,QAAQ,GAAG,CAAC;wBACZ,IAAI,CAAC,WAAW,GAAG;wBACnB,aAAa,kBAAkB;wBAC/B,IAAI,CAAC,OAAO;oBACd;oBACA;gBAEF,KAAK;oBACH,6CAA6C;oBAC7C,MAAM,eAAe,KAAK,IAAI,EAAE;oBAEhC,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,kBAAkB;oBAE9B,2CAA2C;oBAC3C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;wBACrB,IAAI,cAAc;4BAChB,QAAQ,GAAG,CAAC;4BACZ,IAAI,CAAC,WAAW,GAAG;4BACnB,aAAa;wBACf,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE;4BAClC,QAAQ,GAAG,CAAC;4BACZ,IAAI,CAAC,WAAW,GAAG;4BACnB,aAAa,IAAI,CAAC,kBAAkB;wBACtC;wBACA,IAAI,CAAC,OAAO;oBACd;oBACA;gBAEF,KAAK;oBACH,MAAM,WAAW,KAAK,IAAI,EAAE,SAAS,KAAK,KAAK,IAAI;oBACnD,UAAU;oBACV,IAAI,CAAC,OAAO;oBACZ;gBAEF;oBACE,8CAA8C;oBAC9C,IAAI,CAAC;wBAAC;wBAAgB;qBAAe,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;wBACzD,QAAQ,IAAI,CAAC,2BAA2B,KAAK,IAAI;oBACnD;YACJ;QACF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,QAAQ,KAAK,CAAC,mBAAmB;QACjC,yDAAyD;QAC3D;IACF;IAEA,gBAAsB;QACpB,IAAI,CAAC,OAAO;IACd;IAEQ,kBAAkB,KAAa,EAAiB;QACtD,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;QAExD,+DAA+D;QAC/D,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,2CAA2C;QAC3C,IAAI,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,YAAY;YACrD,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,MAAM,CAAC,CAAC;YAC1D,OAAO;QACT;QAEA,8EAA8E;QAC9E,IAAI,MAAM,QAAQ,CAAC,SAAS;YAC1B,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,MAAM,YAAY,QAAQ,KAAK,CAAC,EAAE,GAAG;YACrC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,UAAU,CAAC,CAAC;YAC9D,OAAO;QACT;QAEA,8CAA8C;QAC9C,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,MAAM;YAC1E,MAAM,UAAU,MAAM,OAAO,CAAC,aAAa;YAC3C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC;YACpD,OAAO;QACT;QAEA,iDAAiD;QACjD,IAAI,MAAM,UAAU,CAAC,aAAa,MAAM,UAAU,CAAC,MAAM;YACvD,MAAM,UAAU,MAAM,OAAO,CAAC,cAAc;YAC5C,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;YACnD,OAAO,WAAW;QACpB;QAEA,oCAAoC;QACpC,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;QAC/C,OAAO;IACT;IAEQ,UAAgB;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK;YACtB,IAAI,CAAC,WAAW,GAAG;QACrB;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA,sBAA+B;QAC7B,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,wBAAgC;QAC9B,OAAO,IAAI,CAAC,kBAAkB;IAChC;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 3998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/MarkdownRenderer.tsx"], "sourcesContent": ["import React, { useMemo, useRef, useCallback } from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport rehypeRaw from 'rehype-raw';\nimport { cn } from '@/lib/utils';\n\ninterface MarkdownRendererProps {\n  content: string;\n  className?: string;\n  isStreaming?: boolean; // Add this prop to optimize for streaming\n}\n\n// Function to preprocess content and convert custom formatting to proper markdown\nconst preprocessContent = (content: string): string => {\n  if (!content) return '';\n\n  let processed = content;\n\n  // The backend sends content with ### as section separators, not proper markdown headers\n  // We need to convert this format to proper markdown\n\n  // Step 1: Split content by ### to get sections\n  const sections = processed.split('###').map(section => section.trim()).filter(Boolean);\n\n  const processedSections = sections.map((section, index) => {\n    let cleanSection = section.trim();\n\n    // Check if section starts with an emoji - make it a header\n    const emojiMatch = cleanSection.match(/^([🏆📊📋💡🔍📈📉💰🎯⚡🚀💎🔥⭐🎉🎊🏅🎖️💹💲💸💵💴💶💷🔢📝📄📃📑🔎📖📚📓📕📗📘📙📒📰🗞️])\\s*([\\s\\S]+)$/);\n\n    if (emojiMatch) {\n      const [, emoji, rest] = emojiMatch;\n      // Split the rest into title and content\n      const lines = rest.split('\\n');\n      const title = lines[0].trim();\n      const content = lines.slice(1).join('\\n').trim();\n\n      if (content) {\n        cleanSection = `## ${emoji} ${title}\\n\\n${content}`;\n      } else {\n        cleanSection = `## ${emoji} ${title}`;\n      }\n    }\n\n    return cleanSection;\n  });\n\n  // Join sections with proper spacing\n  processed = processedSections.join('\\n\\n');\n\n  // Step 2: Convert bullet points (•) to proper markdown lists\n  processed = processed.replace(/•\\s*/g, '- ');\n\n  // Step 3: Fix numbered lists - ensure they're on new lines\n  processed = processed.replace(/(\\d+\\.\\s*\\*\\*[^*]+\\*\\*)/g, '\\n$1');\n\n  // NEW STEP: Ensure lists start on a new line when they immediately follow a header ending with a colon\n  // e.g., \"## 📊 Key Observations: -\" => \"## 📊 Key Observations:\\n\\n-\"\n  processed = processed.replace(/(:)\\s*(-\\s+)/g, '$1\\n\\n$2');\n\n  // NEW STEP: Convert heading followed directly by markdown table into separate header + table\n  // e.g., \"Top 10 Movies | Rank | Title\" => \"### Top 10 Movies\\n\\n| Rank | Title\"\n  processed = processed.replace(/^([^|\\n#]+?)\\s+\\|\\s+(.+)$/gm, '### $1\\n\\n| $2');\n\n  // NEW STEP: Ensure bullets that appear inline (\" - **Bold\" pattern) start on new lines for proper list rendering\n  processed = processed.replace(/\\s-\\s\\*\\*/g, '\\n- **');\n\n  // Step 4: Clean up spacing\n  processed = processed.replace(/\\n{3,}/g, '\\n\\n');\n  processed = processed.replace(/^\\n+/, ''); // Remove leading newlines\n\n  return processed.trim();\n};\n\n// Create a debounced preprocessing function to prevent excessive re-processing during streaming\nconst createDebouncedPreprocessor = () => {\n  let timeoutId: NodeJS.Timeout | null = null;\n  let lastContent = '';\n  let lastResult = '';\n  \n  return (content: string, isStreaming: boolean = false): string => {\n    // If content hasn't changed, return cached result immediately\n    if (content === lastContent) {\n      return lastResult;\n    }\n    \n    // For non-streaming or short content, process immediately\n    if (!isStreaming || content.length < 100) {\n      const result = preprocessContent(content);\n      lastContent = content;\n      lastResult = result;\n      return result;\n    }\n    \n    // For streaming content, use the last processed result for small changes\n    // Only reprocess if the content has grown significantly or streaming has stopped\n    const contentDiff = Math.abs(content.length - lastContent.length);\n    if (contentDiff < 50 && isStreaming) {\n      // Return a simple concatenation for small streaming updates\n      return lastResult + content.slice(lastContent.length);\n    }\n    \n    // Reprocess for significant changes\n    const result = preprocessContent(content);\n    lastContent = content;\n    lastResult = result;\n    return result;\n  };\n};\n\n// Create a single instance to maintain state across re-renders\nconst debouncedPreprocessor = createDebouncedPreprocessor();\n\nexport const MarkdownRenderer: React.FC<MarkdownRendererProps> = React.memo(({\n  content,\n  className = '',\n  isStreaming = false,\n}) => {\n  const lastProcessedContentRef = useRef<string>('');\n  const lastProcessedResultRef = useRef<string>('');\n  const lastLoggedContentRef = useRef<string>('');\n  const renderCountRef = useRef(0);\n  \n  // Optimized preprocessing with streaming awareness\n  const processedContent = useMemo(() => {\n    renderCountRef.current++;\n    \n    // Prevent excessive re-renders during streaming\n    if (isStreaming && renderCountRef.current > 10 && content === lastProcessedContentRef.current) {\n      return lastProcessedResultRef.current;\n    }\n    \n    // If content hasn't changed significantly, return cached result\n    if (content === lastProcessedContentRef.current) {\n      return lastProcessedResultRef.current;\n    }\n    \n    // Use debounced preprocessor for streaming content\n    const result = debouncedPreprocessor(content, isStreaming);\n    \n    // Cache only if content is complete or has grown significantly\n    if (!isStreaming || content.length - lastProcessedContentRef.current.length > 100) {\n      lastProcessedContentRef.current = content;\n      lastProcessedResultRef.current = result;\n    }\n    \n    return result;\n  }, [content, isStreaming]);\n\n  // Throttled logging to prevent console spam during streaming\n  useMemo(() => {\n    if (process.env.NODE_ENV === 'development' && content && content.includes('###')) {\n      // Only log if content actually changed significantly and not during active streaming\n      if (!isStreaming && content !== lastLoggedContentRef.current && Math.abs(content.length - lastLoggedContentRef.current.length) > 50) {\n        console.log('🔍 MarkdownRenderer Debug (non-streaming):');\n        console.log('Original content length:', content.length);\n        console.log('Processed content length:', processedContent.length);\n        lastLoggedContentRef.current = content;\n      }\n    }\n  }, [content, processedContent, isStreaming]);\n\n  // Reset render count when content changes significantly\n  const prevContentLengthRef = useRef(0);\n  useMemo(() => {\n    if (Math.abs(content.length - prevContentLengthRef.current) > 200) {\n      renderCountRef.current = 0;\n      prevContentLengthRef.current = content.length;\n    }\n  }, [content.length]);\n\n  // Memoize the ReactMarkdown component to prevent unnecessary re-renders\n  const markdownComponent = useMemo(() => (\n    <ReactMarkdown\n      remarkPlugins={[remarkGfm]}\n      rehypePlugins={[rehypeRaw]}\n      components={{\n        // Headers\n        h1: ({ children }) => (\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6\">\n            {children}\n          </h1>\n        ),\n        h2: ({ children }) => (\n          <h2 className=\"text-xl font-semibold text-blue-600 dark:text-blue-400 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4 mt-6\">\n            {children}\n          </h2>\n        ),\n        h3: ({ children }) => (\n          <h3 className=\"text-lg font-medium text-gray-700 dark:text-gray-300 mb-3 mt-5 border-l-4 border-blue-300 dark:border-blue-600 pl-3\">\n            {children}\n          </h3>\n        ),\n        h4: ({ children }) => (\n          <h4 className=\"text-base font-medium text-gray-700 dark:text-gray-300 mb-2 mt-2\">\n            {children}\n          </h4>\n        ),\n        h5: ({ children }) => (\n          <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2\">\n            {children}\n          </h5>\n        ),\n        h6: ({ children }) => (\n          <h6 className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1 mt-2\">\n            {children}\n          </h6>\n        ),\n        \n        // Text formatting - Enhanced for analytical content\n        strong: ({ children }) => (\n          <strong className=\"font-semibold text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 px-1 py-0.5 rounded\">\n            {children}\n          </strong>\n        ),\n        em: ({ children }) => (\n          <em className=\"italic text-gray-800 dark:text-gray-200\">\n            {children}\n          </em>\n        ),\n        \n        // Lists - Enhanced for analytical content\n        ul: ({ children }) => (\n          <ul className=\"list-disc pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200\">\n            {children}\n          </ul>\n        ),\n        ol: ({ children }) => (\n          <ol className=\"list-decimal pl-6 my-4 space-y-2 text-gray-800 dark:text-gray-200\">\n            {children}\n          </ol>\n        ),\n        li: ({ children }) => (\n          <li className=\"leading-relaxed py-0.5 text-sm\">\n            {children}\n          </li>\n        ),\n        \n        // Paragraphs - Enhanced spacing for analytical content\n        p: ({ children }) => (\n          <p className=\"mb-4 leading-relaxed text-gray-800 dark:text-gray-200 text-sm\">\n            {children}\n          </p>\n        ),\n        \n        // Code\n        // @ts-ignore - react-markdown passes an \"inline\" prop that is not in the intrinsic HTML <code> element props\n        code: ({ children, inline }) => \n          inline ? (\n            <code className=\"bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono text-gray-900 dark:text-gray-100\">\n              {children}\n            </code>\n          ) : (\n            <pre className=\"bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-3 my-3 overflow-x-auto\">\n              <code className=\"text-sm font-mono text-gray-900 dark:text-gray-100\">\n                {children}\n              </code>\n            </pre>\n          ),\n        \n        // Tables\n        table: ({ children }) => (\n          <div className=\"overflow-x-auto my-3\">\n            <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n              {children}\n            </table>\n          </div>\n        ),\n        th: ({ children }) => (\n          <th className=\"border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 px-3 py-2 text-left font-semibold text-gray-900 dark:text-gray-100\">\n            {children}\n          </th>\n        ),\n        td: ({ children }) => (\n          <td className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-800 dark:text-gray-200\">\n            {children}\n          </td>\n        ),\n        \n        // Blockquotes\n        blockquote: ({ children }) => (\n          <blockquote className=\"border-l-4 border-blue-500 dark:border-blue-400 pl-4 my-3 italic text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 py-2 rounded-r\">\n            {children}\n          </blockquote>\n        ),\n        \n        // Links\n        a: ({ children, href }) => (\n          <a \n            href={href} \n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n          >\n            {children}\n          </a>\n        ),\n        \n        // Horizontal rule\n        hr: () => (\n          <hr className=\"my-4 border-gray-300 dark:border-gray-600\" />\n        ),\n      }}\n    >\n      {processedContent || 'No content available'}\n    </ReactMarkdown>\n  ), [processedContent]);\n\n  return (\n    <div className={cn(\"markdown-content\", className)}>\n      {markdownComponent}\n    </div>\n  );\n});\n\nMarkdownRenderer.displayName = 'MarkdownRenderer';\n\nexport default MarkdownRenderer;\n"], "names": [], "mappings": ";;;;AAuJQ;;AAvJR;AACA;AACA;AACA;AACA;AAAA;;;;;;;;AAQA,kFAAkF;AAClF,MAAM,oBAAoB,CAAC;IACzB,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI,YAAY;IAEhB,wFAAwF;IACxF,oDAAoD;IAEpD,+CAA+C;IAC/C,MAAM,WAAW,UAAU,KAAK,CAAC,OAAO,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI,IAAI,MAAM,CAAC;IAE9E,MAAM,oBAAoB,SAAS,GAAG,CAAC,CAAC,SAAS;QAC/C,IAAI,eAAe,QAAQ,IAAI;QAE/B,2DAA2D;QAC3D,MAAM,aAAa,aAAa,KAAK,CAAC;QAEtC,IAAI,YAAY;YACd,MAAM,GAAG,OAAO,KAAK,GAAG;YACxB,wCAAwC;YACxC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,IAAI;YAC3B,MAAM,UAAU,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI;YAE9C,IAAI,SAAS;gBACX,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,MAAM,IAAI,EAAE,SAAS;YACrD,OAAO;gBACL,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO;YACvC;QACF;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,YAAY,kBAAkB,IAAI,CAAC;IAEnC,6DAA6D;IAC7D,YAAY,UAAU,OAAO,CAAC,SAAS;IAEvC,2DAA2D;IAC3D,YAAY,UAAU,OAAO,CAAC,4BAA4B;IAE1D,uGAAuG;IACvG,sEAAsE;IACtE,YAAY,UAAU,OAAO,CAAC,iBAAiB;IAE/C,6FAA6F;IAC7F,gFAAgF;IAChF,YAAY,UAAU,OAAO,CAAC,+BAA+B;IAE7D,iHAAiH;IACjH,YAAY,UAAU,OAAO,CAAC,cAAc;IAE5C,2BAA2B;IAC3B,YAAY,UAAU,OAAO,CAAC,WAAW;IACzC,YAAY,UAAU,OAAO,CAAC,QAAQ,KAAK,0BAA0B;IAErE,OAAO,UAAU,IAAI;AACvB;AAEA,gGAAgG;AAChG,MAAM,8BAA8B;IAClC,IAAI,YAAmC;IACvC,IAAI,cAAc;IAClB,IAAI,aAAa;IAEjB,OAAO,CAAC,SAAiB,cAAuB,KAAK;QACnD,8DAA8D;QAC9D,IAAI,YAAY,aAAa;YAC3B,OAAO;QACT;QAEA,0DAA0D;QAC1D,IAAI,CAAC,eAAe,QAAQ,MAAM,GAAG,KAAK;YACxC,MAAM,SAAS,kBAAkB;YACjC,cAAc;YACd,aAAa;YACb,OAAO;QACT;QAEA,yEAAyE;QACzE,iFAAiF;QACjF,MAAM,cAAc,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,YAAY,MAAM;QAChE,IAAI,cAAc,MAAM,aAAa;YACnC,4DAA4D;YAC5D,OAAO,aAAa,QAAQ,KAAK,CAAC,YAAY,MAAM;QACtD;QAEA,oCAAoC;QACpC,MAAM,SAAS,kBAAkB;QACjC,cAAc;QACd,aAAa;QACb,OAAO;IACT;AACF;AAEA,+DAA+D;AAC/D,MAAM,wBAAwB;AAEvB,MAAM,iCAAoD,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC,CAAC,EAC3E,OAAO,EACP,YAAY,EAAE,EACd,cAAc,KAAK,EACpB;;IACC,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAC/C,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAC9C,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAC5C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,mDAAmD;IACnD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;YAC/B,eAAe,OAAO;YAEtB,gDAAgD;YAChD,IAAI,eAAe,eAAe,OAAO,GAAG,MAAM,YAAY,wBAAwB,OAAO,EAAE;gBAC7F,OAAO,uBAAuB,OAAO;YACvC;YAEA,gEAAgE;YAChE,IAAI,YAAY,wBAAwB,OAAO,EAAE;gBAC/C,OAAO,uBAAuB,OAAO;YACvC;YAEA,mDAAmD;YACnD,MAAM,SAAS,sBAAsB,SAAS;YAE9C,+DAA+D;YAC/D,IAAI,CAAC,eAAe,QAAQ,MAAM,GAAG,wBAAwB,OAAO,CAAC,MAAM,GAAG,KAAK;gBACjF,wBAAwB,OAAO,GAAG;gBAClC,uBAAuB,OAAO,GAAG;YACnC;YAEA,OAAO;QACT;qDAAG;QAAC;QAAS;KAAY;IAEzB,6DAA6D;IAC7D,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE;YACN,IAAI,oDAAyB,iBAAiB,WAAW,QAAQ,QAAQ,CAAC,QAAQ;gBAChF,qFAAqF;gBACrF,IAAI,CAAC,eAAe,YAAY,qBAAqB,OAAO,IAAI,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,qBAAqB,OAAO,CAAC,MAAM,IAAI,IAAI;oBACnI,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,4BAA4B,QAAQ,MAAM;oBACtD,QAAQ,GAAG,CAAC,6BAA6B,iBAAiB,MAAM;oBAChE,qBAAqB,OAAO,GAAG;gBACjC;YACF;QACF;mCAAG;QAAC;QAAS;QAAkB;KAAY;IAE3C,wDAAwD;IACxD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACpC,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE;YACN,IAAI,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,qBAAqB,OAAO,IAAI,KAAK;gBACjE,eAAe,OAAO,GAAG;gBACzB,qBAAqB,OAAO,GAAG,QAAQ,MAAM;YAC/C;QACF;mCAAG;QAAC,QAAQ,MAAM;KAAC;IAEnB,wEAAwE;IACxE,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE,kBAChC,6LAAC,2LAAA,CAAA,UAAa;gBACZ,eAAe;oBAAC,gJAAA,CAAA,UAAS;iBAAC;gBAC1B,eAAe;oBAAC,gJAAA,CAAA,UAAS;iBAAC;gBAC1B,YAAY;oBACV,UAAU;oBACV,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAIL,oDAAoD;oBACpD,MAAM;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;gCAAO,WAAU;0CACf;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAIL,0CAA0C;oBAC1C,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAIL,uDAAuD;oBACvD,CAAC;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;gCAAE,WAAU;0CACV;;;;;;;oBAIL,OAAO;oBACP,6GAA6G;oBAC7G,IAAI;uEAAE,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,GACzB,uBACE,6LAAC;gCAAK,WAAU;0CACb;;;;;uDAGH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;oBAKT,SAAS;oBACT,KAAK;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;8CACd;;;;;;;;;;;;oBAIP,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAGL,EAAE;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;;oBAIL,cAAc;oBACd,UAAU;uEAAE,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;gCAAW,WAAU;0CACnB;;;;;;;oBAIL,QAAQ;oBACR,CAAC;uEAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpB,6LAAC;gCACC,MAAM;gCACN,WAAU;gCACV,QAAO;gCACP,KAAI;0CAEH;;;;;;;oBAIL,kBAAkB;oBAClB,EAAE;uEAAE,kBACF,6LAAC;gCAAG,WAAU;;;;;;;gBAElB;0BAEC,oBAAoB;;;;;;sDAEtB;QAAC;KAAiB;IAErB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;kBACpC;;;;;;AAGP;;AAEA,iBAAiB,WAAW,GAAG;uCAEhB", "debugId": null}}, {"offset": {"line": 4423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/chat/AnalyticalContentRenderer.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/MarkdownRenderer';\nimport { BarChart3 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface AnalyticalContentRendererProps {\n  content: string;\n  className?: string;\n  isStreaming?: boolean;\n}\n\n// Function to detect analytical content patterns\nconst detectAnalyticalSections = (content: string) => {\n  const sections = {\n    hasExecutiveSummary: /## Executive Summary/i.test(content),\n    hasKeyInsights: /### Key Insights/i.test(content),\n    hasBusinessImplications: /### Business Implications/i.test(content),\n    hasRecommendations: /### Recommendation/i.test(content),\n    hasStatisticalOverview: /### Statistical Overview/i.test(content),\n    hasNumberedResults: /^\\d+\\.\\s+\\*\\*/.test(content.replace(/\\n/g, ' ')),\n    hasBulletPoints: /^\\*\\s+\\*\\*/.test(content.replace(/\\n/g, ' ')),\n  };\n  \n  return sections;\n};\n\n// Enhanced preprocessing for analytical content\nconst preprocessAnalyticalContent = (content: string): string => {\n  if (!content) return '';\n\n  let processed = content;\n  const sections = detectAnalyticalSections(content);\n\n  // Add icons and enhanced styling for key sections\n  if (sections.hasExecutiveSummary) {\n    processed = processed.replace(\n      /## Executive Summary/gi,\n      '## 📊 Executive Summary'\n    );\n  }\n\n  if (sections.hasKeyInsights) {\n    processed = processed.replace(\n      /### Key Insights:/gi,\n      '### 💡 Key Insights:'\n    );\n  }\n\n  if (sections.hasBusinessImplications) {\n    processed = processed.replace(\n      /### Business Implications:/gi,\n      '### 🎯 Business Implications:'\n    );\n  }\n\n  if (sections.hasRecommendations) {\n    processed = processed.replace(\n      /### Recommendation:/gi,\n      '### 🚀 Recommendation:'\n    );\n  }\n\n  if (sections.hasStatisticalOverview) {\n    processed = processed.replace(\n      /### Statistical Overview/gi,\n      '### 📈 Statistical Overview'\n    );\n  }\n\n  return processed;\n};\n\nexport const AnalyticalContentRenderer: React.FC<AnalyticalContentRendererProps> = React.memo(({\n  content,\n  className = '',\n  isStreaming = false,\n}) => {\n  // Memoize expensive operations to prevent infinite re-renders\n  const sections = useMemo(() => detectAnalyticalSections(content), [content]);\n  const processedContent = useMemo(() => preprocessAnalyticalContent(content), [content]);\n  \n  // Determine if this is analytical content\n  const isAnalyticalContent = useMemo(() => Object.values(sections).some(Boolean), [sections]);\n\n  return (\n    <div className={cn(\n      \"analytical-content\",\n      className\n    )}>\n      <MarkdownRenderer\n        content={processedContent}\n        isStreaming={isStreaming}\n        className={cn(\n          isAnalyticalContent && \"analytical-markdown\",\n          className\n        )}\n      />\n    </div>\n  );\n});\n\nAnalyticalContentRenderer.displayName = 'AnalyticalContentRenderer';\n\nexport default AnalyticalContentRenderer;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AAAA;;;;;;AAQA,iDAAiD;AACjD,MAAM,2BAA2B,CAAC;IAChC,MAAM,WAAW;QACf,qBAAqB,wBAAwB,IAAI,CAAC;QAClD,gBAAgB,oBAAoB,IAAI,CAAC;QACzC,yBAAyB,6BAA6B,IAAI,CAAC;QAC3D,oBAAoB,sBAAsB,IAAI,CAAC;QAC/C,wBAAwB,4BAA4B,IAAI,CAAC;QACzD,oBAAoB,gBAAgB,IAAI,CAAC,QAAQ,OAAO,CAAC,OAAO;QAChE,iBAAiB,aAAa,IAAI,CAAC,QAAQ,OAAO,CAAC,OAAO;IAC5D;IAEA,OAAO;AACT;AAEA,gDAAgD;AAChD,MAAM,8BAA8B,CAAC;IACnC,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI,YAAY;IAChB,MAAM,WAAW,yBAAyB;IAE1C,kDAAkD;IAClD,IAAI,SAAS,mBAAmB,EAAE;QAChC,YAAY,UAAU,OAAO,CAC3B,0BACA;IAEJ;IAEA,IAAI,SAAS,cAAc,EAAE;QAC3B,YAAY,UAAU,OAAO,CAC3B,uBACA;IAEJ;IAEA,IAAI,SAAS,uBAAuB,EAAE;QACpC,YAAY,UAAU,OAAO,CAC3B,gCACA;IAEJ;IAEA,IAAI,SAAS,kBAAkB,EAAE;QAC/B,YAAY,UAAU,OAAO,CAC3B,yBACA;IAEJ;IAEA,IAAI,SAAS,sBAAsB,EAAE;QACnC,YAAY,UAAU,OAAO,CAC3B,8BACA;IAEJ;IAEA,OAAO;AACT;AAEO,MAAM,0CAAsE,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC,CAAC,EAC7F,OAAO,EACP,YAAY,EAAE,EACd,cAAc,KAAK,EACpB;;IACC,8DAA8D;IAC9D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE,IAAM,yBAAyB;sDAAU;QAAC;KAAQ;IAC3E,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+DAAE,IAAM,4BAA4B;8DAAU;QAAC;KAAQ;IAEtF,0CAA0C;IAC1C,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kEAAE,IAAM,OAAO,MAAM,CAAC,UAAU,IAAI,CAAC;iEAAU;QAAC;KAAS;IAE3F,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,sBACA;kBAEA,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;YACf,SAAS;YACT,aAAa;YACb,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,uBAAuB,uBACvB;;;;;;;;;;;AAKV;;AAEA,0BAA0B,WAAW,GAAG;uCAEzB", "debugId": null}}, {"offset": {"line": 4524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/chat/StreamingMessage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useMemo } from 'react';\nimport { TypewriterConfig } from '@/types/streaming';\nimport AnalyticalContentRenderer from './AnalyticalContentRenderer';\n\ninterface StreamingMessageProps {\n  content: string;\n  isStreaming: boolean;\n  className?: string;\n  typewriterConfig?: Partial<TypewriterConfig>;\n}\n\nconst defaultTypewriterConfig: TypewriterConfig = {\n  speed: 0, // No delay for real-time streaming\n  enableCursor: true,\n  cursorChar: '|',\n};\n\nexport const StreamingMessage: React.FC<StreamingMessageProps> = React.memo(({\n  content,\n  isStreaming,\n  className = '',\n  typewriterConfig = {},\n}) => {\n  const [displayedContent, setDisplayedContent] = useState('');\n  const [showCursor, setShowCursor] = useState(false);\n  const cursorIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  const [lastContentLength, setLastContentLength] = useState(0);\n\n  // Memoize config to prevent unnecessary re-renders\n  const config = useMemo(() => ({ ...defaultTypewriterConfig, ...typewriterConfig }), [typewriterConfig]);\n\n  // Real-time content display (no typewriter delay for streaming)\n  useEffect(() => {\n    if (config.speed === 0) {\n      // For real-time streaming, show content immediately\n      setDisplayedContent(content);\n      setLastContentLength(content.length);\n    } else {\n      // Traditional typewriter effect for non-streaming content\n      if (!content) {\n        setDisplayedContent('');\n        setLastContentLength(0);\n        return;\n      }\n\n      // If content is shorter than current display (e.g., reset), reset immediately\n      if (content.length < displayedContent.length) {\n        setDisplayedContent(content);\n        setLastContentLength(content.length);\n        return;\n      }\n\n      // If we're already displaying the full content, no need to animate\n      if (displayedContent === content && !isStreaming) {\n        return;\n      }\n\n      let currentIndex = displayedContent.length;\n      const interval = setInterval(() => {\n        if (currentIndex < content.length) {\n          currentIndex++;\n          setDisplayedContent(content.slice(0, currentIndex));\n          setLastContentLength(currentIndex);\n        } else {\n          clearInterval(interval);\n        }\n      }, config.speed);\n\n      return () => clearInterval(interval);\n    }\n  }, [content, config.speed, isStreaming, displayedContent.length]);\n\n  // Memoize the rendered content to prevent unnecessary re-renders\n  const memoizedAnalyticalContent = useMemo(() => (\n    <AnalyticalContentRenderer\n      content={displayedContent}\n      isStreaming={isStreaming}\n      className=\"inline\"\n    />\n  ), [displayedContent, isStreaming]);\n\n  // Cursor blinking effect\n  useEffect(() => {\n    if (!config.enableCursor || !isStreaming) {\n      setShowCursor(false);\n      if (cursorIntervalRef.current) {\n        clearInterval(cursorIntervalRef.current);\n        cursorIntervalRef.current = null;\n      }\n      return;\n    }\n\n    // Show cursor immediately when streaming starts\n    setShowCursor(true);\n\n    // Start cursor blinking\n    cursorIntervalRef.current = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, 600);\n\n    return () => {\n      if (cursorIntervalRef.current) {\n        clearInterval(cursorIntervalRef.current);\n        cursorIntervalRef.current = null;\n      }\n    };\n  }, [isStreaming, config.enableCursor]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (cursorIntervalRef.current) {\n        clearInterval(cursorIntervalRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <div className={`streaming-message ${className}`}>\n      <div className=\"relative\">\n        <div className=\"text-sm\">\n          {memoizedAnalyticalContent}\n          {isStreaming && config.enableCursor && (\n            <span\n              className={`inline-block ml-0.5 text-blue-500 font-bold transition-opacity duration-150 ${\n                showCursor ? 'opacity-100' : 'opacity-0'\n              }`}\n              style={{ width: '2px' }}\n            >\n              {config.cursorChar}\n            </span>\n          )}\n        </div>\n        {/* Subtle glow effect when streaming */}\n        {isStreaming && (\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-50/20 to-transparent rounded pointer-events-none animate-pulse\" />\n        )}\n      </div>\n    </div>\n  );\n});\n\nStreamingMessage.displayName = 'StreamingMessage';\n\nexport default StreamingMessage;\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;;AASA,MAAM,0BAA4C;IAChD,OAAO;IACP,cAAc;IACd,YAAY;AACd;AAEO,MAAM,iCAAoD,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC,CAAC,EAC3E,OAAO,EACP,WAAW,EACX,YAAY,EAAE,EACd,mBAAmB,CAAC,CAAC,EACtB;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACxD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,mDAAmD;IACnD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE,IAAM,CAAC;gBAAE,GAAG,uBAAuB;gBAAE,GAAG,gBAAgB;YAAC,CAAC;2CAAG;QAAC;KAAiB;IAEtG,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,OAAO,KAAK,KAAK,GAAG;gBACtB,oDAAoD;gBACpD,oBAAoB;gBACpB,qBAAqB,QAAQ,MAAM;YACrC,OAAO;gBACL,0DAA0D;gBAC1D,IAAI,CAAC,SAAS;oBACZ,oBAAoB;oBACpB,qBAAqB;oBACrB;gBACF;gBAEA,8EAA8E;gBAC9E,IAAI,QAAQ,MAAM,GAAG,iBAAiB,MAAM,EAAE;oBAC5C,oBAAoB;oBACpB,qBAAqB,QAAQ,MAAM;oBACnC;gBACF;gBAEA,mEAAmE;gBACnE,IAAI,qBAAqB,WAAW,CAAC,aAAa;oBAChD;gBACF;gBAEA,IAAI,eAAe,iBAAiB,MAAM;gBAC1C,MAAM,WAAW;2DAAY;wBAC3B,IAAI,eAAe,QAAQ,MAAM,EAAE;4BACjC;4BACA,oBAAoB,QAAQ,KAAK,CAAC,GAAG;4BACrC,qBAAqB;wBACvB,OAAO;4BACL,cAAc;wBAChB;oBACF;0DAAG,OAAO,KAAK;gBAEf;kDAAO,IAAM,cAAc;;YAC7B;QACF;qCAAG;QAAC;QAAS,OAAO,KAAK;QAAE;QAAa,iBAAiB,MAAM;KAAC;IAEhE,iEAAiE;IACjE,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+DAAE,kBACxC,6LAAC,sKAAA,CAAA,UAAyB;gBACxB,SAAS;gBACT,aAAa;gBACb,WAAU;;;;;;8DAEX;QAAC;QAAkB;KAAY;IAElC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,OAAO,YAAY,IAAI,CAAC,aAAa;gBACxC,cAAc;gBACd,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,cAAc,kBAAkB,OAAO;oBACvC,kBAAkB,OAAO,GAAG;gBAC9B;gBACA;YACF;YAEA,gDAAgD;YAChD,cAAc;YAEd,wBAAwB;YACxB,kBAAkB,OAAO,GAAG;8CAAY;oBACtC;sDAAc,CAAA,OAAQ,CAAC;;gBACzB;6CAAG;YAEH;8CAAO;oBACL,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,cAAc,kBAAkB,OAAO;wBACvC,kBAAkB,OAAO,GAAG;oBAC9B;gBACF;;QACF;qCAAG;QAAC;QAAa,OAAO,YAAY;KAAC;IAErC,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;8CAAO;oBACL,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,cAAc,kBAAkB,OAAO;oBACzC;gBACF;;QACF;qCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;kBAC9C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;wBACZ;wBACA,eAAe,OAAO,YAAY,kBACjC,6LAAC;4BACC,WAAW,CAAC,4EAA4E,EACtF,aAAa,gBAAgB,aAC7B;4BACF,OAAO;gCAAE,OAAO;4BAAM;sCAErB,OAAO,UAAU;;;;;;;;;;;;gBAKvB,6BACC,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;;AAEA,iBAAiB,WAAW,GAAG;uCAEhB", "debugId": null}}, {"offset": {"line": 4723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n} "], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/chat/DataTable.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Download, ExternalLink, Table as TableIcon, AlertCircle, Loader2 } from 'lucide-react';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n  TableCaption,\r\n} from \"@/components/ui/table\";\r\nimport * as XLSX from 'xlsx';\r\n\r\ninterface OutputFile {\r\n  database_name: string;\r\n  file_path: string;\r\n  format: string;\r\n}\r\n\r\ninterface DataTableProps {\r\n  outputFiles: OutputFile[];\r\n  className?: string;\r\n}\r\n\r\ninterface TableData {\r\n  headers: string[];\r\n  rows: string[][];\r\n  fileName: string;\r\n  format: string;\r\n}\r\n\r\nconst DataTable: React.FC<DataTableProps> = ({ outputFiles, className = \"\" }) => {\r\n  const [tableData, setTableData] = useState<TableData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isExpanded, setIsExpanded] = useState(true); // Auto-expand by default\r\n\r\n  // Parse CSV text into structured data\r\n  const parseCSV = (csvText: string): { headers: string[]; rows: string[][] } => {\r\n    const lines = csvText.trim().split('\\n');\r\n    if (lines.length === 0) return { headers: [], rows: [] };\r\n\r\n    // Function to parse a CSV line respecting quoted fields\r\n    const parseCSVLine = (line: string): string[] => {\r\n      const result: string[] = [];\r\n      let current = '';\r\n      let inQuotes = false;\r\n\r\n      for (let i = 0; i < line.length; i++) {\r\n        const char = line[i];\r\n\r\n        if (char === '\"') {\r\n          if (inQuotes && line[i + 1] === '\"') {\r\n            // Handle escaped quotes\r\n            current += '\"';\r\n            i++; // Skip the next quote\r\n          } else {\r\n            // Toggle quote state\r\n            inQuotes = !inQuotes;\r\n          }\r\n        } else if (char === ',' && !inQuotes) {\r\n          // End of field\r\n          result.push(current.trim());\r\n          current = '';\r\n        } else {\r\n          current += char;\r\n        }\r\n      }\r\n\r\n      // Add the last field\r\n      result.push(current.trim());\r\n      return result;\r\n    };\r\n\r\n    const headers = parseCSVLine(lines[0]);\r\n    const rows = lines.slice(1).map(line => parseCSVLine(line));\r\n\r\n    return { headers, rows };\r\n  };\r\n\r\n  // Parse Excel file into structured data\r\n  const parseExcel = (arrayBuffer: ArrayBuffer): { headers: string[]; rows: string[][] } => {\r\n    try {\r\n      const workbook = XLSX.read(arrayBuffer, { type: 'array' });\r\n      const firstSheetName = workbook.SheetNames[0];\r\n      const worksheet = workbook.Sheets[firstSheetName];\r\n\r\n      // Convert to JSON format first\r\n      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];\r\n\r\n      if (jsonData.length === 0) return { headers: [], rows: [] };\r\n\r\n      const headers = jsonData[0] || [];\r\n      const rows = jsonData.slice(1);\r\n\r\n      return { headers, rows };\r\n    } catch (err) {\r\n      console.error('Error parsing Excel file:', err);\r\n      throw new Error('Failed to parse Excel file');\r\n    }\r\n  };\r\n\r\n  // Fetch and parse file data from S3 URL\r\n  const fetchFileData = async (url: string, format: string, fileName: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      let parsedData: { headers: string[]; rows: string[][] };\r\n\r\n      if (format.toLowerCase() === 'csv') {\r\n        const csvText = await response.text();\r\n        parsedData = parseCSV(csvText);\r\n      } else if (format.toLowerCase() === 'xlsx' || format.toLowerCase() === 'xls') {\r\n        const arrayBuffer = await response.arrayBuffer();\r\n        parsedData = parseExcel(arrayBuffer);\r\n      } else {\r\n        throw new Error(`Unsupported file format: ${format}`);\r\n      }\r\n\r\n      setTableData({\r\n        headers: parsedData.headers,\r\n        rows: parsedData.rows,\r\n        fileName,\r\n        format\r\n      });\r\n    } catch (err) {\r\n      console.error('Failed to fetch file data:', err);\r\n      setError(err instanceof Error ? err.message : 'Failed to load data');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Auto-fetch data when component mounts or outputFiles change\r\n  useEffect(() => {\r\n    if (outputFiles && outputFiles.length > 0) {\r\n      const primaryFile = outputFiles[0];\r\n      const fileName = primaryFile.file_path.split('/').pop() || `data.${primaryFile.format}`;\r\n      fetchFileData(primaryFile.file_path, primaryFile.format, fileName);\r\n    }\r\n  }, [outputFiles]);\r\n\r\n  // Download file from S3 URL\r\n  const downloadFile = (url: string, fileName: string) => {\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    link.target = '_blank';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n  if (!outputFiles || outputFiles.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const primaryFile = outputFiles[0]; // Use the first file as primary\r\n  const fileName = primaryFile.file_path.split('/').pop() || `data.${primaryFile.format}`;\r\n\r\n  return (\r\n    <div className={`border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50/30 dark:bg-blue-900/10 p-4 mt-4 ${className}`}>\r\n      {/* Header with file info and actions */}\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\r\n            <TableIcon className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" />\r\n          </div>\r\n          <div>\r\n            <h4 className=\"font-semibold text-sm text-blue-900 dark:text-blue-100\">Query Results Data</h4>\r\n            <p className=\"text-xs text-blue-700 dark:text-blue-300\">\r\n              {primaryFile.database_name} • {primaryFile.format.toUpperCase()} • {tableData?.rows.length || 0} rows\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            onClick={() => downloadFile(primaryFile.file_path, fileName)}\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n            className=\"text-xs\"\r\n          >\r\n            <Download className=\"w-3 h-3 mr-1\" />\r\n            Download\r\n          </Button>\r\n\r\n          <Button\r\n            onClick={() => window.open(primaryFile.file_path, '_blank')}\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n            className=\"text-xs\"\r\n          >\r\n            <ExternalLink className=\"w-3 h-3 mr-1\" />\r\n            Open\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Loading state */}\r\n      {isLoading && (\r\n        <div className=\"flex items-center justify-center py-8 text-gray-600 dark:text-gray-400\">\r\n          <Loader2 className=\"w-4 h-4 animate-spin mr-2\" />\r\n          <span className=\"text-sm\">Loading table data...</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Error state */}\r\n      {error && !isLoading && (\r\n        <div className=\"flex items-center gap-2 text-red-600 dark:text-red-400 text-sm mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800\">\r\n          <AlertCircle className=\"w-4 h-4 flex-shrink-0\" />\r\n          <div>\r\n            <p className=\"font-medium\">Failed to load table data</p>\r\n            <p className=\"text-xs mt-1\">{error}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Table display */}\r\n      {isExpanded && tableData && !error && !isLoading && (\r\n        <div className=\"max-h-96 overflow-auto border border-blue-200 dark:border-blue-700 rounded-lg bg-white dark:bg-gray-900\">\r\n          <Table>\r\n            <TableCaption className=\"text-xs text-blue-600 dark:text-blue-400 font-medium py-2\">\r\n              Showing {Math.min(tableData.rows.length, 100)} of {tableData.rows.length} rows • {tableData.fileName}\r\n            </TableCaption>\r\n            <TableHeader>\r\n              <TableRow>\r\n                {tableData.headers.map((header, index) => (\r\n                  <TableHead key={index} className=\"text-xs font-medium\">\r\n                    {header || `Column ${index + 1}`}\r\n                  </TableHead>\r\n                ))}\r\n              </TableRow>\r\n            </TableHeader>\r\n            <TableBody>\r\n              {tableData.rows.slice(0, 100).map((row, rowIndex) => (\r\n                <TableRow key={rowIndex}>\r\n                  {tableData.headers.map((_, cellIndex) => (\r\n                    <TableCell key={cellIndex} className=\"text-xs\">\r\n                      {row[cellIndex] || ''}\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableBody>\r\n          </Table>\r\n\r\n          {tableData.rows.length > 100 && (\r\n            <div className=\"p-2 text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 border-t\">\r\n              Showing first 100 rows of {tableData.rows.length} total rows. Download the full file to see all data.\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Expand/Collapse button */}\r\n      {tableData && !isLoading && (\r\n        <div className=\"mt-2 flex justify-center\">\r\n          <Button\r\n            onClick={() => setIsExpanded(!isExpanded)}\r\n            size=\"sm\"\r\n            variant=\"ghost\"\r\n            className=\"text-xs\"\r\n          >\r\n            {isExpanded ? 'Collapse Table' : 'Expand Table'}\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Collapsed state preview */}\r\n      {!isExpanded && tableData && !isLoading && (\r\n        <div className=\"text-center py-4 text-gray-600 dark:text-gray-400\">\r\n          <TableIcon className=\"w-6 h-6 mx-auto mb-2 opacity-50\" />\r\n          <p className=\"text-sm\">\r\n            Table with {tableData.rows.length} rows and {tableData.headers.length} columns\r\n          </p>\r\n          <p className=\"text-xs mt-1\">Click \"Expand Table\" to view data</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DataTable; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AASA;;;;;;;;AAoBA,MAAM,YAAsC,CAAC,EAAE,WAAW,EAAE,YAAY,EAAE,EAAE;;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,yBAAyB;IAE7E,sCAAsC;IACtC,MAAM,WAAW,CAAC;QAChB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QACnC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;YAAE,SAAS,EAAE;YAAE,MAAM,EAAE;QAAC;QAEvD,wDAAwD;QACxD,MAAM,eAAe,CAAC;YACpB,MAAM,SAAmB,EAAE;YAC3B,IAAI,UAAU;YACd,IAAI,WAAW;YAEf,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,OAAO,IAAI,CAAC,EAAE;gBAEpB,IAAI,SAAS,KAAK;oBAChB,IAAI,YAAY,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK;wBACnC,wBAAwB;wBACxB,WAAW;wBACX,KAAK,sBAAsB;oBAC7B,OAAO;wBACL,qBAAqB;wBACrB,WAAW,CAAC;oBACd;gBACF,OAAO,IAAI,SAAS,OAAO,CAAC,UAAU;oBACpC,eAAe;oBACf,OAAO,IAAI,CAAC,QAAQ,IAAI;oBACxB,UAAU;gBACZ,OAAO;oBACL,WAAW;gBACb;YACF;YAEA,qBAAqB;YACrB,OAAO,IAAI,CAAC,QAAQ,IAAI;YACxB,OAAO;QACT;QAEA,MAAM,UAAU,aAAa,KAAK,CAAC,EAAE;QACrC,MAAM,OAAO,MAAM,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA,OAAQ,aAAa;QAErD,OAAO;YAAE;YAAS;QAAK;IACzB;IAEA,wCAAwC;IACxC,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,OAAS,AAAD,EAAE,aAAa;gBAAE,MAAM;YAAQ;YACxD,MAAM,iBAAiB,SAAS,UAAU,CAAC,EAAE;YAC7C,MAAM,YAAY,SAAS,MAAM,CAAC,eAAe;YAEjD,+BAA+B;YAC/B,MAAM,WAAW,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC,WAAW;gBAAE,QAAQ;YAAE;YAEjE,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;gBAAE,SAAS,EAAE;gBAAE,MAAM,EAAE;YAAC;YAE1D,MAAM,UAAU,QAAQ,CAAC,EAAE,IAAI,EAAE;YACjC,MAAM,OAAO,SAAS,KAAK,CAAC;YAE5B,OAAO;gBAAE;gBAAS;YAAK;QACzB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,wCAAwC;IACxC,MAAM,gBAAgB,OAAO,KAAa,QAAgB;QACxD,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YACnF;YAEA,IAAI;YAEJ,IAAI,OAAO,WAAW,OAAO,OAAO;gBAClC,MAAM,UAAU,MAAM,SAAS,IAAI;gBACnC,aAAa,SAAS;YACxB,OAAO,IAAI,OAAO,WAAW,OAAO,UAAU,OAAO,WAAW,OAAO,OAAO;gBAC5E,MAAM,cAAc,MAAM,SAAS,WAAW;gBAC9C,aAAa,WAAW;YAC1B,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,QAAQ;YACtD;YAEA,aAAa;gBACX,SAAS,WAAW,OAAO;gBAC3B,MAAM,WAAW,IAAI;gBACrB;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;gBACzC,MAAM,cAAc,WAAW,CAAC,EAAE;gBAClC,MAAM,WAAW,YAAY,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,YAAY,MAAM,EAAE;gBACvF,cAAc,YAAY,SAAS,EAAE,YAAY,MAAM,EAAE;YAC3D;QACF;8BAAG;QAAC;KAAY;IAEhB,4BAA4B;IAC5B,MAAM,eAAe,CAAC,KAAa;QACjC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,MAAM,GAAG;QACd,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;IAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;QAC5C,OAAO;IACT;IAEA,MAAM,cAAc,WAAW,CAAC,EAAE,EAAE,gCAAgC;IACpE,MAAM,WAAW,YAAY,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,YAAY,MAAM,EAAE;IAEvF,qBACE,6LAAC;QAAI,WAAW,CAAC,kGAAkG,EAAE,WAAW;;0BAE9H,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyD;;;;;;kDACvE,6LAAC;wCAAE,WAAU;;4CACV,YAAY,aAAa;4CAAC;4CAAI,YAAY,MAAM,CAAC,WAAW;4CAAG;4CAAI,WAAW,KAAK,UAAU;4CAAE;;;;;;;;;;;;;;;;;;;kCAKtG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,aAAa,YAAY,SAAS,EAAE;gCACnD,MAAK;gCACL,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIvC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,YAAY,SAAS,EAAE;gCAClD,MAAK;gCACL,SAAQ;gCACR,WAAU;;kDAEV,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAO9C,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAK7B,SAAS,CAAC,2BACT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAc;;;;;;0CAC3B,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;YAMlC,cAAc,aAAa,CAAC,SAAS,CAAC,2BACrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;;0CACJ,6LAAC,oIAAA,CAAA,eAAY;gCAAC,WAAU;;oCAA4D;oCACzE,KAAK,GAAG,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE;oCAAK;oCAAK,UAAU,IAAI,CAAC,MAAM;oCAAC;oCAAS,UAAU,QAAQ;;;;;;;0CAEtG,6LAAC,oIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;8CACN,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC9B,6LAAC,oIAAA,CAAA,YAAS;4CAAa,WAAU;sDAC9B,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAG;2CADlB;;;;;;;;;;;;;;;0CAMtB,6LAAC,oIAAA,CAAA,YAAS;0CACP,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,KAAK,yBACtC,6LAAC,oIAAA,CAAA,WAAQ;kDACN,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,0BACzB,6LAAC,oIAAA,CAAA,YAAS;gDAAiB,WAAU;0DAClC,GAAG,CAAC,UAAU,IAAI;+CADL;;;;;uCAFL;;;;;;;;;;;;;;;;oBAWpB,UAAU,IAAI,CAAC,MAAM,GAAG,qBACvB,6LAAC;wBAAI,WAAU;;4BAAqF;4BACvE,UAAU,IAAI,CAAC,MAAM;4BAAC;;;;;;;;;;;;;YAOxD,aAAa,CAAC,2BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,IAAM,cAAc,CAAC;oBAC9B,MAAK;oBACL,SAAQ;oBACR,WAAU;8BAET,aAAa,mBAAmB;;;;;;;;;;;YAMtC,CAAC,cAAc,aAAa,CAAC,2BAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAE,WAAU;;4BAAU;4BACT,UAAU,IAAI,CAAC,MAAM;4BAAC;4BAAW,UAAU,OAAO,CAAC,MAAM;4BAAC;;;;;;;kCAExE,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;;;;;;;AAKtC;GAhQM;KAAA;uCAkQS", "debugId": null}}, {"offset": {"line": 5356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/chat/Chat.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';\r\n// import { useApi } from '@/providers/ApiContext';\r\nimport { useChatHistory } from '@/providers/ChatHistoryContext';\r\nimport { useRouter } from 'next/navigation';\r\nimport { usePageTitle } from '@/hooks/usePageTitle';\r\nimport { MessageCircle, Plus } from 'lucide-react';\r\n\r\n\r\nimport { MessageInput } from \"@/components/ui/message-input\";\r\nimport { ChatStartScreen } from './ChatStartScreen';\r\nimport { tokenStreamingService } from '@/lib/services/tokenStreamingService';\r\nimport StreamingMessage from './StreamingMessage';\r\nimport DataTable from './DataTable';\r\nimport AnalyticalContentRenderer from './AnalyticalContentRenderer';\r\n\r\ninterface Message {\r\n  role: 'user' | 'agent';\r\n  content: string;\r\n  timestamp?: Date;\r\n  isStreaming?: boolean;\r\n  outputFiles?: Array<{\r\n    database_name: string;\r\n    file_path: string;\r\n    format: string;\r\n  }>;\r\n  sqlQueries?: Record<string, string>;\r\n}\r\n\r\ninterface ChatProps {\r\n  chatId?: string;\r\n}\r\n\r\n// Constants\r\nconst RATE_LIMIT_MS = 1000; // Prevent messages within 1 second of each other\r\nconst processedFirstMessageChats = new Set<string>();\r\n\r\nconst Chat: React.FC<ChatProps> = ({ chatId }) => {\r\n  // State\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [attachedFiles, setAttachedFiles] = useState<Array<{ name: string; type: string }>>([]);\r\n  const [streamingMessage, setStreamingMessage] = useState<Message | null>(null);\r\n  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(null);\r\n  const [chatNotFound, setChatNotFound] = useState(false);\r\n  const [isSendingMessage, setIsSendingMessage] = useState(false);\r\n  \r\n  // Refs\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const tokenQueueRef = useRef<string[]>([]);\r\n  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const lastMessageTime = useRef(0);\r\n  const isSendingRef = useRef(false);\r\n  const processedFirstMessageRef = useRef(false);\r\n  const completionGuardRef = useRef<string | null>(null);\r\n  const {\r\n    chatHistory,\r\n    activeChat,\r\n    chatMessages,\r\n    isLoadingHistory,\r\n    isLoadingChats,\r\n    pendingFirstMessage,\r\n    setPendingFirstMessage,\r\n    setActiveChat,\r\n    addMessageToChat,\r\n    addChat,\r\n    refreshChatList,\r\n  } = useChatHistory();\r\n  const router = useRouter();\r\n\r\n  // Messages for the current chat\r\n  const messages = activeChat ? chatMessages[activeChat.session_id] || [] : [];\r\n\r\n  // Memoize page title configuration to prevent infinite re-renders\r\n  const pageConfig = useMemo(() => ({\r\n    title: activeChat \r\n      ? `Data Chat ${activeChat.id.startsWith('chat_sess_') ? activeChat.id.slice(9, 17) : activeChat.id.slice(0, 8)}` \r\n      : 'New Chat',\r\n    icon: activeChat ? MessageCircle : Plus\r\n  }), [activeChat]);\r\n\r\n  // Set page title based on chat state\r\n  usePageTitle(pageConfig);\r\n\r\n  // Set active chat when chatId changes - Enhanced for page refresh handling\r\n  useEffect(() => {\r\n    console.log('Chat component useEffect triggered with chatId:', chatId);\r\n    console.log('Current activeChat:', activeChat?.id);\r\n    console.log('Chat history loaded:', chatHistory.length > 0);\r\n    console.log('Is loading chats:', isLoadingChats);\r\n\r\n    // If no chatId (new chat scenario), clear active chat\r\n    if (!chatId) {\r\n      if (activeChat) {\r\n        console.log('Clearing active chat for new chat scenario');\r\n        setActiveChat(null);\r\n      }\r\n      return;\r\n    }\r\n\r\n    // If the chatId matches the current active chat, no need to reload\r\n    // This prevents clearing the activeChat when URL changes after creating a new chat\r\n    if (activeChat && activeChat.id === chatId) {\r\n      console.log('Chat is already active, no action needed');\r\n      return;\r\n    }\r\n\r\n    // Reset chat not found state when trying to load a new chat\r\n    setChatNotFound(false);\r\n\r\n    // Check if this is a newly created chat that exists in local state but not backend yet\r\n    // Look for the chat in the current chat history first\r\n    const existingChatInHistory = chatHistory.find(chat => chat.id === chatId);\r\n    if (existingChatInHistory) {\r\n      console.log('Found chat in local history, setting as active:', existingChatInHistory.id);\r\n      setActiveChat(existingChatInHistory);\r\n      return;\r\n    }\r\n\r\n    // Don't try to load from backend if we're still loading chats\r\n    // This prevents unnecessary backend calls during initial load\r\n    if (isLoadingChats) {\r\n      console.log('Still loading chats, waiting...');\r\n      return;\r\n    }\r\n\r\n    // IMPORTANT: Only mark as not found if we have actually loaded chat history\r\n    // On page refresh, chatHistory is initially empty but that doesn't mean the chat doesn't exist\r\n    // We should only mark as not found if we have loaded the chat list and the chat is still not there\r\n    if (chatHistory.length === 0) {\r\n      console.log('Chat history not loaded yet, waiting for backend data...');\r\n      return;\r\n    }\r\n\r\n    // Now we can safely mark as not found since we have loaded chat history and chat is not there\r\n    console.log('Chat not found in loaded history, marking as not found:', chatId);\r\n    setChatNotFound(true);\r\n  }, [chatId, activeChat?.id, isLoadingChats, chatHistory.length]);\r\n\r\n  // Process pending first message after chat creation and navigation\r\n  useEffect(() => {\r\n    // Prevent duplicate processing in React strict mode\r\n    if (processedFirstMessageRef.current) {\r\n      return;\r\n    }\r\n\r\n    // Existing diagnostic logs\r\n    console.log('🔍 pendingFirstMessage useEffect triggered:', {\r\n      pendingFirstMessage: !!pendingFirstMessage,\r\n      activeChat: !!activeChat,\r\n      activeChatId: activeChat?.id,\r\n      chatId,\r\n      isLoading,\r\n      chatIdMatches: activeChat?.id === chatId\r\n    });\r\n    \r\n    // Wait for all conditions: pending message, active chat set, chatId matches, and they're the same chat\r\n    if (pendingFirstMessage && activeChat && chatId && activeChat.id === chatId && !processedFirstMessageChats.has(chatId)) {\r\n      // Mark as processed to avoid running twice under Strict-Mode\r\n      processedFirstMessageRef.current = true;\r\n      processedFirstMessageChats.add(chatId);\r\n\r\n      console.log('✅ Processing pending first message after navigation:', pendingFirstMessage);\r\n      \r\n      // Set loading state\r\n      setIsLoading(true);\r\n      console.log('🔄 pendingFirstMessage: Set isLoading to true');\r\n      \r\n      // Clear any existing streaming message and create new streaming ID\r\n      setStreamingMessage(null);\r\n      const streamingId = `welcome_stream_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\r\n      setCurrentStreamingId(streamingId);\r\n      \r\n      // Reset completion guard for new message\r\n      completionGuardRef.current = null;\r\n      \r\n      // Clear the pending message to prevent reprocessing\r\n      const messageToProcess = pendingFirstMessage;\r\n      setPendingFirstMessage(null);\r\n\r\n      // Add completion guard to prevent duplicate processing\r\n      let hasCompleted = false;\r\n\r\n      // Start streaming\r\n      const startStreaming = async () => {\r\n        try {\r\n          await tokenStreamingService.startStreaming({\r\n            sessionId: activeChat.session_id,\r\n            query: messageToProcess,\r\n            outputFormat: 'excel',\r\n            onTokenReceived: (token) => {\r\n              console.log('🔤 Welcome message token received:', token);\r\n              setIsLoading(false);\r\n              // Prevent processing the same token multiple times\r\n              if (!token || typeof token !== 'string') {\r\n                return;\r\n              }\r\n              addTokenToQueue(token);\r\n            },\r\n            onComplete: async (finalContent, completeResponse) => {\r\n              // Always turn off the typing indicator as soon as we know the stream ended.\r\n              // This guarantees the blue dots disappear even if this completion event\r\n              // belongs to an older (superseded) request.\r\n              setIsLoading(false);\r\n\r\n              // Enhanced guard against duplicate completion calls using content hash\r\n              const contentHash = `${finalContent}_${Date.now()}`;\r\n              if (hasCompleted || completionGuardRef.current === contentHash) {\r\n                console.log('⏭️ Skipping duplicate onComplete call');\r\n                return;\r\n              }\r\n              hasCompleted = true;\r\n              completionGuardRef.current = contentHash;\r\n              \r\n              console.log('✅ Welcome message streaming complete:', finalContent);\r\n              console.log('📊 Complete response data:', completeResponse);\r\n\r\n              // Clear any remaining timeout and queue\r\n              if (streamingTimeoutRef.current) {\r\n                clearTimeout(streamingTimeoutRef.current);\r\n                streamingTimeoutRef.current = null;\r\n              }\r\n              tokenQueueRef.current = [];\r\n\r\n              // Add the final message to chat with structured data\r\n              const finalMessage: Message = {\r\n                role: 'agent',\r\n                content: finalContent,\r\n                timestamp: new Date(),\r\n                isStreaming: false,\r\n                outputFiles: completeResponse?.output_files || [],\r\n                sqlQueries: completeResponse?.sql_queries || {},\r\n              };\r\n              addMessageToChat(activeChat.session_id, finalMessage);\r\n              setStreamingMessage(null);\r\n              setCurrentStreamingId(null);\r\n\r\n              // Refresh chat list to get updated title\r\n              setTimeout(async () => {\r\n                await refreshChatList();\r\n              }, 500);\r\n            },\r\n            onError: (error) => {\r\n              console.error('Streaming failed:', error);\r\n              \r\n              // Always ensure cleanup happens\r\n              setIsLoading(false);\r\n              \r\n              // Clear any remaining timeout and queue\r\n              if (streamingTimeoutRef.current) {\r\n                clearTimeout(streamingTimeoutRef.current);\r\n                streamingTimeoutRef.current = null;\r\n              }\r\n              tokenQueueRef.current = [];\r\n              \r\n              // Show error message if we have an active chat\r\n              if (activeChat) {\r\n                const errorMessage: Message = {\r\n                  role: 'agent',\r\n                  content: 'Sorry, there was an error processing your request. Please try again.',\r\n                  timestamp: new Date()\r\n                };\r\n                addMessageToChat(activeChat.session_id, errorMessage);\r\n              }\r\n              setStreamingMessage(null);\r\n              setCurrentStreamingId(null);\r\n            },\r\n          });\r\n        } catch (streamingError) {\r\n          console.error('Failed to start streaming for first message:', streamingError);\r\n          \r\n          // Always ensure cleanup happens\r\n          setIsLoading(false);\r\n          \r\n          // Clear any remaining timeout and queue\r\n          if (streamingTimeoutRef.current) {\r\n            clearTimeout(streamingTimeoutRef.current);\r\n            streamingTimeoutRef.current = null;\r\n          }\r\n          tokenQueueRef.current = [];\r\n          \r\n          // Show error message if we have an active chat\r\n          if (activeChat) {\r\n            const errorMessage: Message = {\r\n              role: 'agent',\r\n              content: 'Sorry, there was an error processing your request. Please try again.',\r\n              timestamp: new Date()\r\n            };\r\n            addMessageToChat(activeChat.session_id, errorMessage);\r\n          }\r\n          setStreamingMessage(null);\r\n          setCurrentStreamingId(null);\r\n        }\r\n      };\r\n\r\n      startStreaming();\r\n    }\r\n  }, [pendingFirstMessage, activeChat, chatId]); // Keep the same dependencies\r\n\r\n  // Reset the processedFirstMessageRef when chatId changes (e.g., user starts a brand-new chat)\r\n  useEffect(() => {\r\n    // Reset ref for new chatId\r\n    processedFirstMessageRef.current = false;\r\n  }, [chatId]);\r\n\r\n  // Cleanup streaming service on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      tokenStreamingService.stopStreaming();\r\n      if (streamingTimeoutRef.current) {\r\n        clearTimeout(streamingTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  // Configuration for token streaming display speed\r\n  const STREAMING_CONFIG = {\r\n    // Delay between tokens in milliseconds - adjust this to control reading speed\r\n    // 400ms provides a comfortable reading pace (about 150 WPM)\r\n    TOKEN_DELAY: 400,\r\n    // Minimum delay for very short tokens (like single characters or punctuation)\r\n    MIN_DELAY: 200,\r\n    // Maximum delay for very long tokens\r\n    MAX_DELAY: 600,\r\n  };\r\n\r\n  // Function to calculate dynamic delay based on token length\r\n  const calculateTokenDelay = (token: string): number => {\r\n    const baseDelay = STREAMING_CONFIG.TOKEN_DELAY;\r\n    const tokenLength = token.trim().length;\r\n\r\n    // Shorter tokens (punctuation, single chars) get shorter delays\r\n    if (tokenLength <= 2) {\r\n      return STREAMING_CONFIG.MIN_DELAY;\r\n    }\r\n\r\n    // Longer tokens get slightly longer delays for better readability\r\n    if (tokenLength > 8) {\r\n      return STREAMING_CONFIG.MAX_DELAY;\r\n    }\r\n\r\n    // Standard delay for normal-length tokens\r\n    return baseDelay;\r\n  };\r\n\r\n  // Function to handle throttled token streaming display\r\n  const processTokenQueue = () => {\r\n    // If the queue is empty, clear any lingering timeout and exit early.\r\n    if (tokenQueueRef.current.length === 0) {\r\n      if (streamingTimeoutRef.current) {\r\n        clearTimeout(streamingTimeoutRef.current);\r\n        streamingTimeoutRef.current = null;\r\n      }\r\n      return;\r\n    }\r\n\r\n    const token = tokenQueueRef.current.shift();\r\n    if (token && token.trim()) {\r\n      setStreamingMessage(prev => {\r\n        if (!prev) {\r\n          return {\r\n            role: 'agent',\r\n            content: token,\r\n            timestamp: new Date(),\r\n            isStreaming: true,\r\n          };\r\n        }\r\n        // Only update if the content actually changes\r\n        const newContent = prev.content + token;\r\n        if (newContent === prev.content) {\r\n          return prev; // No change, return same object to prevent re-render\r\n        }\r\n        return {\r\n          ...prev,\r\n          content: newContent,\r\n        };\r\n      });\r\n\r\n      // Continue processing queue if there are more tokens\r\n      if (tokenQueueRef.current.length > 0) {\r\n        const delay = calculateTokenDelay(token);\r\n        streamingTimeoutRef.current = setTimeout(processTokenQueue, delay);\r\n      } else {\r\n        // Queue is now empty, clear the timeout reference so that future\r\n        // streaming sessions can start processing immediately.\r\n        if (streamingTimeoutRef.current) {\r\n          clearTimeout(streamingTimeoutRef.current);\r\n          streamingTimeoutRef.current = null;\r\n        }\r\n      }\r\n    } else {\r\n      // If token is empty but queue has more items, continue processing\r\n      if (tokenQueueRef.current.length > 0) {\r\n        streamingTimeoutRef.current = setTimeout(processTokenQueue, 50); // Short delay for empty tokens\r\n      }\r\n    }\r\n  };\r\n\r\n  const addTokenToQueue = (token: string) => {\r\n    // Don't add empty tokens to the queue\r\n    if (!token || !token.trim()) {\r\n      return;\r\n    }\r\n    \r\n    // Throttle token additions to prevent overwhelming the queue\r\n    const now = Date.now();\r\n    const lastTokenTime = tokenQueueRef.current.length > 0 ? \r\n      (tokenQueueRef.current as any).lastAddTime || 0 : 0;\r\n    \r\n    if (now - lastTokenTime < 10) { // Minimum 10ms between tokens\r\n      return;\r\n    }\r\n    \r\n    tokenQueueRef.current.push(token);\r\n    (tokenQueueRef.current as any).lastAddTime = now;\r\n    \r\n    // Start processing if not already running\r\n    if (!streamingTimeoutRef.current) {\r\n      processTokenQueue();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, streamingMessage]);\r\n\r\n\r\n\r\n  /**\r\n   * Clean message submission handler - Industry standard approach\r\n   * \r\n   * This function handles the complete message flow from user input to backend streaming:\r\n   * 1. Input validation and rate limiting\r\n   * 2. User message display (optimistic UI)\r\n   * 3. Backend API call with streaming response\r\n   * 4. Token-by-token message building\r\n   * 5. Error handling and cleanup\r\n   * \r\n   * Follows single responsibility principle and proper error boundaries.\r\n   */\r\n  const handleMessageSubmit = useCallback(async (message: string, file?: File) => {\r\n    console.log('Chat: Message submitted', { message, file });\r\n    \r\n    // Validate input\r\n    if (!message.trim() && !file) {\r\n      console.log('Chat: No message or file provided, skipping submit');\r\n      return;\r\n    }\r\n\r\n    // Validate chat state\r\n    if (!activeChat) {\r\n      console.error('Chat: Cannot send message - no active chat');\r\n      return;\r\n    }\r\n\r\n    // Prevent concurrent execution\r\n    if (isSendingRef.current || isSendingMessage) {\r\n      console.log('Chat: Message already being sent, ignoring duplicate request');\r\n      return;\r\n    }\r\n\r\n    // Rate limiting check\r\n    const now = Date.now();\r\n    if (now - lastMessageTime.current < RATE_LIMIT_MS) {\r\n      console.log('Chat: Rate limit exceeded, ignoring message send');\r\n      return;\r\n    }\r\n\r\n    // Set guards and state\r\n    isSendingRef.current = true;\r\n    setIsSendingMessage(true);\r\n    lastMessageTime.current = now;\r\n\r\n    try {\r\n      // Handle file attachment if provided\r\n      if (file) {\r\n        console.log('Chat: Processing attached file', file.name);\r\n        setAttachedFiles(prev => [...prev, { name: file.name, type: file.type }]);\r\n      }\r\n\r\n      // Create user message\r\n      const userMessage: Message = {\r\n        role: 'user',\r\n        content: message.trim(),\r\n        timestamp: new Date()\r\n      };\r\n\r\n      // Add user message to chat immediately for responsive UI\r\n      addMessageToChat(activeChat.session_id, userMessage);\r\n      setIsLoading(true);\r\n\r\n      // Save any existing streaming message to permanent chat history before clearing\r\n      if (streamingMessage && streamingMessage.content.trim()) {\r\n        console.log('Chat: Saving previous streaming message to history');\r\n        const permanentMessage: Message = {\r\n          role: 'agent',\r\n          content: streamingMessage.content,\r\n          timestamp: new Date(),\r\n          isStreaming: false,\r\n          outputFiles: streamingMessage.outputFiles || [],\r\n          sqlQueries: streamingMessage.sqlQueries || {},\r\n        };\r\n        addMessageToChat(activeChat.session_id, permanentMessage);\r\n      }\r\n\r\n      // Clear any existing streaming message and create new streaming ID\r\n      setStreamingMessage(null);\r\n      const streamingId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\r\n      setCurrentStreamingId(streamingId);\r\n\r\n      // Reset completion guard for new message\r\n      completionGuardRef.current = null;\r\n\r\n      // Add completion guard to prevent duplicate processing\r\n      let hasCompleted = false;\r\n\r\n      // Start streaming\r\n      console.log('Chat: Starting streaming for session:', activeChat.session_id);\r\n        await tokenStreamingService.startStreaming({\r\n          sessionId: activeChat.session_id,\r\n        query: message.trim(),\r\n          outputFormat: 'excel',\r\n          onTokenReceived: (token) => {\r\n            // Prevent processing invalid tokens\r\n            if (!token || typeof token !== 'string') {\r\n              return;\r\n            }\r\n            // Hide thinking animation as soon as first token arrives\r\n              setIsLoading(false);\r\n            addTokenToQueue(token);\r\n          },\r\n          onComplete: (finalContent, completeResponse) => {\r\n          console.log('Chat: Streaming complete', { finalContent, outputFiles: completeResponse?.output_files });\r\n\r\n          // Always turn off the typing indicator\r\n            setIsLoading(false);\r\n\r\n          // Enhanced guard against duplicate completion calls\r\n            const contentHash = `${finalContent}_${Date.now()}`;\r\n            if (hasCompleted || completionGuardRef.current === contentHash) {\r\n            console.log('Chat: Skipping duplicate onComplete call');\r\n              return;\r\n            }\r\n\r\n            hasCompleted = true;\r\n            completionGuardRef.current = contentHash;\r\n\r\n            // Clear any remaining timeout and queue\r\n            if (streamingTimeoutRef.current) {\r\n              clearTimeout(streamingTimeoutRef.current);\r\n              streamingTimeoutRef.current = null;\r\n            }\r\n            tokenQueueRef.current = [];\r\n\r\n          // Add final message to chat\r\n              const finalMessage: Message = {\r\n                role: 'agent',\r\n                content: finalContent,\r\n                timestamp: new Date(),\r\n                isStreaming: false,\r\n                outputFiles: completeResponse?.output_files || [],\r\n                sqlQueries: completeResponse?.sql_queries || {},\r\n              };\r\n              addMessageToChat(activeChat.session_id, finalMessage);\r\n          \r\n          // Cleanup streaming state\r\n            setStreamingMessage(null);\r\n            setCurrentStreamingId(null);\r\n          },\r\n          onError: (error) => {\r\n          console.error('Chat: Streaming failed:', error);\r\n            \r\n            // Always ensure cleanup happens\r\n            setIsLoading(false);\r\n            \r\n            // Clear any remaining timeout and queue\r\n            if (streamingTimeoutRef.current) {\r\n              clearTimeout(streamingTimeoutRef.current);\r\n              streamingTimeoutRef.current = null;\r\n            }\r\n            tokenQueueRef.current = [];\r\n            \r\n          // Show error message\r\n              const errorMessage: Message = {\r\n                role: 'agent',\r\n                content: 'Sorry, there was an error processing your request. Please try again.',\r\n                timestamp: new Date()\r\n              };\r\n              addMessageToChat(activeChat.session_id, errorMessage);\r\n          \r\n          // Cleanup streaming state\r\n            setStreamingMessage(null);\r\n            setCurrentStreamingId(null);\r\n          },\r\n        });\r\n      } catch (error) {\r\n      console.error('Chat: Error in message submission:', error);\r\n        setIsLoading(false);\r\n        \r\n      // Show error message\r\n        if (activeChat) {\r\n          const errorMessage: Message = {\r\n            role: 'agent',\r\n            content: 'Sorry, there was an error processing your request. Please try again.',\r\n            timestamp: new Date()\r\n          };\r\n          addMessageToChat(activeChat.session_id, errorMessage);\r\n        }\r\n    } finally {\r\n      // Reset sending state after a delay to prevent rapid re-sending\r\n      setTimeout(() => {\r\n        setIsSendingMessage(false);\r\n        isSendingRef.current = false;\r\n      }, 1500);\r\n    }\r\n  }, [activeChat, isSendingMessage, streamingMessage, addMessageToChat, addTokenToQueue]);\r\n\r\n  return (\r\n    <div\r\n      className=\"flex flex-col items-center justify-center h-full w-full md:p-6 bg-sidebar-bg overflow-y-auto\"\r\n      onDragOver={e => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n      }}\r\n      onDrop={e => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        const file = e.dataTransfer.files?.[0];\r\n        if (file) {\r\n          setAttachedFiles(prev => [...prev, { name: file.name, type: file.type }]);\r\n        }\r\n      }}\r\n    >\r\n          {/* Welcome message with animated input - only show when no active chat AND no chatId */}\r\n          {!activeChat && !chatId && (\r\n            <ChatStartScreen \r\n              onSendMessage={async (message: string) => {\r\n                console.log('Sending first message to create new chat:', message);\r\n\r\n                // 🚀 OPTIMISTIC UI - Create chat immediately for responsive UX\r\n                const newChat = addChat();\r\n                await setActiveChat(newChat);\r\n\r\n                // 💬 IMMEDIATELY ADD USER MESSAGE (ChatGPT behavior)\r\n                const userMessage: Message = {\r\n                  role: 'user',\r\n                  content: message,\r\n                  timestamp: new Date()\r\n                };\r\n                addMessageToChat(newChat.session_id, userMessage);\r\n\r\n                // Start thinking animation BEFORE navigation to avoid state reset\r\n                setIsLoading(true);\r\n                console.log('🔄 WelcomeMessage: Set isLoading to true');\r\n\r\n                // Clear any existing streaming message and create new streaming ID\r\n                setStreamingMessage(null);\r\n                const streamingId = `welcome_stream_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\r\n                setCurrentStreamingId(streamingId);\r\n\r\n                // Update URL using Next.js router for proper state sync\r\n                router.replace(`/chat/${newChat.id}`);\r\n                console.log('🔄 WelcomeMessage: Navigated to chat, setting pending message');\r\n\r\n                // Set pending first message to be processed after navigation\r\n                setPendingFirstMessage(message);\r\n              }} \r\n            />\r\n          )}\r\n\r\n      <div className=\"w-full max-w-3xl flex flex-col flex-1 h-full\">\r\n        {/* 💬 Scrollable chat messages */}\r\n        <div className=\"flex-grow space-y-8 pr-2 relative\">\r\n          \r\n          {/* Loading state for chat history or when loading specific chat by ID */}\r\n          {((activeChat && isLoadingHistory) || (chatId && !activeChat && (isLoadingChats || !chatNotFound))) && (\r\n            <div className=\"flex flex-1 items-center justify-center min-h-[400px] h-full w-full\">\r\n              <div className=\"max-w-md w-full text-center mx-auto\">\r\n                <div className=\"mb-6 p-6 rounded-lg bg-blue-50 dark:bg-blue-900 text-blue-900 dark:text-blue-100 border border-blue-200 dark:border-blue-700 shadow flex flex-col items-center justify-center text-center\">\r\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3\"></div>\r\n                  <h3 className=\"text-lg font-bold mb-2\">\r\n                    {(activeChat && isLoadingHistory) ? 'Loading conversation...' : 'Loading chat...'}\r\n                  </h3>\r\n                  <p className=\"text-sm\">\r\n                    {(activeChat && isLoadingHistory)\r\n                      ? 'Fetching your chat history from the server.'\r\n                      : 'Finding your chat and loading the conversation.'\r\n                    }\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Chat not found error */}\r\n          {chatId && chatNotFound && !isLoadingChats && (\r\n            <div className=\"flex flex-1 items-center justify-center min-h-[400px] h-full w-full\">\r\n              <div className=\"max-w-md w-full text-center mx-auto\">\r\n                <div className=\"mb-6 p-6 rounded-lg bg-red-50 dark:bg-red-900 text-red-900 dark:text-red-100 border border-red-200 dark:border-red-700 shadow flex flex-col items-center justify-center text-center\">\r\n                  <div className=\"text-red-500 mb-3\">\r\n                    <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h3 className=\"text-lg font-bold mb-2\">Chat Not Found</h3>\r\n                  <p className=\"text-sm mb-4\">The chat you're looking for doesn't exist or may have been deleted.</p>\r\n                  <button\r\n                    onClick={() => router.push('/chat')}\r\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                  >\r\n                    Start New Chat\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeChat && !isLoadingHistory && messages.map((message, index) => (\r\n            <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>\r\n              <div className={`max-w-[85%] px-5 py-2.5 ${message.role === 'user' ? 'bg-gray-200/50 dark:bg-gray-700/50 rounded-3xl text-gray-900 dark:text-gray-100' : 'text-gray-900 dark:text-gray-100'}`}>\r\n                {message.role === 'user' ? (\r\n                  <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\r\n                ) : (\r\n                  <AnalyticalContentRenderer\r\n                    content={message.content}\r\n                    isStreaming={false}\r\n                    className=\"text-sm\"\r\n                  />\r\n                )}\r\n\r\n                {/* Show data table if output files are available */}\r\n                {message.role === 'agent' && message.outputFiles && message.outputFiles.length > 0 && (\r\n                  <div className=\"mt-3\">\r\n                    <DataTable outputFiles={message.outputFiles} />\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ))}\r\n\r\n          {/* Streaming message - Show when we have streaming content */}\r\n          {streamingMessage && activeChat && (\r\n            <div className=\"flex justify-start\">\r\n              <div className=\"max-w-[85%] px-5 py-2.5 text-gray-900 dark:text-gray-100\">\r\n                <StreamingMessage\r\n                  content={streamingMessage.content}\r\n                  isStreaming={streamingMessage.isStreaming || false}\r\n                  className=\"text-gray-900 dark:text-gray-100\"\r\n                />\r\n                \r\n                {/* Show data table if output files are available during streaming */}\r\n                {streamingMessage.outputFiles && streamingMessage.outputFiles.length > 0 && (\r\n                  <div className=\"mt-3\">\r\n                    <DataTable outputFiles={streamingMessage.outputFiles} />\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* 🤖 Thinking Animation - Only show when loading and NOT streaming */}\r\n          {isLoading && activeChat && !streamingMessage && (\r\n            <div className=\"flex justify-start mb-4 animate-in fade-in duration-300\">\r\n              <div className=\"max-w-[70%] px-4 py-3 rounded-2xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 shadow-sm border border-gray-100 dark:border-gray-600\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"flex space-x-1\">\r\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{animationDelay: '0ms', animationDuration: '1s'}}></div>\r\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{animationDelay: '150ms', animationDuration: '1s'}}></div>\r\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{animationDelay: '300ms', animationDuration: '1s'}}></div>\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-300 font-medium\">AI is thinking...</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <div ref={messagesEndRef} />\r\n        </div>\r\n\r\n        {/* 📨 Message input area - Only show when there's an active chat or loading a specific chat */}\r\n        {(activeChat || (chatId && !chatNotFound)) && (\r\n          <div className=\"mt-6 border-t border-gray-200 dark:border-gray-700 pt-6\">\r\n            <MessageInput\r\n              onSubmit={handleMessageSubmit}\r\n              placeholder={activeChat ? \"Message...\" : \"Loading chat...\"}\r\n              disabled={!activeChat || isLoading || isSendingMessage}\r\n              isLoading={isLoading || isSendingMessage}\r\n              className=\"w-full max-w-full\"\r\n            />\r\n              </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Chat;\r\n"], "names": [], "mappings": ";;;;AACA;AACA,mDAAmD;AACnD;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;AAiCA,YAAY;AACZ,MAAM,gBAAgB,MAAM,iDAAiD;AAC7E,MAAM,6BAA6B,IAAI;AAEvC,MAAM,OAA4B,CAAC,EAAE,MAAM,EAAE;;IAC3C,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyC,EAAE;IAC5F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACzE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,OAAO;IACP,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAY,EAAE;IACzC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACjD,MAAM,EACJ,WAAW,EACX,UAAU,EACV,YAAY,EACZ,gBAAgB,EAChB,cAAc,EACd,mBAAmB,EACnB,sBAAsB,EACtB,aAAa,EACb,gBAAgB,EAChB,OAAO,EACP,eAAe,EAChB,GAAG,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,gCAAgC;IAChC,MAAM,WAAW,aAAa,YAAY,CAAC,WAAW,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;IAE5E,kEAAkE;IAClE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE,IAAM,CAAC;gBAChC,OAAO,aACH,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,gBAAgB,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,GAC9G;gBACJ,MAAM,aAAa,2NAAA,CAAA,gBAAa,GAAG,qMAAA,CAAA,OAAI;YACzC,CAAC;mCAAG;QAAC;KAAW;IAEhB,qCAAqC;IACrC,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;IAEb,2EAA2E;IAC3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,QAAQ,GAAG,CAAC,mDAAmD;YAC/D,QAAQ,GAAG,CAAC,uBAAuB,YAAY;YAC/C,QAAQ,GAAG,CAAC,wBAAwB,YAAY,MAAM,GAAG;YACzD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,sDAAsD;YACtD,IAAI,CAAC,QAAQ;gBACX,IAAI,YAAY;oBACd,QAAQ,GAAG,CAAC;oBACZ,cAAc;gBAChB;gBACA;YACF;YAEA,mEAAmE;YACnE,mFAAmF;YACnF,IAAI,cAAc,WAAW,EAAE,KAAK,QAAQ;gBAC1C,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,4DAA4D;YAC5D,gBAAgB;YAEhB,uFAAuF;YACvF,sDAAsD;YACtD,MAAM,wBAAwB,YAAY,IAAI;wDAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;YACnE,IAAI,uBAAuB;gBACzB,QAAQ,GAAG,CAAC,mDAAmD,sBAAsB,EAAE;gBACvF,cAAc;gBACd;YACF;YAEA,8DAA8D;YAC9D,8DAA8D;YAC9D,IAAI,gBAAgB;gBAClB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,4EAA4E;YAC5E,+FAA+F;YAC/F,mGAAmG;YACnG,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,8FAA8F;YAC9F,QAAQ,GAAG,CAAC,2DAA2D;YACvE,gBAAgB;QAClB;yBAAG;QAAC;QAAQ,YAAY;QAAI;QAAgB,YAAY,MAAM;KAAC;IAE/D,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,oDAAoD;YACpD,IAAI,yBAAyB,OAAO,EAAE;gBACpC;YACF;YAEA,2BAA2B;YAC3B,QAAQ,GAAG,CAAC,+CAA+C;gBACzD,qBAAqB,CAAC,CAAC;gBACvB,YAAY,CAAC,CAAC;gBACd,cAAc,YAAY;gBAC1B;gBACA;gBACA,eAAe,YAAY,OAAO;YACpC;YAEA,uGAAuG;YACvG,IAAI,uBAAuB,cAAc,UAAU,WAAW,EAAE,KAAK,UAAU,CAAC,2BAA2B,GAAG,CAAC,SAAS;gBACtH,6DAA6D;gBAC7D,yBAAyB,OAAO,GAAG;gBACnC,2BAA2B,GAAG,CAAC;gBAE/B,QAAQ,GAAG,CAAC,wDAAwD;gBAEpE,oBAAoB;gBACpB,aAAa;gBACb,QAAQ,GAAG,CAAC;gBAEZ,mEAAmE;gBACnE,oBAAoB;gBACpB,MAAM,cAAc,CAAC,eAAe,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;gBACjG,sBAAsB;gBAEtB,yCAAyC;gBACzC,mBAAmB,OAAO,GAAG;gBAE7B,oDAAoD;gBACpD,MAAM,mBAAmB;gBACzB,uBAAuB;gBAEvB,uDAAuD;gBACvD,IAAI,eAAe;gBAEnB,kBAAkB;gBAClB,MAAM;qDAAiB;wBACrB,IAAI;4BACF,MAAM,kJAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;gCACzC,WAAW,WAAW,UAAU;gCAChC,OAAO;gCACP,cAAc;gCACd,eAAe;qEAAE,CAAC;wCAChB,QAAQ,GAAG,CAAC,sCAAsC;wCAClD,aAAa;wCACb,mDAAmD;wCACnD,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;4CACvC;wCACF;wCACA,gBAAgB;oCAClB;;gCACA,UAAU;qEAAE,OAAO,cAAc;wCAC/B,4EAA4E;wCAC5E,wEAAwE;wCACxE,4CAA4C;wCAC5C,aAAa;wCAEb,uEAAuE;wCACvE,MAAM,cAAc,GAAG,aAAa,CAAC,EAAE,KAAK,GAAG,IAAI;wCACnD,IAAI,gBAAgB,mBAAmB,OAAO,KAAK,aAAa;4CAC9D,QAAQ,GAAG,CAAC;4CACZ;wCACF;wCACA,eAAe;wCACf,mBAAmB,OAAO,GAAG;wCAE7B,QAAQ,GAAG,CAAC,yCAAyC;wCACrD,QAAQ,GAAG,CAAC,8BAA8B;wCAE1C,wCAAwC;wCACxC,IAAI,oBAAoB,OAAO,EAAE;4CAC/B,aAAa,oBAAoB,OAAO;4CACxC,oBAAoB,OAAO,GAAG;wCAChC;wCACA,cAAc,OAAO,GAAG,EAAE;wCAE1B,qDAAqD;wCACrD,MAAM,eAAwB;4CAC5B,MAAM;4CACN,SAAS;4CACT,WAAW,IAAI;4CACf,aAAa;4CACb,aAAa,kBAAkB,gBAAgB,EAAE;4CACjD,YAAY,kBAAkB,eAAe,CAAC;wCAChD;wCACA,iBAAiB,WAAW,UAAU,EAAE;wCACxC,oBAAoB;wCACpB,sBAAsB;wCAEtB,yCAAyC;wCACzC;6EAAW;gDACT,MAAM;4CACR;4EAAG;oCACL;;gCACA,OAAO;qEAAE,CAAC;wCACR,QAAQ,KAAK,CAAC,qBAAqB;wCAEnC,gCAAgC;wCAChC,aAAa;wCAEb,wCAAwC;wCACxC,IAAI,oBAAoB,OAAO,EAAE;4CAC/B,aAAa,oBAAoB,OAAO;4CACxC,oBAAoB,OAAO,GAAG;wCAChC;wCACA,cAAc,OAAO,GAAG,EAAE;wCAE1B,+CAA+C;wCAC/C,IAAI,YAAY;4CACd,MAAM,eAAwB;gDAC5B,MAAM;gDACN,SAAS;gDACT,WAAW,IAAI;4CACjB;4CACA,iBAAiB,WAAW,UAAU,EAAE;wCAC1C;wCACA,oBAAoB;wCACpB,sBAAsB;oCACxB;;4BACF;wBACF,EAAE,OAAO,gBAAgB;4BACvB,QAAQ,KAAK,CAAC,gDAAgD;4BAE9D,gCAAgC;4BAChC,aAAa;4BAEb,wCAAwC;4BACxC,IAAI,oBAAoB,OAAO,EAAE;gCAC/B,aAAa,oBAAoB,OAAO;gCACxC,oBAAoB,OAAO,GAAG;4BAChC;4BACA,cAAc,OAAO,GAAG,EAAE;4BAE1B,+CAA+C;4BAC/C,IAAI,YAAY;gCACd,MAAM,eAAwB;oCAC5B,MAAM;oCACN,SAAS;oCACT,WAAW,IAAI;gCACjB;gCACA,iBAAiB,WAAW,UAAU,EAAE;4BAC1C;4BACA,oBAAoB;4BACpB,sBAAsB;wBACxB;oBACF;;gBAEA;YACF;QACF;yBAAG;QAAC;QAAqB;QAAY;KAAO,GAAG,6BAA6B;IAE5E,8FAA8F;IAC9F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,2BAA2B;YAC3B,yBAAyB,OAAO,GAAG;QACrC;yBAAG;QAAC;KAAO;IAEX,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;kCAAO;oBACL,kJAAA,CAAA,wBAAqB,CAAC,aAAa;oBACnC,IAAI,oBAAoB,OAAO,EAAE;wBAC/B,aAAa,oBAAoB,OAAO;oBAC1C;gBACF;;QACF;yBAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,kDAAkD;IAClD,MAAM,mBAAmB;QACvB,8EAA8E;QAC9E,4DAA4D;QAC5D,aAAa;QACb,8EAA8E;QAC9E,WAAW;QACX,qCAAqC;QACrC,WAAW;IACb;IAEA,4DAA4D;IAC5D,MAAM,sBAAsB,CAAC;QAC3B,MAAM,YAAY,iBAAiB,WAAW;QAC9C,MAAM,cAAc,MAAM,IAAI,GAAG,MAAM;QAEvC,gEAAgE;QAChE,IAAI,eAAe,GAAG;YACpB,OAAO,iBAAiB,SAAS;QACnC;QAEA,kEAAkE;QAClE,IAAI,cAAc,GAAG;YACnB,OAAO,iBAAiB,SAAS;QACnC;QAEA,0CAA0C;QAC1C,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,oBAAoB;QACxB,qEAAqE;QACrE,IAAI,cAAc,OAAO,CAAC,MAAM,KAAK,GAAG;YACtC,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,aAAa,oBAAoB,OAAO;gBACxC,oBAAoB,OAAO,GAAG;YAChC;YACA;QACF;QAEA,MAAM,QAAQ,cAAc,OAAO,CAAC,KAAK;QACzC,IAAI,SAAS,MAAM,IAAI,IAAI;YACzB,oBAAoB,CAAA;gBAClB,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;wBACf,aAAa;oBACf;gBACF;gBACA,8CAA8C;gBAC9C,MAAM,aAAa,KAAK,OAAO,GAAG;gBAClC,IAAI,eAAe,KAAK,OAAO,EAAE;oBAC/B,OAAO,MAAM,qDAAqD;gBACpE;gBACA,OAAO;oBACL,GAAG,IAAI;oBACP,SAAS;gBACX;YACF;YAEA,qDAAqD;YACrD,IAAI,cAAc,OAAO,CAAC,MAAM,GAAG,GAAG;gBACpC,MAAM,QAAQ,oBAAoB;gBAClC,oBAAoB,OAAO,GAAG,WAAW,mBAAmB;YAC9D,OAAO;gBACL,iEAAiE;gBACjE,uDAAuD;gBACvD,IAAI,oBAAoB,OAAO,EAAE;oBAC/B,aAAa,oBAAoB,OAAO;oBACxC,oBAAoB,OAAO,GAAG;gBAChC;YACF;QACF,OAAO;YACL,kEAAkE;YAClE,IAAI,cAAc,OAAO,CAAC,MAAM,GAAG,GAAG;gBACpC,oBAAoB,OAAO,GAAG,WAAW,mBAAmB,KAAK,+BAA+B;YAClG;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,sCAAsC;QACtC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B;QACF;QAEA,6DAA6D;QAC7D,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,gBAAgB,cAAc,OAAO,CAAC,MAAM,GAAG,IACnD,AAAC,cAAc,OAAO,CAAS,WAAW,IAAI,IAAI;QAEpD,IAAI,MAAM,gBAAgB,IAAI;YAC5B;QACF;QAEA,cAAc,OAAO,CAAC,IAAI,CAAC;QAC1B,cAAc,OAAO,CAAS,WAAW,GAAG;QAE7C,0CAA0C;QAC1C,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG;QAAC;QAAU;KAAiB;IAI/B;;;;;;;;;;;GAWC,GACD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO,SAAiB;YAC9D,QAAQ,GAAG,CAAC,2BAA2B;gBAAE;gBAAS;YAAK;YAEvD,iBAAiB;YACjB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,MAAM;gBAC5B,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,sBAAsB;YACtB,IAAI,CAAC,YAAY;gBACf,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,+BAA+B;YAC/B,IAAI,aAAa,OAAO,IAAI,kBAAkB;gBAC5C,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,sBAAsB;YACtB,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,MAAM,gBAAgB,OAAO,GAAG,eAAe;gBACjD,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,uBAAuB;YACvB,aAAa,OAAO,GAAG;YACvB,oBAAoB;YACpB,gBAAgB,OAAO,GAAG;YAE1B,IAAI;gBACF,qCAAqC;gBACrC,IAAI,MAAM;oBACR,QAAQ,GAAG,CAAC,kCAAkC,KAAK,IAAI;oBACvD;iEAAiB,CAAA,OAAQ;mCAAI;gCAAM;oCAAE,MAAM,KAAK,IAAI;oCAAE,MAAM,KAAK,IAAI;gCAAC;6BAAE;;gBAC1E;gBAEA,sBAAsB;gBACtB,MAAM,cAAuB;oBAC3B,MAAM;oBACN,SAAS,QAAQ,IAAI;oBACrB,WAAW,IAAI;gBACjB;gBAEA,yDAAyD;gBACzD,iBAAiB,WAAW,UAAU,EAAE;gBACxC,aAAa;gBAEb,gFAAgF;gBAChF,IAAI,oBAAoB,iBAAiB,OAAO,CAAC,IAAI,IAAI;oBACvD,QAAQ,GAAG,CAAC;oBACZ,MAAM,mBAA4B;wBAChC,MAAM;wBACN,SAAS,iBAAiB,OAAO;wBACjC,WAAW,IAAI;wBACf,aAAa;wBACb,aAAa,iBAAiB,WAAW,IAAI,EAAE;wBAC/C,YAAY,iBAAiB,UAAU,IAAI,CAAC;oBAC9C;oBACA,iBAAiB,WAAW,UAAU,EAAE;gBAC1C;gBAEA,mEAAmE;gBACnE,oBAAoB;gBACpB,MAAM,cAAc,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;gBACzF,sBAAsB;gBAEtB,yCAAyC;gBACzC,mBAAmB,OAAO,GAAG;gBAE7B,uDAAuD;gBACvD,IAAI,eAAe;gBAEnB,kBAAkB;gBAClB,QAAQ,GAAG,CAAC,yCAAyC,WAAW,UAAU;gBACxE,MAAM,kJAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;oBACzC,WAAW,WAAW,UAAU;oBAClC,OAAO,QAAQ,IAAI;oBACjB,cAAc;oBACd,eAAe;iEAAE,CAAC;4BAChB,oCAAoC;4BACpC,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;gCACvC;4BACF;4BACA,yDAAyD;4BACvD,aAAa;4BACf,gBAAgB;wBAClB;;oBACA,UAAU;iEAAE,CAAC,cAAc;4BAC3B,QAAQ,GAAG,CAAC,4BAA4B;gCAAE;gCAAc,aAAa,kBAAkB;4BAAa;4BAEpG,uCAAuC;4BACrC,aAAa;4BAEf,oDAAoD;4BAClD,MAAM,cAAc,GAAG,aAAa,CAAC,EAAE,KAAK,GAAG,IAAI;4BACnD,IAAI,gBAAgB,mBAAmB,OAAO,KAAK,aAAa;gCAChE,QAAQ,GAAG,CAAC;gCACV;4BACF;4BAEA,eAAe;4BACf,mBAAmB,OAAO,GAAG;4BAE7B,wCAAwC;4BACxC,IAAI,oBAAoB,OAAO,EAAE;gCAC/B,aAAa,oBAAoB,OAAO;gCACxC,oBAAoB,OAAO,GAAG;4BAChC;4BACA,cAAc,OAAO,GAAG,EAAE;4BAE5B,4BAA4B;4BACxB,MAAM,eAAwB;gCAC5B,MAAM;gCACN,SAAS;gCACT,WAAW,IAAI;gCACf,aAAa;gCACb,aAAa,kBAAkB,gBAAgB,EAAE;gCACjD,YAAY,kBAAkB,eAAe,CAAC;4BAChD;4BACA,iBAAiB,WAAW,UAAU,EAAE;4BAE5C,0BAA0B;4BACxB,oBAAoB;4BACpB,sBAAsB;wBACxB;;oBACA,OAAO;iEAAE,CAAC;4BACV,QAAQ,KAAK,CAAC,2BAA2B;4BAEvC,gCAAgC;4BAChC,aAAa;4BAEb,wCAAwC;4BACxC,IAAI,oBAAoB,OAAO,EAAE;gCAC/B,aAAa,oBAAoB,OAAO;gCACxC,oBAAoB,OAAO,GAAG;4BAChC;4BACA,cAAc,OAAO,GAAG,EAAE;4BAE5B,qBAAqB;4BACjB,MAAM,eAAwB;gCAC5B,MAAM;gCACN,SAAS;gCACT,WAAW,IAAI;4BACjB;4BACA,iBAAiB,WAAW,UAAU,EAAE;4BAE5C,0BAA0B;4BACxB,oBAAoB;4BACpB,sBAAsB;wBACxB;;gBACF;YACF,EAAE,OAAO,OAAO;gBAChB,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,aAAa;gBAEf,qBAAqB;gBACnB,IAAI,YAAY;oBACd,MAAM,eAAwB;wBAC5B,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;oBACjB;oBACA,iBAAiB,WAAW,UAAU,EAAE;gBAC1C;YACJ,SAAU;gBACR,gEAAgE;gBAChE;6DAAW;wBACT,oBAAoB;wBACpB,aAAa,OAAO,GAAG;oBACzB;4DAAG;YACL;QACF;gDAAG;QAAC;QAAY;QAAkB;QAAkB;QAAkB;KAAgB;IAEtF,qBACE,6LAAC;QACC,WAAU;QACV,YAAY,CAAA;YACV,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;QACA,QAAQ,CAAA;YACN,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,MAAM,OAAO,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE;YACtC,IAAI,MAAM;gBACR,iBAAiB,CAAA,OAAQ;2BAAI;wBAAM;4BAAE,MAAM,KAAK,IAAI;4BAAE,MAAM,KAAK,IAAI;wBAAC;qBAAE;YAC1E;QACF;;YAGK,CAAC,cAAc,CAAC,wBACf,6LAAC,4JAAA,CAAA,kBAAe;gBACd,eAAe,OAAO;oBACpB,QAAQ,GAAG,CAAC,6CAA6C;oBAEzD,+DAA+D;oBAC/D,MAAM,UAAU;oBAChB,MAAM,cAAc;oBAEpB,qDAAqD;oBACrD,MAAM,cAAuB;wBAC3B,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;oBACjB;oBACA,iBAAiB,QAAQ,UAAU,EAAE;oBAErC,kEAAkE;oBAClE,aAAa;oBACb,QAAQ,GAAG,CAAC;oBAEZ,mEAAmE;oBACnE,oBAAoB;oBACpB,MAAM,cAAc,CAAC,eAAe,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;oBACjG,sBAAsB;oBAEtB,wDAAwD;oBACxD,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;oBACpC,QAAQ,GAAG,CAAC;oBAEZ,6DAA6D;oBAC7D,uBAAuB;gBACzB;;;;;;0BAIR,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BAGZ,CAAC,AAAC,cAAc,oBAAsB,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAE,mBAChG,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAG,WAAU;0DACX,AAAC,cAAc,mBAAoB,4BAA4B;;;;;;0DAElE,6LAAC;gDAAE,WAAU;0DACV,AAAC,cAAc,mBACZ,gDACA;;;;;;;;;;;;;;;;;;;;;;4BASb,UAAU,gBAAgB,CAAC,gCAC1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAoB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC3E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAE,WAAU;0DAAe;;;;;;0DAC5B,6LAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;4BAQR,cAAc,CAAC,oBAAoB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACzD,6LAAC;oCAAgB,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;8CAC7F,cAAA,6LAAC;wCAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,IAAI,KAAK,SAAS,oFAAoF,oCAAoC;;4CAC1L,QAAQ,IAAI,KAAK,uBAChB,6LAAC;gDAAE,WAAU;0DAA+B,QAAQ,OAAO;;;;;qEAE3D,6LAAC,sKAAA,CAAA,UAAyB;gDACxB,SAAS,QAAQ,OAAO;gDACxB,aAAa;gDACb,WAAU;;;;;;4CAKb,QAAQ,IAAI,KAAK,WAAW,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC/E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sJAAA,CAAA,UAAS;oDAAC,aAAa,QAAQ,WAAW;;;;;;;;;;;;;;;;;mCAfzC;;;;;4BAuBX,oBAAoB,4BACnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6JAAA,CAAA,UAAgB;4CACf,SAAS,iBAAiB,OAAO;4CACjC,aAAa,iBAAiB,WAAW,IAAI;4CAC7C,WAAU;;;;;;wCAIX,iBAAiB,WAAW,IAAI,iBAAiB,WAAW,CAAC,MAAM,GAAG,mBACrE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sJAAA,CAAA,UAAS;gDAAC,aAAa,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;;;;;4BAQ7D,aAAa,cAAc,CAAC,kCAC3B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAC,gBAAgB;4DAAO,mBAAmB;wDAAI;;;;;;kEACvH,6LAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAC,gBAAgB;4DAAS,mBAAmB;wDAAI;;;;;;kEACzH,6LAAC;wDAAI,WAAU;wDAAkD,OAAO;4DAAC,gBAAgB;4DAAS,mBAAmB;wDAAI;;;;;;;;;;;;0DAE3H,6LAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;;;;;0CAK/E,6LAAC;gCAAI,KAAK;;;;;;;;;;;;oBAIX,CAAC,cAAe,UAAU,CAAC,YAAa,mBACvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;4BACX,UAAU;4BACV,aAAa,aAAa,eAAe;4BACzC,UAAU,CAAC,cAAc,aAAa;4BACtC,WAAW,aAAa;4BACxB,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GAvvBM;;QA6BA,0IAAA,CAAA,iBAAc;QACH,qIAAA,CAAA,YAAS;QAcxB,+HAAA,CAAA,eAAY;;;KA5CR;uCAyvBS", "debugId": null}}, {"offset": {"line": 6354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/onboarding.ts"], "sourcesContent": ["// Onboarding utility functions\r\n\r\nimport { ROUTES } from '@/lib/constants';\r\n\r\n/**\r\n * Determines the appropriate redirect path based on user authentication and onboarding status\r\n */\r\nexport function getRedirectPath(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): string | null {\r\n  // Not authenticated - redirect to login (except for public routes)\r\n  if (!isAuthenticated) {\r\n    const publicRoutes = [\r\n      ROUTES.HOME,\r\n      ROUTES.LOGIN,\r\n      ROUTES.REGISTER,\r\n      ROUTES.AUTH.CALLBACK,\r\n      ROUTES.OAUTH.CALLBACK,\r\n      ROUTES.ONBOARDING,\r\n    ];\r\n\r\n    if (!publicRoutes.includes(currentPath as any)) {\r\n      return ROUTES.LOGIN;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Authenticated user scenarios\r\n  if (isAuthenticated) {\r\n    // New user not on onboarding page - redirect to onboarding\r\n    if (isNewUser && currentPath !== ROUTES.ONBOARDING) {\r\n      const publicRoutes = [\r\n        ROUTES.HOME,\r\n        ROUTES.LOGIN,\r\n        ROUTES.REGISTER,\r\n        ROUTES.AUTH.CALLBACK,\r\n        ROUTES.OAUTH.CALLBACK,\r\n      ];\r\n\r\n      // Don't redirect if on public routes (except login)\r\n      if (!publicRoutes.includes(currentPath as any) || currentPath === ROUTES.LOGIN) {\r\n        return ROUTES.ONBOARDING;\r\n      }\r\n    }\r\n    \r\n    // Existing user on onboarding page - redirect to chat\r\n    if (!isNewUser && currentPath === ROUTES.ONBOARDING) {\r\n      return ROUTES.CHAT;\r\n    }\r\n    \r\n    // Authenticated user on login or register page - redirect based on onboarding status\r\n    if (currentPath === ROUTES.LOGIN || currentPath === ROUTES.REGISTER) {\r\n      return isNewUser ? ROUTES.ONBOARDING : ROUTES.CHAT;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Checks if a route requires authentication\r\n */\r\nexport function isProtectedRoute(path: string): boolean {\r\n  const publicRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return !publicRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Checks if a route is accessible to new users\r\n */\r\nexport function isNewUserAccessibleRoute(path: string): boolean {\r\n  const newUserRoutes = [\r\n    ROUTES.HOME,\r\n    ROUTES.LOGIN,\r\n    ROUTES.REGISTER,\r\n    ROUTES.AUTH.CALLBACK,\r\n    ROUTES.OAUTH.CALLBACK,\r\n    ROUTES.ONBOARDING,\r\n  ];\r\n\r\n  return newUserRoutes.includes(path as any);\r\n}\r\n\r\n/**\r\n * Gets the next step in the onboarding flow after completion\r\n */\r\nexport function getPostOnboardingRedirect(): string {\r\n  return ROUTES.CHAT;\r\n}\r\n\r\n/**\r\n * Validates if onboarding completion is allowed from the current state\r\n */\r\nexport function canCompleteOnboarding(\r\n  isAuthenticated: boolean,\r\n  isNewUser: boolean,\r\n  currentPath: string\r\n): boolean {\r\n  return isAuthenticated && isNewUser && currentPath === ROUTES.ONBOARDING;\r\n}\r\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;AAE/B;AAAA;;AAKO,SAAS,gBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,mEAAmE;IACnE,IAAI,CAAC,iBAAiB;QACpB,MAAM,eAAe;YACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;YACX,oIAAA,CAAA,SAAM,CAAC,KAAK;YACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;YACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;YACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;SAClB;QAED,IAAI,CAAC,aAAa,QAAQ,CAAC,cAAqB;YAC9C,OAAO,oIAAA,CAAA,SAAM,CAAC,KAAK;QACrB;QACA,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,iBAAiB;QACnB,2DAA2D;QAC3D,IAAI,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YAClD,MAAM,eAAe;gBACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;gBACX,oIAAA,CAAA,SAAM,CAAC,KAAK;gBACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;gBACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;gBACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;aACtB;YAED,oDAAoD;YACpD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAuB,gBAAgB,oIAAA,CAAA,SAAM,CAAC,KAAK,EAAE;gBAC9E,OAAO,oIAAA,CAAA,SAAM,CAAC,UAAU;YAC1B;QACF;QAEA,sDAAsD;QACtD,IAAI,CAAC,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU,EAAE;YACnD,OAAO,oIAAA,CAAA,SAAM,CAAC,IAAI;QACpB;QAEA,qFAAqF;QACrF,IAAI,gBAAgB,oIAAA,CAAA,SAAM,CAAC,KAAK,IAAI,gBAAgB,oIAAA,CAAA,SAAM,CAAC,QAAQ,EAAE;YACnE,OAAO,YAAY,oIAAA,CAAA,SAAM,CAAC,UAAU,GAAG,oIAAA,CAAA,SAAM,CAAC,IAAI;QACpD;IACF;IAEA,OAAO;AACT;AAKO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,eAAe;QACnB,oIAAA,CAAA,SAAM,CAAC,IAAI;QACX,oIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,CAAC,aAAa,QAAQ,CAAC;AAChC;AAKO,SAAS,yBAAyB,IAAY;IACnD,MAAM,gBAAgB;QACpB,oIAAA,CAAA,SAAM,CAAC,IAAI;QACX,oIAAA,CAAA,SAAM,CAAC,KAAK;QACZ,oIAAA,CAAA,SAAM,CAAC,QAAQ;QACf,oIAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,oIAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QACrB,oIAAA,CAAA,SAAM,CAAC,UAAU;KAClB;IAED,OAAO,cAAc,QAAQ,CAAC;AAChC;AAKO,SAAS;IACd,OAAO,oIAAA,CAAA,SAAM,CAAC,IAAI;AACpB;AAKO,SAAS,sBACd,eAAwB,EACxB,SAAkB,EAClB,WAAmB;IAEnB,OAAO,mBAAmB,aAAa,gBAAgB,oIAAA,CAAA,SAAM,CAAC,UAAU;AAC1E", "debugId": null}}, {"offset": {"line": 6445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '@/providers/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { isProtectedRoute } from '@/lib/utils/onboarding';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requireAuth?: boolean;\n  redirectTo?: string;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  requireAuth = true, \n  redirectTo = '/login' \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, isNewUser, signIn } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isCheckingAuth, setIsCheckingAuth] = useState(true);\n  const [hasTriedAutoAuth, setHasTriedAutoAuth] = useState(false);\n\n  useEffect(() => {\n    const checkAuthentication = async () => {\n      // Skip auth check for public routes\n      if (!requireAuth || !isProtectedRoute(pathname)) {\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If already authenticated, handle onboarding redirect\n      if (isAuthenticated) {\n        if (isNewUser && pathname !== '/onboarding') {\n          console.log('New user on protected route, redirecting to onboarding');\n          router.push('/onboarding');\n          return;\n        }\n        if (!isNewUser && pathname === '/onboarding') {\n          console.log('Existing user on onboarding, redirecting to chat');\n          router.push('/chat');\n          return;\n        }\n        setIsCheckingAuth(false);\n        return;\n      }\n\n      // If not authenticated and haven't tried auto-auth yet, try it once\n      if (!isAuthenticated && !hasTriedAutoAuth && !isLoading) {\n        setHasTriedAutoAuth(true);\n        console.log('Attempting automatic authentication from stored tokens...');\n\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n          // If successful, the useEffect will re-run due to isAuthenticated change\n        } catch (error) {\n          console.log('Auto-authentication failed, redirecting to login');\n          router.push(redirectTo);\n        }\n        return;\n      }\n\n      // If not authenticated and already tried auto-auth, redirect to login\n      if (!isAuthenticated && hasTriedAutoAuth && !isLoading) {\n        console.log('Not authenticated, redirecting to login');\n        router.push(redirectTo);\n        return;\n      }\n\n      setIsCheckingAuth(false);\n    };\n\n    checkAuthentication();\n  }, [isAuthenticated, isLoading, isNewUser, pathname, requireAuth, redirectTo, router, signIn, hasTriedAutoAuth]);\n\n  // Show loading state while checking authentication\n  if (isCheckingAuth || isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">Checking authentication...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // For protected routes, only render children if authenticated\n  if (requireAuth && !isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-background\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Authentication Required</h2>\n          <p className=\"text-muted-foreground mb-4\">Please sign in to access this page.</p>\n          <button\n            onClick={() => router.push(redirectTo)}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n          >\n            Sign In\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n}\n\n// Higher-order component for easy wrapping\nexport function withProtectedRoute<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: Omit<ProtectedRouteProps, 'children'>\n) {\n  return function ProtectedComponent(props: P) {\n    return (\n      <ProtectedRoute {...options}>\n        <Component {...props} />\n      </ProtectedRoute>\n    );\n  };\n}\n\n// Hook for manual authentication checks\nexport function useRequireAuth(redirectTo: string = '/login') {\n  const { isAuthenticated, isLoading, signIn } = useAuth();\n  const router = useRouter();\n  const [isChecking, setIsChecking] = useState(true);\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (isLoading) return;\n\n      if (!isAuthenticated) {\n        try {\n          await signIn(undefined, false); // Don't redirect, just authenticate\n        } catch {\n          router.push(redirectTo);\n        }\n      }\n      setIsChecking(false);\n    };\n\n    checkAuth();\n  }, [isAuthenticated, isLoading, signIn, router, redirectTo]);\n\n  return { isAuthenticated, isLoading: isLoading || isChecking };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAae,SAAS,eAAe,EACrC,QAAQ,EACR,cAAc,IAAI,EAClB,aAAa,QAAQ,EACD;;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;gEAAsB;oBAC1B,oCAAoC;oBACpC,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;wBAC/C,kBAAkB;wBAClB;oBACF;oBAEA,uDAAuD;oBACvD,IAAI,iBAAiB;wBACnB,IAAI,aAAa,aAAa,eAAe;4BAC3C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;4BACZ;wBACF;wBACA,IAAI,CAAC,aAAa,aAAa,eAAe;4BAC5C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;4BACZ;wBACF;wBACA,kBAAkB;wBAClB;oBACF;oBAEA,oEAAoE;oBACpE,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,WAAW;wBACvD,oBAAoB;wBACpB,QAAQ,GAAG,CAAC;wBAEZ,IAAI;4BACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;wBACpE,yEAAyE;wBAC3E,EAAE,OAAO,OAAO;4BACd,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd;wBACA;oBACF;oBAEA,sEAAsE;oBACtE,IAAI,CAAC,mBAAmB,oBAAoB,CAAC,WAAW;wBACtD,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,kBAAkB;gBACpB;;YAEA;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAW;QAAU;QAAa;QAAY;QAAQ;QAAQ;KAAiB;IAE/G,mDAAmD;IACnD,IAAI,kBAAkB,WAAW;QAC/B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,8DAA8D;IAC9D,IAAI,eAAe,CAAC,iBAAiB;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBAAO;kBAAG;;AACZ;GA9FwB;;QAKoC,mIAAA,CAAA,UAAO;QAClD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAPN;AAiGjB,SAAS,mBACd,SAAiC,EACjC,OAA+C;IAE/C,OAAO,SAAS,mBAAmB,KAAQ;QACzC,qBACE,6LAAC;YAAgB,GAAG,OAAO;sBACzB,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,aAAqB,QAAQ;;IAC1D,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI,WAAW;oBAEf,IAAI,CAAC,iBAAiB;wBACpB,IAAI;4BACF,MAAM,OAAO,WAAW,QAAQ,oCAAoC;wBACtE,EAAE,OAAM;4BACN,OAAO,IAAI,CAAC;wBACd;oBACF;oBACA,cAAc;gBAChB;;YAEA;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAQ;QAAQ;KAAW;IAE3D,OAAO;QAAE;QAAiB,WAAW,aAAa;IAAW;AAC/D;IAvBgB;;QACiC,mIAAA,CAAA,UAAO;QACvC,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 6687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28dashboard%29/chat/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from 'react';\r\nimport Layout from '@/components/layout/Layout';\r\nimport Chat from '@/components/features/chat/Chat';\r\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\r\n\r\nexport default function NewChatPage() {\r\n  return (\r\n    <ProtectedRoute>\r\n      <Layout>\r\n        <Chat />\r\n      </Layout>\r\n    </ProtectedRoute>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC,+IAAA,CAAA,UAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,UAAM;sBACL,cAAA,6LAAC,iJAAA,CAAA,UAAI;;;;;;;;;;;;;;;AAIb;KARwB", "debugId": null}}]}