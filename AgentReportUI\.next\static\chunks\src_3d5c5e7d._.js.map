{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/cn.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/date.ts"], "sourcesContent": ["// Date utility functions\n\nexport const formatDate = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n};\n\nexport const formatDateTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return d.toLocaleString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n};\n\nexport const formatRelativeTime = (date: Date | string): string => {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return 'Just now';\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;\n  }\n\n  return formatDate(d);\n};\n\nexport const isTokenExpired = (expiresAt: string): boolean => {\n  const expirationTime = new Date(expiresAt).getTime();\n  const currentTime = new Date().getTime();\n  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer\n  \n  return currentTime >= (expirationTime - bufferTime);\n};\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAElB,MAAM,aAAa,CAAC;IACzB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,cAAc,CAAC,SAAS;QAC/B,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,EAAE,OAAO,EAAE,IAAI;IAEjE,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,OAAO,EAAE,gBAAgB,IAAI,MAAM,GAAG,IAAI,CAAC;IACrE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,EAAE,cAAc,IAAI,MAAM,GAAG,IAAI,CAAC;IAC/D;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,IAAI,EAAE,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;IAC5D;IAEA,OAAO,WAAW;AACpB;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,iBAAiB,IAAI,KAAK,WAAW,OAAO;IAClD,MAAM,cAAc,IAAI,OAAO,OAAO;IACtC,MAAM,aAAa,IAAI,KAAK,MAAM,mBAAmB;IAErD,OAAO,eAAgB,iBAAiB;AAC1C", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/format.ts"], "sourcesContent": ["// Formatting utility functions\n\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport const formatNumber = (num: number): string => {\n  return new Intl.NumberFormat('en-US').format(num);\n};\n\nexport const formatCurrency = (amount: number, currency = 'USD'): string => {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n};\n\nexport const truncateText = (text: string, maxLength: number): string => {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n};\n\nexport const capitalizeFirst = (str: string): string => {\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const camelToTitle = (str: string): string => {\n  return str\n    .replace(/([A-Z])/g, ' $1')\n    .replace(/^./, (str) => str.toUpperCase())\n    .trim();\n};\n\nexport const generateSessionId = (): string => {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;AAExB,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,MAAM,iBAAiB,CAAC,QAAgB,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,MAAM,eAAe,CAAC,MAAc;IACzC,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,YAAY,OACpB,OAAO,CAAC,MAAM,CAAC,MAAQ,IAAI,WAAW,IACtC,IAAI;AACT;AAEO,MAAM,oBAAoB;IAC/B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/validation.ts"], "sourcesContent": ["// Validation utility functions\n\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const isValidPassword = (password: string): boolean => {\n  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number\n  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;\n  return passwordRegex.test(password);\n};\n\nexport const isValidUrl = (url: string): boolean => {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const isValidPort = (port: number): boolean => {\n  return port >= 1 && port <= 65535;\n};\n\nexport const isValidDatabaseName = (name: string): boolean => {\n  // Basic validation for database names\n  const dbNameRegex = /^[a-zA-Z][a-zA-Z0-9_-]*$/;\n  return dbNameRegex.test(name) && name.length >= 2 && name.length <= 50;\n};\n\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n\nexport const validateRequired = (value: any): boolean => {\n  if (typeof value === 'string') {\n    return value.trim().length > 0;\n  }\n  return value !== null && value !== undefined;\n};\n\nexport const validateMinLength = (value: string, minLength: number): boolean => {\n  return value.length >= minLength;\n};\n\nexport const validateMaxLength = (value: string, maxLength: number): boolean => {\n  return value.length <= maxLength;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;AAExB,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,kBAAkB,CAAC;IAC9B,4DAA4D;IAC5D,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,QAAQ,KAAK,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,CAAC;IAClC,sCAAsC;IACtC,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI;AACtE;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,MAAM,IAAI,GAAG,MAAM,GAAG;IAC/B;IACA,OAAO,UAAU,QAAQ,UAAU;AACrC;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB;AAEO,MAAM,oBAAoB,CAAC,OAAe;IAC/C,OAAO,MAAM,MAAM,IAAI;AACzB", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/lib/utils/index.ts"], "sourcesContent": ["// Main utility exports\n\nexport * from './cn';\nexport * from './date';\nexport * from './format';\nexport * from './validation';\n\n// Re-export the original cn function for backward compatibility\nexport { cn } from './cn';\n"], "names": [], "mappings": "AAAA,uBAAuB;;AAEvB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1   shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;AAAA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gbACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/components/features/auth/RegisterForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, FormEvent } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\n// import { Label } from '@/components/ui/label';\r\nimport { useAuth } from '@/providers/AuthContext';\r\nimport { Eye, EyeOff, Mail, Lock, User } from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nconst RegisterForm: React.FC = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [fullName, setFullName] = useState('');\r\n  const [confirmPassword, setConfirmPassword] = useState('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [errorType, setErrorType] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { register } = useAuth();\r\n\r\n  // Email validation\r\n  const isValidEmail = (email: string) => {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  };\r\n\r\n  // Password validation\r\n  const isValidPassword = (password: string) => {\r\n    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character\r\n    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\r\n    return passwordRegex.test(password);\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (!fullName.trim()) {\r\n      setError('Full name is required');\r\n      return false;\r\n    }\r\n\r\n    if (!email.trim()) {\r\n      setError('Email is required');\r\n      return false;\r\n    }\r\n\r\n    if (!isValidEmail(email)) {\r\n      setError('Please enter a valid email address');\r\n      return false;\r\n    }\r\n\r\n    if (!password) {\r\n      setError('Password is required');\r\n      return false;\r\n    }\r\n\r\n    if (!isValidPassword(password)) {\r\n      setError('Password must be at least 8 characters with uppercase, lowercase, number, and special character');\r\n      return false;\r\n    }\r\n\r\n    if (password !== confirmPassword) {\r\n      setError('Passwords do not match');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\r\n    event.preventDefault();\r\n    setError(null);\r\n    setErrorType(null);\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      await register({\r\n        email: email.trim(),\r\n        password,\r\n        full_name: fullName.trim(),\r\n      });\r\n    } catch (err: any) {\r\n      console.error('Registration failed', err);\r\n\r\n      // Handle structured error response from backend\r\n      if (err.response?.data?.error) {\r\n        const errorData = err.response.data.error;\r\n\r\n        // Special handling for email already exists\r\n        if (errorData.code === 'EMAIL_ALREADY_EXISTS') {\r\n          setError(errorData.message || 'An account with this email address already exists. Please use a different email or try logging in.');\r\n          setErrorType('EMAIL_ALREADY_EXISTS');\r\n          // Focus on email field to help user try a different email\r\n          setTimeout(() => {\r\n            const emailInput = document.getElementById('email');\r\n            if (emailInput) {\r\n              emailInput.focus();\r\n              (emailInput as HTMLInputElement).select();\r\n            }\r\n          }, 100);\r\n        } else {\r\n          setError(errorData.message || 'Registration failed. Please try again.');\r\n          setErrorType('GENERAL_ERROR');\r\n        }\r\n      }\r\n      // Handle error message from AuthContext (processed by auth-error-handling)\r\n      else if (err.message) {\r\n        setError(err.message);\r\n        setErrorType('GENERAL_ERROR');\r\n      }\r\n      // Fallback for any other error format\r\n      else {\r\n        setError('Registration failed. Please try again.');\r\n        setErrorType('GENERAL_ERROR');\r\n      }\r\n    }\r\n    setIsLoading(false);\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"space-y-5\">\r\n        {/* Full Name Field */}\r\n        <div className=\"relative group\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\r\n            <User className=\"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200\" />\r\n          </div>\r\n          <Input\r\n            id=\"fullName\"\r\n            type=\"text\"\r\n            value={fullName}\r\n            onChange={(e) => setFullName(e.target.value)}\r\n            placeholder=\"Full name\"\r\n            className=\"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500\"\r\n            required\r\n            disabled={isLoading}\r\n          />\r\n        </div>\r\n\r\n        {/* Email Field */}\r\n        <div className=\"relative group\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\r\n            <Mail className=\"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200\" />\r\n          </div>\r\n          <Input\r\n            id=\"email\"\r\n            type=\"email\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            placeholder=\"Email address\"\r\n            className=\"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500\"\r\n            required\r\n            disabled={isLoading}\r\n          />\r\n        </div>\r\n\r\n        {/* Password Field */}\r\n        <div className=\"relative group\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\r\n            <Lock className=\"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200\" />\r\n          </div>\r\n          <Input\r\n            id=\"password\"\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            value={password}\r\n            onChange={(e) => setPassword(e.target.value)}\r\n            placeholder=\"Password\"\r\n            className=\"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500\"\r\n            required\r\n            disabled={isLoading}\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setShowPassword(!showPassword)}\r\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n            disabled={isLoading}\r\n          >\r\n            {showPassword ? (\r\n              <EyeOff className=\"h-5 w-5\" />\r\n            ) : (\r\n              <Eye className=\"h-5 w-5\" />\r\n            )}\r\n          </button>\r\n        </div>\r\n\r\n        {/* Confirm Password Field */}\r\n        <div className=\"relative group\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\r\n            <Lock className=\"h-5 w-5 text-gray-500 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-200\" />\r\n          </div>\r\n          <Input\r\n            id=\"confirmPassword\"\r\n            type={showConfirmPassword ? \"text\" : \"password\"}\r\n            value={confirmPassword}\r\n            onChange={(e) => setConfirmPassword(e.target.value)}\r\n            placeholder=\"Confirm password\"\r\n            className=\"w-full pl-12 pr-14 py-4 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-blue-500/30 dark:focus:ring-blue-400/30 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-300 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 text-base shadow-sm dark:shadow-gray-900/20 hover:border-gray-300 dark:hover:border-gray-500\"\r\n            required\r\n            disabled={isLoading}\r\n          />\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100 transition-colors duration-200 z-10 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n            disabled={isLoading}\r\n          >\r\n            {showConfirmPassword ? (\r\n              <EyeOff className=\"h-5 w-5\" />\r\n            ) : (\r\n              <Eye className=\"h-5 w-5\" />\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Password Requirements */}\r\n      <div className=\"bg-blue-50 dark:bg-blue-900/30 border-2 border-blue-200 dark:border-blue-500/50 rounded-2xl p-4 shadow-sm\">\r\n        <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium mb-2\">Password requirements:</p>\r\n        <ul className=\"text-xs text-blue-600 dark:text-blue-400 space-y-1\">\r\n          <li>• At least 8 characters long</li>\r\n          <li>• Contains uppercase and lowercase letters</li>\r\n          <li>• Contains at least one number</li>\r\n          <li>• Contains at least one special character (@$!%*?&)</li>\r\n        </ul>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className={`border-2 rounded-2xl p-4 animate-in slide-in-from-top-2 duration-300 shadow-sm ${\r\n          errorType === 'EMAIL_ALREADY_EXISTS'\r\n            ? 'bg-amber-50 dark:bg-amber-900/30 border-amber-200 dark:border-amber-500/50'\r\n            : 'bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-500/50'\r\n        }`}>\r\n          <p className={`text-sm text-center font-medium ${\r\n            errorType === 'EMAIL_ALREADY_EXISTS'\r\n              ? 'text-amber-700 dark:text-amber-300'\r\n              : 'text-red-700 dark:text-red-300'\r\n          }`}>\r\n            {error}\r\n          </p>\r\n\r\n          {/* Show \"Go to Login\" button for email already exists */}\r\n          {errorType === 'EMAIL_ALREADY_EXISTS' && (\r\n            <div className=\"mt-3 text-center\">\r\n              <Link\r\n                href=\"/login\"\r\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-amber-700 dark:text-amber-300 bg-amber-100 dark:bg-amber-800/50 border border-amber-300 dark:border-amber-600 rounded-lg hover:bg-amber-200 dark:hover:bg-amber-800/70 transition-colors duration-200\"\r\n              >\r\n                Go to Login Page\r\n              </Link>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Submit Button */}\r\n      <Button \r\n        type=\"submit\" \r\n        className=\"w-full bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 shadow-lg shadow-blue-500/30 dark:shadow-blue-400/20 hover:shadow-blue-500/50 dark:hover:shadow-blue-400/30 hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:shadow-blue-500/25 text-base border-0\"\r\n        disabled={isLoading}\r\n      >\r\n        {isLoading ? (\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3\"></div>\r\n            Creating account...\r\n          </div>\r\n        ) : (\r\n          'Create account'\r\n        )}\r\n      </Button>\r\n\r\n      {/* Sign In Link */}\r\n      <div className=\"text-center pt-4\">\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n          Already have an account?{' '}\r\n          <Link href=\"/login\" className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 font-semibold hover:underline\">\r\n            Sign in\r\n          </Link>\r\n        </p>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default RegisterForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;AAUA,MAAM,eAAyB;;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAE3B,mBAAmB;IACnB,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,CAAC;QACvB,iFAAiF;QACjF,MAAM,gBAAgB;QACtB,OAAO,cAAc,IAAI,CAAC;IAC5B;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,aAAa,QAAQ;YACxB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,UAAU;YACb,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAC,gBAAgB,WAAW;YAC9B,SAAS;YACT,OAAO;QACT;QAEA,IAAI,aAAa,iBAAiB;YAChC,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,SAAS;QACT,aAAa;QAEb,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,SAAS;gBACb,OAAO,MAAM,IAAI;gBACjB;gBACA,WAAW,SAAS,IAAI;YAC1B;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YAErC,gDAAgD;YAChD,IAAI,IAAI,QAAQ,EAAE,MAAM,OAAO;gBAC7B,MAAM,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAEzC,4CAA4C;gBAC5C,IAAI,UAAU,IAAI,KAAK,wBAAwB;oBAC7C,SAAS,UAAU,OAAO,IAAI;oBAC9B,aAAa;oBACb,0DAA0D;oBAC1D,WAAW;wBACT,MAAM,aAAa,SAAS,cAAc,CAAC;wBAC3C,IAAI,YAAY;4BACd,WAAW,KAAK;4BACf,WAAgC,MAAM;wBACzC;oBACF,GAAG;gBACL,OAAO;oBACL,SAAS,UAAU,OAAO,IAAI;oBAC9B,aAAa;gBACf;YACF,OAEK,IAAI,IAAI,OAAO,EAAE;gBACpB,SAAS,IAAI,OAAO;gBACpB,aAAa;YACf,OAEK;gBACH,SAAS;gBACT,aAAa;YACf;QACF;QACA,aAAa;IACf;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,eAAe,SAAS;gCAC9B,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;0CAEZ,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAU;gCACV,UAAU;0CAET,6BACC,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAM,sBAAsB,SAAS;gCACrC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;0CAEZ,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,uBAAuB,CAAC;gCACvC,WAAU;gCACV,UAAU;0CAET,oCACC,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA4D;;;;;;kCACzE,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;YAKP,uBACC,6LAAC;gBAAI,WAAW,CAAC,+EAA+E,EAC9F,cAAc,yBACV,+EACA,sEACJ;;kCACA,6LAAC;wBAAE,WAAW,CAAC,gCAAgC,EAC7C,cAAc,yBACV,uCACA,kCACJ;kCACC;;;;;;oBAIF,cAAc,wCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAST,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,WAAU;gBACV,UAAU;0BAET,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;wBAAuF;;;;;;2BAIxG;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAA2C;wBAC7B;sCACzB,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAA6I;;;;;;;;;;;;;;;;;;;;;;;AAOrL;GApRM;;QAUiB,mIAAA,CAAA,UAAO;;;KAVxB;uCAsRS", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/NXTHUMANS/internal-solutions-nxtwebsite/AgentReport%202/AgentReportUI/src/app/%28auth%29/register/page.tsx"], "sourcesContent": ["'use client';\n\nimport RegisterForm from '@/components/features/auth/RegisterForm';\nimport Link from 'next/link';\nimport React from 'react';\n\nconst RegisterPage: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header with logo */}\n      <div className=\"absolute top-0 left-0 p-6 z-20\">\n        <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n          <div className=\"flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm\">\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" className=\"w-5 h-5 text-white\">\n              <path\n                d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n          </div>\n          <span className=\"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200\">\n            Agent\n          </span>\n        </Link>\n      </div>\n      \n      {/* Main content */}\n      <div className=\"min-h-screen flex items-center justify-center p-6\">\n        <div className=\"w-full max-w-md\">\n          {/* Glassmorphism card */}\n          <div className=\"backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl\">\n            {/* Header section */}\n            <div className=\"p-8 pb-0 text-center\">\n              <h1 className=\"text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight\">\n                Create your account\n              </h1>\n              <p className=\"text-gray-500 dark:text-gray-400 font-light\">\n                Join us and start your journey\n              </p>\n            </div>\n\n            {/* Form section */}\n            <div className=\"p-8\">\n              <RegisterForm />\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"text-center mt-8\">\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              By creating an account, you agree to our{' '}\n              <a href=\"#\" className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline\">\n                Terms of Service\n              </a>{' '}\n              and{' '}\n              <a href=\"#\" className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline\">\n                Privacy Policy\n              </a>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMA,MAAM,eAAyB;IAC7B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,SAAQ;gCAAY,MAAK;gCAAO,WAAU;0CAC7C,cAAA,6LAAC;oCACC,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,eAAc;oCACd,gBAAe;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAK,WAAU;sCAA+G;;;;;;;;;;;;;;;;;0BAOnI,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwE;;;;;;sDAGtF,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;8CAM7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAA2C;oCACb;kDACzC,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA+H;;;;;;oCAEhJ;oCAAI;oCACL;kDACJ,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA+H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnK;KA7DM;uCA+DS", "debugId": null}}]}