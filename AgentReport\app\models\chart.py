"""Chart Models

This module defines the chart-related models used for the chart selection API endpoint.
These models align with the frontend TypeScript interfaces for seamless integration.
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator


class ChartType(str, Enum):
    """Enumeration of supported chart types that match frontend TypeScript interface."""
    TABLE = "table"
    LINE = "line"
    TIMEBAR = "timebar"
    BAR = "bar"
    FUNNEL = "funnel"
    NUMBER = "number"
    IMAGE = "image"
    DETAIL = "detail"
    TEXT = "text"
    PIE = "pie"
    ACTIVITY = "activity"


class ChartDataPoint(BaseModel):
    """Model representing a single data point in a chart."""
    name: str = Field(..., description="The name/label for this data point (Recharts compatible)")
    value: Union[int, float] = Field(..., description="The numeric value for this data point")
    category: Optional[str] = Field(None, description="Optional category for grouping")

    @validator('value')
    def validate_value(cls, v):
        """Ensure value is a valid number."""
        if not isinstance(v, (int, float)) or (isinstance(v, float) and not (v == v)):  # Check for NaN
            raise ValueError("Value must be a valid number")
        return v


class ChartMetadata(BaseModel):
    """Model representing chart metadata and configuration."""
    xAxisLabel: Optional[str] = Field(None, description="Label for the X-axis (only for charts that use axes)")
    yAxisLabel: Optional[str] = Field(None, description="Label for the Y-axis (only for charts that use axes)")
    colors: Optional[List[str]] = Field(None, description="Default color palette")
    sqlQuery: Optional[str] = Field(None, description="SQL query used to generate the data")
    description: Optional[str] = Field(None, description="Additional description or context")
    dataSource: Optional[str] = Field(None, description="Source of the data")
    generatedAt: Optional[str] = Field(None, description="Timestamp when chart was generated")
    chartSpecificConfig: Optional[Dict[str, Any]] = Field(None, description="Chart-type specific configuration")
    
    @validator('colors')
    def validate_colors(cls, v):
        """Validate color format (basic hex color validation)."""
        if v is not None:
            for color in v:
                if not isinstance(color, str) or not color.startswith('#'):
                    raise ValueError("Colors must be hex color strings starting with #")
        return v


# Chart-specific data formats for frontend consumption
class PieChartData(BaseModel):
    """Expected data format for pie charts - matches Recharts PieChart requirements."""
    data: List[Dict[str, Union[str, float]]] = Field(..., description="Array of objects with 'name' and 'value' properties")
    
    @validator('data')
    def validate_pie_data_structure(cls, v):
        """Ensure each data item has name and value properties."""
        for item in v:
            if 'name' not in item or 'value' not in item:
                raise ValueError("Pie chart data must have 'name' and 'value' properties")
            if not isinstance(item['name'], str):
                raise ValueError("Pie chart 'name' must be a string")
            if not isinstance(item['value'], (int, float)):
                raise ValueError("Pie chart 'value' must be a number")
        return v

class BarChartData(BaseModel):
    """Expected data format for bar charts - matches Recharts BarChart requirements."""
    data: List[Dict[str, Union[str, int, float]]] = Field(..., description="Array of objects with category and value properties")
    categoryKey: str = Field(default="name", description="Property name for category labels")
    valueKey: str = Field(default="value", description="Property name for values")

class LineChartData(BaseModel):
    """Expected data format for line charts - matches Recharts LineChart requirements."""
    data: List[Dict[str, Union[str, int, float]]] = Field(..., description="Array of objects with x and y properties")
    xKey: str = Field(default="name", description="Property name for x-axis values")
    yKey: str = Field(default="value", description="Property name for y-axis values")

class TimeBarChartData(BaseModel):
    """Expected data format for time bar charts - optimized for time intervals."""
    data: List[Dict[str, Union[str, int, float]]] = Field(..., description="Array of objects with time and value properties")
    timeKey: str = Field(default="time", description="Property name for time intervals")
    valueKey: str = Field(default="value", description="Property name for duration or count values")

class NumberChartData(BaseModel):
    """Expected data format for number displays."""
    value: Union[int, float] = Field(..., description="The primary numeric value")
    name: str = Field(..., description="Name/description for the number")
    formatType: str = Field(default="number", description="Format type: number, currency, percentage")
    color: Optional[str] = Field(None, description="Color for the number display")

class TableChartData(BaseModel):
    """Expected data format for table displays."""
    columns: List[Dict[str, str]] = Field(..., description="Column definitions with key and label")
    data: List[Dict[str, Any]] = Field(..., description="Table data as array of objects")
    
    @validator('columns')
    def validate_columns(cls, v):
        """Ensure columns have required structure."""
        for col in v:
            if 'key' not in col or 'label' not in col:
                raise ValueError("Table columns must have 'key' and 'label' properties")
        return v

class FunnelChartData(BaseModel):
    """Expected data format for funnel charts."""
    data: List[Dict[str, Union[str, int, float]]] = Field(..., description="Array of objects with name and value properties")
    nameKey: str = Field(default="name", description="Property name for stage names")
    valueKey: str = Field(default="value", description="Property name for values")

class ActivityChartData(BaseModel):
    """Expected data format for activity charts."""
    data: List[Dict[str, Union[str, int]]] = Field(..., description="Array of objects with time and count properties")
    timeKey: str = Field(default="time", description="Property name for time periods")
    countKey: str = Field(default="count", description="Property name for activity counts")

class TextChartData(BaseModel):
    """Expected data format for text displays."""
    content: str = Field(..., description="Text content to display")
    formatType: str = Field(default="plain", description="Format type: plain, markdown, html")

class ImageChartData(BaseModel):
    """Expected data format for image displays."""
    imageUrl: str = Field(..., description="URL or path to the image")
    altText: Optional[str] = Field(None, description="Alternative text for the image")
    caption: Optional[str] = Field(None, description="Image caption")

class DetailChartData(BaseModel):
    """Expected data format for detail displays."""
    title: str = Field(..., description="Detail view title")
    data: List[Dict[str, Any]] = Field(..., description="Detailed data as array of objects")
    layout: Optional[str] = Field("vertical", description="Layout type: vertical, horizontal, grid")


class ChartData(BaseModel):
    """Model representing complete chart data structure."""
    title: str = Field(..., description="Title of the chart")
    chartType: ChartType = Field(..., description="Type of chart to render")
    data: Union[
        List[ChartDataPoint],  # Legacy format for backward compatibility
        PieChartData,
        BarChartData, 
        LineChartData,
        TimeBarChartData,
        NumberChartData,
        TableChartData,
        FunnelChartData,
        ActivityChartData,
        TextChartData,
        ImageChartData,
        DetailChartData
    ] = Field(..., description="Chart data in the appropriate format for the chart type")
    metadata: ChartMetadata = Field(default_factory=ChartMetadata, description="Chart metadata and configuration")

    @validator('data')
    def validate_data_not_empty(cls, v):
        """Ensure data is not empty."""
        if isinstance(v, list) and not v:
            raise ValueError("Chart data cannot be empty")
        elif hasattr(v, '__dict__') and not any(v.__dict__.values()):
            raise ValueError("Chart data cannot be empty")
        return v


class ChartQueryRequest(BaseModel):
    """Model for chart query requests from the frontend."""
    prompt: str = Field(..., min_length=1, max_length=4000, description="User query/prompt for chart generation")
    user_id: Optional[str] = Field(None, description="Optional user ID for personalization")
    dashboard_id: Optional[str] = Field(None, description="Optional dashboard ID for context")
    database_id: str = Field(..., description="Required database ID to query for chart data")

    @validator('prompt')
    def validate_prompt(cls, v):
        """Validate and clean the prompt."""
        if not v or not v.strip():
            raise ValueError("Prompt cannot be empty")
        return v.strip()


class ChartQueryResponse(BaseModel):
    """Model for chart query responses to the frontend."""
    success: bool = Field(..., description="Whether the request was successful")
    data: Optional[ChartData] = Field(None, description="Chart data if successful")
    error: Optional[str] = Field(None, description="Error message if unsuccessful")
    
    @validator('data')
    def validate_success_data_consistency(cls, v, values):
        """Ensure data is present when success is True."""
        if values.get('success') and not v:
            raise ValueError("Data must be provided when success is True")
        return v
    
    @validator('error')
    def validate_error_success_consistency(cls, v, values):
        """Ensure error is present when success is False."""
        if not values.get('success') and not v:
            raise ValueError("Error message must be provided when success is False")
        return v


class ChartGenerationContext(BaseModel):
    """Internal model for chart generation context and LLM processing."""
    user_query: str = Field(..., description="Original user query")
    user_id: Optional[str] = Field(None, description="User ID for context")
    dashboard_id: Optional[str] = Field(None, description="Dashboard ID for context")
    recommended_chart_type: Optional[ChartType] = Field(None, description="LLM recommended chart type")
    data_requirements: Optional[Dict[str, Any]] = Field(None, description="Data requirements identified by LLM")
    processing_notes: Optional[List[str]] = Field(None, description="Processing notes and decisions")


class ChartTypeRecommendation(BaseModel):
    """Model for LLM chart type recommendations."""
    chart_type: ChartType = Field(..., description="Recommended chart type")
    title: str = Field(..., description="Generated chart title")


class MockDataGenerationConfig(BaseModel):
    """Configuration for mock data generation during development."""
    data_points_count: int = Field(default=5, ge=1, le=50, description="Number of data points to generate")
    value_range: tuple[int, int] = Field(default=(10, 1000), description="Range for generated values")
    use_realistic_labels: bool = Field(default=True, description="Whether to use realistic labels")
    include_categories: bool = Field(default=False, description="Whether to include categories")


# Default chart colors that match frontend constants
DEFAULT_CHART_COLORS = [
    '#8b5cf6',  # Purple
    '#06b6d4',  # Cyan
    '#10b981',  # Emerald
    '#f59e0b',  # Amber
    '#ef4444',  # Red
    '#8b5cf6',  # Purple variant
    '#06b6d4',  # Cyan variant
    '#10b981',  # Emerald variant
    '#f59e0b',  # Amber variant
    '#ef4444',  # Red variant
]


# Chart type to description mapping for LLM prompts
CHART_TYPE_DESCRIPTIONS = {
    ChartType.TABLE: "Tables are suitable for detailed data display with multiple columns and precise values",
    ChartType.LINE: "Line charts are perfect for showing trends and changes over continuous time periods",
    ChartType.TIMEBAR: "Time bar charts show data changes over specific time intervals and periods",
    ChartType.BAR: "Bar charts are ideal for comparing discrete categories or showing changes over time with distinct periods",
    ChartType.FUNNEL: "Funnel charts are ideal for showing conversion rates, process stages, and sequential data",
    ChartType.NUMBER: "Number displays are perfect for showing single key metrics, KPIs, and summary statistics",
    ChartType.IMAGE: "Image displays are used for showing visual content, photos, graphics, or visual documentation",
    ChartType.DETAIL: "Detail displays are used for comprehensive information views, drill-down data, and detailed analysis",
    ChartType.TEXT: "Text displays are used for descriptive information, summaries, explanations, and narrative content",
    ChartType.PIE: "Pie charts are best for showing parts of a whole, percentage breakdowns, and composition analysis",
    ChartType.ACTIVITY: "Activity charts show events, actions, timelines, and activity patterns over time",
}


# SQL query expectations for each chart type
CHART_SQL_REQUIREMENTS = {
    ChartType.PIE: {
        "columns": 2,
        "description": "SELECT category_name, value FROM table GROUP BY category_name",
        "requirements": ["Categories should be meaningful groupings", "Values should be positive numbers"]
    },
    ChartType.BAR: {
        "columns": 2,
        "description": "SELECT category_name, value FROM table GROUP BY category_name",
        "requirements": ["Categories should be meaningful groupings", "Values can be any numeric type"]
    },
    ChartType.LINE: {
        "columns": 2,
        "description": "SELECT time_period, value FROM table GROUP BY time_period ORDER BY time_period",
        "requirements": ["First column should be time-based (dates, months, etc.)", "Results should be ordered chronologically"]
    },
    ChartType.TIMEBAR: {
        "columns": 2,
        "description": "SELECT time_interval, duration_or_count FROM table",
        "requirements": ["First column should be time intervals", "Second column should be duration or count"]
    },
    ChartType.TABLE: {
        "columns": "2+",
        "description": "SELECT col1, col2, col3, ... FROM table",
        "requirements": ["Multiple columns allowed", "Headers should be meaningful", "Data should be tabular"]
    },
    ChartType.NUMBER: {
        "columns": 1,
        "description": "SELECT aggregate_function(column) FROM table",
        "requirements": ["Single numeric value", "Should use COUNT, SUM, AVG, etc."]
    },
    ChartType.FUNNEL: {
        "columns": 2,
        "description": "SELECT stage_name, count FROM table GROUP BY stage_name ORDER BY stage_order",
        "requirements": ["Stages should represent a process flow", "Values should generally decrease"]
    },
    ChartType.ACTIVITY: {
        "columns": 2,
        "description": "SELECT time_period, activity_count FROM table GROUP BY time_period ORDER BY time_period",
        "requirements": ["Time-based activity tracking", "Counts represent events or actions"]
    },
    ChartType.TEXT: {
        "columns": 1,
        "description": "SELECT text_content FROM table",
        "requirements": ["Single text field", "Should contain descriptive information"]
    },
    ChartType.IMAGE: {
        "columns": "1-2",
        "description": "SELECT image_url, optional_caption FROM table",
        "requirements": ["Image URL or path", "Optional caption or description"]
    },
    ChartType.DETAIL: {
        "columns": "2+",
        "description": "SELECT key, value FROM table or multiple columns for detailed view",
        "requirements": ["Key-value pairs or structured data", "Should provide comprehensive information"]
    }
}
