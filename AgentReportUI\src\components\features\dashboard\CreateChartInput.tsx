"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Send, Sparkles, Database, ChevronDown } from 'lucide-react';
import { useDataSources } from '@/providers/DataSourcesContext';

interface CreateChartInputProps {
  onSubmit: (prompt: string, databaseId: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
  placeholder?: string;
}

const CreateChartInput: React.FC<CreateChartInputProps> = ({
  onSubmit,
  isLoading = false,
  className = '',
  placeholder = 'Describe your chart...',
}) => {
  const [prompt, setPrompt] = useState('');
  const [selectedDatabase, setSelectedDatabase] = useState<string>('');
  const inputRef = useRef<HTMLInputElement>(null);
  const { connectedDataSources, isLoading: isLoadingDataSources } = useDataSources();

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim() || isLoading) return;

    // Require database connection since backend validates this
    if (connectedDataSources.length === 0 || !selectedDatabase) {
      return;
    }

    try {
      await onSubmit(prompt.trim(), selectedDatabase);
      setPrompt('');
      // Keep the selected database for next chart
    } catch (err) {
      console.error('Failed to create chart:', err);
    }
  }, [prompt, selectedDatabase, onSubmit, isLoading, connectedDataSources.length]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit(e as any);
    }
  }, [handleSubmit]);

  // Auto-focus on mount and auto-select first database if available
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // Auto-select first database if available and none selected
    if (connectedDataSources.length > 0 && !selectedDatabase) {
      setSelectedDatabase(connectedDataSources[0].id);
    }
  }, [connectedDataSources, selectedDatabase]);

  const canSubmit = prompt.trim() && !isLoading &&
    connectedDataSources.length > 0 && selectedDatabase;

  const hasMultipleDatabases = connectedDataSources.length > 1;

  return (
    <div className={`w-full h-full flex flex-col justify-end p-3 pb-2 ${className}`}>
      {/* Input form positioned at absolute bottom */}
      <div className="w-full max-w-lg mx-auto space-y-3">
        {/* Database selector - only show if there are connected databases */}
        {connectedDataSources.length > 0 && (
          <div className="space-y-2">
            {hasMultipleDatabases && (
              <div className="flex items-center gap-2 text-xs" style={{ color: 'var(--sidebar-text-secondary)' }}>
                <Database className="h-3 w-3" />
                <span>Select Database:</span>
              </div>
            )}
            <Select
              value={selectedDatabase}
              onValueChange={setSelectedDatabase}
              disabled={isLoading || isLoadingDataSources}
            >
              <SelectTrigger
                className="h-8 text-xs border react-grid-no-drag"
                style={{
                  backgroundColor: 'var(--sidebar-surface-secondary)',
                  borderColor: 'var(--sidebar-border)',
                  color: 'var(--sidebar-text-primary)',
                }}
                onMouseDown={(e) => e.stopPropagation()}
              >
                <div className="flex items-center gap-2">
                  <Database className="h-3 w-3" style={{ color: 'var(--sidebar-text-secondary)' }} />
                  <SelectValue placeholder="Choose database..." />
                </div>
              </SelectTrigger>
              <SelectContent>
                {connectedDataSources.map((db) => (
                  <SelectItem key={db.id} value={db.id}>
                    <div className="flex items-center gap-2">
                      <Database className="h-3 w-3" />
                      <span>{db.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div
            className="relative rounded border react-grid-no-drag"
            style={{
              backgroundColor: 'var(--sidebar-surface-secondary)',
              borderColor: 'var(--sidebar-border)',
            }}
            onMouseDown={(e) => e.stopPropagation()}
          >
            <Input
              ref={inputRef}
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={isLoading}
              placeholder={placeholder}
              className="border-0 bg-transparent text-xs leading-tight placeholder:text-gray-500 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 h-7 pr-8"
              style={{
                color: 'var(--sidebar-text-primary)',
              }}
              maxLength={1000}
            />

            {/* Submit button positioned in right side */}
            <div className="absolute right-1 top-1/2 transform -translate-y-1/2">
              <Button
                type="submit"
                disabled={!canSubmit}
                size="sm"
                className={`h-5 w-5 p-0 rounded border-0 transition-all duration-200 ${
                  canSubmit ? 'hover:scale-105' : 'opacity-40'
                }`}
                style={{
                  backgroundColor: canSubmit ? 'rgba(59, 130, 246, 1)' : 'var(--sidebar-surface-tertiary)',
                  color: canSubmit ? 'white' : 'var(--sidebar-text-tertiary)',
                }}
                onMouseEnter={(e) => {
                  if (canSubmit) {
                    e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.9)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (canSubmit) {
                    e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 1)';
                  }
                }}
              >
                <Send className="h-2.5 w-2.5" />
              </Button>
            </div>
          </div>
        </form>

        {/* Loading state */}
        {isLoading && (
          <div className="flex items-center justify-center space-x-1 py-1 mt-1">
            <div
              className="h-0.5 w-0.5 rounded-full animate-pulse"
              style={{ backgroundColor: 'rgba(59, 130, 246, 0.8)' }}
            />
            <div
              className="h-0.5 w-0.5 rounded-full animate-pulse"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                animationDelay: '0.2s'
              }}
            />
            <div
              className="h-0.5 w-0.5 rounded-full animate-pulse"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                animationDelay: '0.4s'
              }}
            />
          </div>
        )}

        {/* No databases message */}
        {connectedDataSources.length === 0 && !isLoadingDataSources && (
          <div className="text-center py-2">
            <p className="text-xs" style={{ color: 'var(--sidebar-text-secondary)' }}>
              No databases connected. Please connect a database to create charts.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateChartInput;
