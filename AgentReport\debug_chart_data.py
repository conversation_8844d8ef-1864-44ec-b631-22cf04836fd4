#!/usr/bin/env python3
"""
Debug script to see what data is actually being returned from the database.
"""

import asyncio
import sys
import os
import pandas as pd

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.chart_service import ChartSelectionService
from app.services.database_service import DatabaseService
from app.services.database_manager_service import DatabaseManagerService
from app.models.chart import ChartQueryRequest, ChartType

async def debug_chart_data():
    """Debug what data is actually being returned."""
    print("🔍 Debugging chart data generation...")
    
    # Initialize services
    chart_service = ChartSelectionService()
    database_service = DatabaseService()
    database_manager = DatabaseManagerService()
    
    user_id = "ef947981-0766-49ed-928b-3d39d3ee11f0"
    database_id = "db_6fa39d8a"
    
    try:
        # Step 1: Test database schema retrieval
        print("\n📋 Step 1: Getting database schema...")
        schema_info = await database_manager.get_database_schema(user_id)
        print(f"Schema info: {len(schema_info.get('databases', []))} databases found")
        
        # Find our database
        database_info = None
        for db in schema_info.get("databases", []):
            if db["id"] == database_id:
                database_info = db
                print(f"Found database: {db['name']} with {len(db.get('tables', []))} tables")
                for table in db.get('tables', []):
                    print(f"  - Table: {table['name']} with columns: {table.get('columns', [])}")
                break
        
        if not database_info:
            print(f"❌ Database {database_id} not found!")
            return
            
        # Step 2: Generate SQL query
        print("\n🔧 Step 2: Generating SQL query...")
        # Test with the users table which we know has data
        sql_query = await chart_service._generate_sql_for_chart_with_schema(
            "Show me users by their role",
            database_info,
            ChartType.BAR
        )
        print(f"Generated SQL: {sql_query}")
        
        # Step 3: Check what tables actually exist and have data
        print("\n💾 Step 3: Checking database content...")

        # Get database credentials
        database_obj = await database_manager.credential_service.get_credentials(user_id, database_id)
        if not database_obj:
            print(f"❌ Could not get credentials for database {database_id}")
            return

        # Connect to database
        database_service = DatabaseService()
        success, error = await database_service.connect_database(database_obj, user_id=user_id)
        if not success:
            print(f"❌ Failed to connect to database: {error}")
            return

        # Check if the tables mentioned in the query actually exist and have data
        tables_to_check = ['movies', 'rentals', 'payments']
        for table in tables_to_check:
            try:
                # Check if table exists and count rows
                count_query = f"SELECT COUNT(*) as count FROM {table}"
                result = await database_service.execute_query(database_id, count_query)
                if not result.empty:
                    count = result.iloc[0]['count']
                    print(f"📊 Table '{table}': {count} rows")

                    # Show a few sample rows if table has data
                    if count > 0:
                        sample_query = f"SELECT * FROM {table} LIMIT 3"
                        sample_result = await database_service.execute_query(database_id, sample_query)
                        print(f"   Sample data: {sample_result.to_dict('records')}")
                else:
                    print(f"📊 Table '{table}': No data returned")
            except Exception as e:
                print(f"❌ Error checking table '{table}': {e}")

        # Now test the original query
        print("\n🔍 Step 4: Testing the original chart query...")
        original_query = """SELECT
    m.title,
    ROUND(SUM(p.amount), 2) as total_revenue,
    COUNT(r.rental_id) as total_rentals
FROM
    movies m
JOIN
    rentals r ON m.movie_id = r.movie_id
JOIN
    payments p ON r.rental_id = p.rental_id
GROUP BY
    m.movie_id, m.title
ORDER BY
    total_revenue DESC
LIMIT 20"""

        try:
            result = await database_service.execute_query(database_id, original_query)
            print(f"Query result type: {type(result)}")
            print(f"Query result shape: {result.shape if hasattr(result, 'shape') else 'No shape'}")
            print(f"Query result empty: {result.empty if hasattr(result, 'empty') else 'No empty attr'}")
            if hasattr(result, 'to_dict'):
                print(f"Query result data: {result.to_dict('records')}")
        except Exception as e:
            print(f"❌ Error executing original query: {e}")
            
        # Connect to database
        success, error = await database_service.connect_database(database_obj, user_id=user_id)
        if not success:
            print(f"❌ Failed to connect to database: {error}")
            return
            
        print("✅ Connected to database successfully")
        
        # Execute the query
        query_result = await database_service.execute_query(database_id, sql_query)
        print(f"Query result type: {type(query_result)}")
        print(f"Query result: {query_result}")
        
        # Step 4: Test data transformation
        print("\n🔄 Step 4: Testing data transformation...")
        if hasattr(query_result, 'to_dict'):
            print("Result is a DataFrame:")
            print(f"Shape: {query_result.shape}")
            print(f"Columns: {query_result.columns.tolist()}")
            print(f"Data types: {query_result.dtypes.to_dict()}")
            print("Data:")
            print(query_result.to_string())
        elif isinstance(query_result, list):
            print(f"Result is a list with {len(query_result)} items:")
            for i, item in enumerate(query_result):
                print(f"  [{i}]: {item}")
        else:
            print(f"Result is: {query_result}")
            
        # Step 5: Transform to chart data
        print("\n📊 Step 5: Transforming to chart data...")
        data_points = chart_service._transform_query_result_to_chart_data(query_result, ChartType.BAR)
        print(f"Generated {len(data_points)} data points:")
        for point in data_points:
            print(f"  - {point.name}: {point.value}")
            
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_chart_data())
