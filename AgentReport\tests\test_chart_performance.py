"""
Performance tests for chart query endpoint optimizations.
"""
import asyncio
import time
import pytest
from unittest.mock import Mock, AsyncMock, patch
from app.services.chart_service import ChartService
from app.models.chart import ChartQueryRequest, ChartGenerationContext, ChartType
from app.models.database import Database, DatabaseType, DatabaseCredentials


class TestChartPerformance:
    """Test performance improvements in chart generation."""

    @pytest.fixture
    def mock_cache_service(self):
        """Mock cache service for testing."""
        cache_service = Mock()
        cache_service.get_cached_data = Mock(return_value=None)
        cache_service.cache_data = Mock()
        return cache_service

    @pytest.fixture
    def mock_database_manager(self):
        """Mock database manager for testing."""
        manager = Mock()
        manager.credential_service = Mock()
        manager.database_service = Mock()
        return manager

    @pytest.fixture
    def chart_service(self, mock_cache_service, mock_database_manager):
        """Create chart service with mocked dependencies."""
        service = ChartService()
        service.cache_service = mock_cache_service
        service.database_manager = mock_database_manager
        return service

    @pytest.mark.asyncio
    async def test_database_schema_caching(self, chart_service, mock_cache_service):
        """Test that database schema is properly cached."""
        user_id = "test_user"
        database_id = "test_db"
        
        # Mock database info
        mock_db_info = {
            "id": database_id,
            "name": "Test DB",
            "type": "postgresql",
            "tables": [
                {
                    "name": "users",
                    "columns": [
                        {"name": "id", "type": "integer", "primary_key": True},
                        {"name": "name", "type": "varchar", "primary_key": False}
                    ]
                }
            ]
        }

        # First call - should fetch from database
        mock_cache_service.get_cached_data.return_value = None
        with patch.object(chart_service, '_get_single_database_schema', return_value=mock_db_info) as mock_fetch:
            result1 = await chart_service._get_database_info(user_id, database_id)
            
            assert result1 == mock_db_info
            mock_fetch.assert_called_once()
            mock_cache_service.cache_data.assert_called_once()

        # Second call - should use cache
        mock_cache_service.get_cached_data.return_value = mock_db_info
        mock_fetch.reset_mock()
        mock_cache_service.cache_data.reset_mock()
        
        result2 = await chart_service._get_database_info(user_id, database_id)
        
        assert result2 == mock_db_info
        mock_fetch.assert_not_called()
        mock_cache_service.cache_data.assert_not_called()

    @pytest.mark.asyncio
    async def test_query_result_caching(self, chart_service, mock_cache_service):
        """Test that query results are properly cached."""
        context = ChartGenerationContext(
            user_query="What are our top performing films?",
            user_id="test_user",
            dashboard_id=None
        )
        chart_type = ChartType.BAR
        database_id = "test_db"
        user_id = "test_user"
        
        mock_data_points = [{"name": "Film A", "value": 100}]
        mock_sql_query = "SELECT title, revenue FROM films ORDER BY revenue DESC LIMIT 10"
        
        # Mock the database info
        mock_db_info = {"id": database_id, "tables": []}
        
        # First call - should execute query
        mock_cache_service.get_cached_data.return_value = None
        with patch.object(chart_service, '_get_database_info', return_value=mock_db_info), \
             patch.object(chart_service, '_generate_sql_for_chart_with_schema', return_value=mock_sql_query), \
             patch.object(chart_service, '_execute_database_query', return_value=mock_data_points), \
             patch.object(chart_service, '_transform_query_result_to_structured_data', return_value=mock_data_points), \
             patch.object(chart_service, '_post_process_single_data_point'):
            
            result1 = await chart_service._generate_real_chart_data_with_info(
                context, chart_type, database_id, user_id, mock_db_info
            )
            
            assert result1[0] == mock_data_points
            assert result1[1] == mock_sql_query
            mock_cache_service.cache_data.assert_called_once()

        # Second call - should use cache
        cached_result = {"data_points": mock_data_points, "sql_query": mock_sql_query}
        mock_cache_service.get_cached_data.return_value = cached_result
        
        with patch.object(chart_service, '_execute_database_query') as mock_execute:
            result2 = await chart_service._generate_real_chart_data_with_info(
                context, chart_type, database_id, user_id, mock_db_info
            )
            
            assert result2[0] == mock_data_points
            assert result2[1] == mock_sql_query
            mock_execute.assert_not_called()

    @pytest.mark.asyncio
    async def test_parallel_processing_performance(self, chart_service):
        """Test that parallel processing improves performance."""
        
        # Mock slow operations
        async def slow_chart_recommendation(context):
            await asyncio.sleep(0.1)  # Simulate 100ms delay
            return Mock(chart_type=ChartType.BAR)
        
        async def slow_database_info(user_id, database_id):
            await asyncio.sleep(0.1)  # Simulate 100ms delay
            return {"id": database_id, "tables": []}
        
        context = ChartGenerationContext(
            user_query="Test query",
            user_id="test_user",
            dashboard_id=None
        )
        
        with patch.object(chart_service, '_get_chart_type_recommendation', side_effect=slow_chart_recommendation), \
             patch.object(chart_service, '_get_database_info', side_effect=slow_database_info), \
             patch.object(chart_service, '_generate_real_chart_data_with_info', return_value=([], "SELECT 1")):
            
            # Measure time for parallel execution
            start_time = time.time()
            
            # Simulate the parallel execution from process_chart_query
            recommendation_task = asyncio.create_task(chart_service._get_chart_type_recommendation(context))
            database_info_task = asyncio.create_task(chart_service._get_database_info("test_user", "test_db"))
            
            recommendation, database_info = await asyncio.gather(recommendation_task, database_info_task)
            
            parallel_time = time.time() - start_time
            
            # Parallel execution should be close to 100ms (not 200ms)
            assert parallel_time < 0.15  # Allow some overhead
            assert parallel_time > 0.09   # Should still take at least the longest operation

    @pytest.mark.asyncio
    async def test_performance_benchmark(self, chart_service):
        """Benchmark chart query performance with optimizations."""
        
        # Mock all dependencies for fast execution
        mock_recommendation = Mock(chart_type=ChartType.BAR)
        mock_db_info = {
            "id": "test_db",
            "tables": [{"name": "test_table", "columns": []}]
        }
        mock_data_points = [{"name": "Test", "value": 100}]
        mock_sql = "SELECT * FROM test_table"
        
        request = ChartQueryRequest(
            prompt="Test query",
            user_id="test_user",
            database_id="test_db"
        )
        
        with patch.object(chart_service, '_get_chart_type_recommendation', return_value=mock_recommendation), \
             patch.object(chart_service, '_get_database_info', return_value=mock_db_info), \
             patch.object(chart_service, '_generate_real_chart_data_with_info', return_value=(mock_data_points, mock_sql)):
            
            # Measure execution time
            start_time = time.time()
            response = await chart_service.process_chart_query(request)
            execution_time = time.time() - start_time
            
            # With optimizations, this should be very fast (under 100ms for mocked operations)
            assert execution_time < 0.1
            assert response.success is True
            assert response.data is not None

    def test_cache_key_generation(self, chart_service):
        """Test that cache keys are generated consistently."""
        import hashlib
        
        user_query = "What are our top films?"
        chart_type = ChartType.BAR
        database_id = "test_db"
        user_id = "test_user"
        
        # Generate cache key manually
        query_hash = hashlib.md5(f"{user_query}:{chart_type.value}:{database_id}".encode()).hexdigest()
        expected_key = f"chart_query_result:{user_id}:{query_hash}"
        
        # This should match the key generation in the actual code
        assert len(expected_key) > 0
        assert user_id in expected_key
        assert "chart_query_result" in expected_key


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
