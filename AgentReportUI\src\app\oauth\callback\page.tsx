'use client';

import { useEffect, useState, useRef, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/providers/AuthContext';

function OAuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { handleOAuthCallback, isNewUser, isLoading } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');
  const [redirecting, setRedirecting] = useState<boolean>(false);
  const [userStateReady, setUserStateReady] = useState<boolean>(false);
  const hasProcessed = useRef(false);

  useEffect(() => {
    // Prevent multiple executions
    if (hasProcessed.current) {
      return;
    }

    const handleCallback = async () => {
      try {
        hasProcessed.current = true;
        console.log('OAuth callback page loaded');
        console.log('Current URL:', window.location.href);
        console.log('Search params:', Object.fromEntries(searchParams.entries()));

        // Check for OAuth errors first
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          let errorMessage = 'Authentication failed. Please try again.';
          
          if (error === 'code_already_used') {
            errorMessage = 'Authorization code has already been used. Please try signing in again.';
          } else if (error === 'oauth_failed') {
            errorMessage = 'OAuth authentication failed. Please try again.';
          } else if (error === 'missing_tokens') {
            errorMessage = 'Authentication tokens are missing. Please try again.';
          } else {
            errorMessage = `OAuth Error: ${error} - ${errorDescription || 'Unknown error'}`;
          }
          
          throw new Error(errorMessage);
        }

        // Extract tokens from URL parameters
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userId = searchParams.get('user_id');
        const expiresIn = searchParams.get('expires_in');
        const tokenType = searchParams.get('token_type') || 'bearer';

        if (!accessToken || !userId) {
          throw new Error('Missing required authentication tokens. Please try signing in again.');
        }

        console.log('✅ Found tokens in URL params - OAuth successful!');

        // Convert expires_in to number (default to 24 hours if not provided)
        const expiresInSeconds = expiresIn ? parseInt(expiresIn, 10) : 24 * 60 * 60;

        // Call AuthContext's handleOAuthCallback method
        await handleOAuthCallback({
          access_token: accessToken,
          refresh_token: refreshToken || '',
          expires_in: expiresInSeconds,
          user_id: userId,
          token_type: tokenType
        });

        setStatus('success');

        // Wait for AuthContext to process the user state
        const waitForUserState = () => {
          return new Promise<void>((resolve) => {
            const checkState = () => {
              if (!isLoading) {
                setUserStateReady(true);
                resolve();
              } else {
                setTimeout(checkState, 100);
              }
            };
            checkState();
          });
        };

        // Wait for user state to be ready, then redirect
        waitForUserState().then(() => {
          setRedirecting(true);

          setTimeout(() => {
            if (isNewUser) {
              console.log('✅ New user authenticated, redirecting to onboarding...');
              router.push('/onboarding');
            } else {
              console.log('✅ Existing user authenticated, redirecting to chat...');
              router.push('/chat');
            }
          }, 500);
        });

      } catch (err: any) {
        console.error('OAuth callback error:', err);

        // Enhanced error handling with specific error types
        let errorMessage = 'Authentication failed';
        let errorCode = 'oauth_failed';

        if (err.message?.includes('network') || err.message?.includes('fetch')) {
          errorMessage = 'Network error during authentication. Please check your connection.';
          errorCode = 'network_error';
        } else if (err.message?.includes('token')) {
          errorMessage = 'Invalid authentication token received.';
          errorCode = 'invalid_token';
        } else if (err.message?.includes('user_state')) {
          errorMessage = 'Failed to determine user onboarding status.';
          errorCode = 'user_state_error';
        }

        setError(errorMessage);
        setStatus('error');

        // Redirect to login after error with detailed error parameters
        setTimeout(() => {
          const errorParams = new URLSearchParams({
            oauth_error: errorMessage,
            error_code: errorCode,
            timestamp: new Date().toISOString()
          });
          router.push(`/login?${errorParams.toString()}`);
        }, 4000); // Longer delay to let user read the error
      }
    };

    handleCallback();
  }, []); // Empty dependency array to run only once

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          {status === 'loading' && (
            <>
              <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Completing sign in...</h2>
              <p className="text-gray-600 dark:text-gray-400 mt-2">Please wait while we authenticate you with Google.</p>
            </>
          )}
          
          {status === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Sign in successful!</h2>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                {redirecting
                  ? (isNewUser ? 'Redirecting to onboarding...' : 'Redirecting to dashboard...')
                  : 'Preparing your account...'
                }
              </p>
            </>
          )}
          
          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Authentication failed</h2>
              <p className="text-red-600 dark:text-red-400 mt-2 text-sm">{error}</p>
              <p className="text-gray-600 dark:text-gray-400 mt-2 text-sm">Redirecting to login page...</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default function OAuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Loading...</h2>
            <p className="text-gray-600 dark:text-gray-400 mt-2">Preparing OAuth callback...</p>
          </div>
        </div>
      </div>
    }>
      <OAuthCallbackContent />
    </Suspense>
  );
}
